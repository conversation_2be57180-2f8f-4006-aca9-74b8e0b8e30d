#pragma once

#include <iostream>
#include <algorithm>
#include <vector>
#include <string>
#include <unordered_map>

#include "json/json.h"

// EMSBOX
#define HARDWARE_EMSBOX_CFG_ENV	"/etc/factory-version"
#define HARDWARE_EMSBOX_CFG_NTP	"/user_part/etc/systemd/timesyncd.conf"
#define HARDWARE_IEMS_CFG_ENV	"/etc/devCfg.json"
//#define HARDWARE_IEMS_CFG_ENV	"devCfg.json"

enum class EIEMSTransferInterface
{
	Unknow,
	Ethernet,
	RS485,
	RS232
};

std::unordered_map<std::string, EIEMSTransferInterface> iemsStringToTransferInterfaceMap =
{
	{"unknow", EIEMSTransferInterface::Unknow},
	{"ethernet", EIEMSTransferInterface::Ethernet},
	{"rs485", EIEMSTransferInterface::RS485},
	{"rs232", EIEMSTransferInterface::RS232},
};

std::unordered_map<EIEMSTransferInterface, std::string> iemsTransferInterfaceToStringMap =
{
	{EIEMSTransferInterface::Unknow, "unknow"},
	{EIEMSTransferInterface::Ethernet, "ethernet"},
	{EIEMSTransferInterface::RS485, "rs485"},
	{EIEMSTransferInterface::RS232, "rs232"},
};

enum class EIEMSRunningStateLed
{
	Err,
	Run
};

std::unordered_map<EIEMSRunningStateLed, std::string> iemsRunningStateToStringMap =
{
	{EIEMSRunningStateLed::Err, "/sys/class/leds/err/brightness"},
	{EIEMSRunningStateLed::Run, "/sys/class/leds/run/brightness"}
};

struct SIEMSCOMInfo
{
	std::string com_name;
	std::string tty_channel;
	EIEMSTransferInterface type;
	std::string led;
};

struct SIEMSMaxSlave
{
	int rs232;
	int rs485;
	int tcp;
};

struct SIEMSProtocolUp 
{

};

struct SIEMSProtocolDown
{
	bool enable;
	std::unordered_map<EIEMSTransferInterface, bool> type;
};
struct SIEMSConfig
{
	std::string product;
	std::string dev_module;
	std::unordered_map<std::string, SIEMSCOMInfo> com_link_param;
	int register_max_num;
	SIEMSMaxSlave slave_max_limit;
	std::unordered_map<std::string, SIEMSProtocolDown> protocol_down;

	Json::Value toJsonValue() const
	{
		Json::Value root;
		root["product"] = product;
		root["dev_module"] = dev_module;

		std::vector<std::pair<std::string, SIEMSCOMInfo>> com_link_param_vec(com_link_param.begin(), com_link_param.end());
		std::sort(com_link_param_vec.begin(), com_link_param_vec.end(), [](const auto& a, const auto& b) {
			return a.second.com_name < b.second.com_name;
			});
		for (auto& clp : com_link_param_vec)
		{
			Json::Value cc;
			cc["com_name"] = clp.second.com_name;
			cc["tty_channel"] = clp.second.tty_channel;
			cc["type"] = iemsTransferInterfaceToStringMap[clp.second.type];
			cc["led"] = clp.second.led;
			root["com_link_param"].append(cc);
		}
		root["register_max_num"] = register_max_num;
		root["slave_max_limit"]["rs232"] = slave_max_limit.rs232;
		root["slave_max_limit"]["rs485"] = slave_max_limit.rs485;
		root["slave_max_limit"]["tcp"] = slave_max_limit.tcp;
		for (auto& pd : protocol_down)
		{
			Json::Value pc;
			pc["enable"] = pd.second.enable;
			for (auto& tit : pd.second.type)
			{
				pc["type"].append(iemsTransferInterfaceToStringMap[tit.first]);
			}
			root["protocol_down"][pd.first] = pc;
		}
		return root;
	}
};