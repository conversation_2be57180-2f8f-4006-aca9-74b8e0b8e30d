[HEADER]
magic_cookie={66E99B07-E706-4689-9E80-9B2582898A13}
file_version=1.0
device=PIC18F6720
[PATH_INFO]
BuildDirPolicy=BuildDirIsSourceDir
dir_src=
dir_bin=
dir_tmp=
dir_sin=
dir_inc=C:\code\bacnet-stack\include;C:\code\bacnet-stack\demo\object;C:\code\bacnet-stack\ports\pic18f6720
dir_lib=C:\mcc18\lib
dir_lkr=
[CAT_FILTERS]
filter_src=*.asm;*.c
filter_inc=*.h;*.inc
filter_obj=*.o
filter_lib=*.lib
filter_lkr=*.lkr
[CAT_SUBFOLDERS]
subfolder_src=
subfolder_inc=
subfolder_obj=
subfolder_lib=
subfolder_lkr=
[FILE_SUBFOLDERS]
file_000=.
file_001=.
file_002=.
file_003=.
file_004=.
file_005=.
file_006=.
file_007=.
file_008=.
file_009=.
file_010=.
file_011=.
file_012=.
file_013=.
file_014=.
file_015=.
file_016=.
file_017=.
file_018=.
file_019=.
file_020=.
file_021=.
file_022=.
file_023=.
file_024=.
file_025=.
file_026=.
file_027=.
file_028=.
file_029=.
file_030=.
file_031=.
file_032=.
file_033=.
file_034=.
file_035=.
file_036=.
file_037=.
file_038=.
file_039=.
file_040=.
file_041=.
file_042=.
file_043=.
file_044=.
file_045=.
file_046=.
file_047=.
file_048=.
file_049=.
file_050=.
file_051=.
file_052=.
file_053=.
file_054=.
file_055=.
file_056=.
file_057=.
file_058=.
[GENERATED_FILES]
file_000=no
file_001=no
file_002=no
file_003=no
file_004=no
file_005=no
file_006=no
file_007=no
file_008=no
file_009=no
file_010=no
file_011=no
file_012=no
file_013=no
file_014=no
file_015=no
file_016=no
file_017=no
file_018=no
file_019=no
file_020=no
file_021=no
file_022=no
file_023=no
file_024=no
file_025=no
file_026=no
file_027=no
file_028=no
file_029=no
file_030=no
file_031=no
file_032=no
file_033=no
file_034=no
file_035=no
file_036=no
file_037=no
file_038=no
file_039=no
file_040=no
file_041=no
file_042=no
file_043=no
file_044=no
file_045=no
file_046=no
file_047=no
file_048=no
file_049=no
file_050=no
file_051=no
file_052=no
file_053=no
file_054=no
file_055=no
file_056=no
file_057=no
file_058=no
[OTHER_FILES]
file_000=no
file_001=no
file_002=no
file_003=no
file_004=no
file_005=no
file_006=no
file_007=no
file_008=no
file_009=no
file_010=no
file_011=no
file_012=no
file_013=no
file_014=no
file_015=no
file_016=no
file_017=no
file_018=no
file_019=no
file_020=no
file_021=no
file_022=no
file_023=no
file_024=no
file_025=no
file_026=no
file_027=no
file_028=no
file_029=no
file_030=no
file_031=no
file_032=no
file_033=no
file_034=no
file_035=no
file_036=no
file_037=no
file_038=no
file_039=no
file_040=no
file_041=no
file_042=no
file_043=no
file_044=no
file_045=no
file_046=no
file_047=no
file_048=no
file_049=no
file_050=no
file_051=no
file_052=no
file_053=no
file_054=no
file_055=no
file_056=no
file_057=no
file_058=no
[FILE_INFO]
file_000=C:\code\bacnet-stack\src\abort.c
file_001=C:\code\bacnet-stack\src\bacapp.c
file_002=C:\code\bacnet-stack\src\bacdcode.c
file_003=C:\code\bacnet-stack\src\bacerror.c
file_004=C:\code\bacnet-stack\src\bacstr.c
file_005=C:\code\bacnet-stack\src\crc.c
file_006=C:\code\bacnet-stack\src\dcc.c
file_007=C:\code\bacnet-stack\src\iam.c
file_008=C:\code\bacnet-stack\src\rd.c
file_009=C:\code\bacnet-stack\src\reject.c
file_010=C:\code\bacnet-stack\src\rp.c
file_011=C:\code\bacnet-stack\src\whois.c
file_012=C:\code\bacnet-stack\demo\handler\h_dcc.c
file_013=C:\code\bacnet-stack\demo\handler\h_rd.c
file_014=main.c
file_015=dlmstp.c
file_016=device.c
file_017=rs485.c
file_018=isr.c
file_019=C:\code\bacnet-stack\src\datetime.c
file_020=C:\code\bacnet-stack\demo\handler\txbuf.c
file_021=C:\code\bacnet-stack\demo\handler\h_whois.c
file_022=mstp.c
file_023=bv.c
file_024=ai.c
file_025=bi.c
file_026=av.c
file_027=C:\code\bacnet-stack\src\wp.c
file_028=C:\code\bacnet-stack\demo\handler\h_npdu.c
file_029=C:\code\bacnet-stack\demo\handler\s_iam.c
file_030=C:\code\bacnet-stack\src\bacreal.c
file_031=C:\code\bacnet-stack\src\bacint.c
file_032=C:\code\bacnet-stack\src\npdu.c
file_033=apdu.c
file_034=C:\code\bacnet-stack\demo\handler\noserv.c
file_035=C:\code\bacnet-stack\src\fifo.c
file_036=C:\code\bacnet-stack\demo\handler\h_wp.c
file_037=C:\code\bacnet-stack\demo\handler\h_rp.c
file_038=C:\code\bacnet-stack\src\bacaddr.c
file_039=stdbool.h
file_040=stdint.h
file_041=rs485.h
file_042=mstp.h
file_043=C:\code\bacnet-stack\include\bits.h
file_044=C:\code\bacnet-stack\include\abort.h
file_045=C:\code\bacnet-stack\include\apdu.h
file_046=C:\code\bacnet-stack\include\bacaddr.h
file_047=C:\code\bacnet-stack\include\bacapp.h
file_048=C:\code\bacnet-stack\include\bacdcode.h
file_049=C:\code\bacnet-stack\include\bacdef.h
file_050=C:\code\bacnet-stack\include\bacenum.h
file_051=C:\code\bacnet-stack\include\bacerror.h
file_052=C:\code\bacnet-stack\include\bacint.h
file_053=C:\code\bacnet-stack\include\bacprop.h
file_054=C:\code\bacnet-stack\include\bacreal.h
file_055=C:\code\bacnet-stack\include\bacstr.h
file_056=C:\code\bacnet-stack\include\bigend.h
file_057=C:\code\bacnet-stack\include\config.h
file_058=18F6720.lkr
[SUITE_INFO]
suite_guid={5B7D72DD-9861-47BD-9F60-2BE967BF8416}
suite_state=
[TOOL_SETTINGS]
TS{DD2213A8-6310-47B1-8376-9430CDFC013F}=
TS{BFD27FBA-4A02-4C0E-A5E5-B812F3E7707C}=/m"$(BINDIR_)$(TARGETBASE).map" /o"$(TARGETBASE).cof"
TS{C2AF05E7-1416-4625-923D-E114DB6E2B96}=-DPRINT_ENABLED=0 -DBACDL_MSTP=1 -DMAX_APDU=50 -DMAX_TSM_TRANSACTIONS=0 -mL -Ls -Ou- -Ot- -Ob- -Op- -Or- -Od- -Opa-
TS{ADE93A55-C7C7-4D4D-A4BA-59305F7D0391}=
[INSTRUMENTED_TRACE]
enable=0
transport=0
format=0
[CUSTOM_BUILD]
Pre-Build=
Pre-BuildEnabled=1
Post-Build=
Post-BuildEnabled=1
