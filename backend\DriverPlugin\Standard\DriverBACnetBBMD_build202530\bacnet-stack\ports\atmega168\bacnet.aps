<AVRStudio><MANAGEMENT><Created>13-Aug-2007 15:08:27</Created><LastEdit>14-Nov-2008 08:40:02</LastEdit><ProjectType>0</ProjectType><Created>13-Aug-2007 15:08:27</Created><Version>4</Version><Build>4, 13, 0, 528</Build><ProjectTypeName>AVR GCC</ProjectTypeName><ICON>241</ICON><ProjectName>bacnet</ProjectName><Created>13-Aug-2007 15:11:07</Created><LastEdit>13-Aug-2007 15:11:07</LastEdit><ICON>241</ICON><ProjectType>0</ProjectType><Created>13-Aug-2007 15:11:07</Created><Version>4</Version><Build>4, 13, 0, 528</Build><ProjectTypeName>AVR GCC</ProjectTypeName></MANAGEMENT><CODE_CREATION><ObjectFile>bacnet.elf</ObjectFile><EntryFile></EntryFile><ObjectFile></ObjectFile><EntryFile></EntryFile></CODE_CREATION><DEBUG_TARGET><CURRENT_PART>ATMEGA168</CURRENT_PART><BREAKPOINTS></BREAKPOINTS><IO_EXPAND><HIDE>false</HIDE></IO_EXPAND><REGISTERNAMES><Register>R00</Register><Register>R01</Register><Register>R02</Register><Register>R03</Register><Register>R04</Register><Register>R05</Register><Register>R06</Register><Register>R07</Register><Register>R08</Register><Register>R09</Register><Register>R10</Register><Register>R11</Register><Register>R12</Register><Register>R13</Register><Register>R14</Register><Register>R15</Register><Register>R16</Register><Register>R17</Register><Register>R18</Register><Register>R19</Register><Register>R20</Register><Register>R21</Register><Register>R22</Register><Register>R23</Register><Register>R24</Register><Register>R25</Register><Register>R26</Register><Register>R27</Register><Register>R28</Register><Register>R29</Register><Register>R30</Register><Register>R31</Register></REGISTERNAMES><CURRENT_TARGET>AVR Dragon</CURRENT_TARGET><CURRENT_TARGET>AVR Simulator</CURRENT_TARGET><CURRENT_PART>ATmega168.xml</CURRENT_PART><BREAKPOINTS></BREAKPOINTS><IO_EXPAND></IO_EXPAND><REGISTERNAMES></REGISTERNAMES><COM>Auto</COM><COMType>0</COMType><WATCHNUM>0</WATCHNUM><WATCHNAMES><Pane0><Variables>property_len</Variables><Variables>object_index</Variables><Variables>Present_Value</Variables><Variables>value</Variables></Pane0><Pane1></Pane1><Pane2></Pane2><Pane3></Pane3></WATCHNAMES><BreakOnTrcaeFull>0</BreakOnTrcaeFull></DEBUG_TARGET><Debugger><modules><module></module></modules><Triggers></Triggers></Debugger><AVRGCCPLUGIN><FILES><SOURCEFILE>main.c</SOURCEFILE><SOURCEFILE>rs485.c</SOURCEFILE><SOURCEFILE>timer.c</SOURCEFILE><SOURCEFILE>dlmstp.c</SOURCEFILE><SOURCEFILE>..\..\demo\handler\txbuf.c</SOURCEFILE><SOURCEFILE>device.c</SOURCEFILE><SOURCEFILE>stack.c</SOURCEFILE><SOURCEFILE>..\..\src\crc.c</SOURCEFILE><SOURCEFILE>..\..\src\npdu.c</SOURCEFILE><SOURCEFILE>apdu.c</SOURCEFILE><SOURCEFILE>h_rp.c</SOURCEFILE><SOURCEFILE>..\..\src\iam.c</SOURCEFILE><SOURCEFILE>av.c</SOURCEFILE><SOURCEFILE>h_wp.c</SOURCEFILE><SOURCEFILE>..\..\src\bacapp.c</SOURCEFILE><SOURCEFILE>..\..\src\bacstr.c</SOURCEFILE><SOURCEFILE>bv.c</SOURCEFILE><SOURCEFILE>h_whois.c</SOURCEFILE><SOURCEFILE>..\..\src\whois.c</SOURCEFILE><HEADERFILE>avr035.h</HEADERFILE><HEADERFILE>hardware.h</HEADERFILE><HEADERFILE>rs485.h</HEADERFILE><HEADERFILE>timer.h</HEADERFILE><HEADERFILE>stack.h</HEADERFILE><HEADERFILE>..\..\include\crc.h</HEADERFILE><HEADERFILE>..\..\include\dlmstp.h</HEADERFILE><HEADERFILE>..\..\include\iam.h</HEADERFILE><HEADERFILE>..\..\include\npdu.h</HEADERFILE><HEADERFILE>..\..\include\txbuf.h</HEADERFILE><HEADERFILE>..\..\include\bacenum.h</HEADERFILE><HEADERFILE>..\..\include\bacdcode.h</HEADERFILE><HEADERFILE>..\..\include\bacapp.h</HEADERFILE><HEADERFILE>..\..\include\bacstr.h</HEADERFILE><OTHERFILE>Makefile</OTHERFILE></FILES><CONFIGS><CONFIG><NAME>default</NAME><USESEXTERNALMAKEFILE>YES</USESEXTERNALMAKEFILE><EXTERNALMAKEFILE>Makefile</EXTERNALMAKEFILE><PART>atmega168</PART><HEX>1</HEX><LIST>0</LIST><MAP>0</MAP><OUTPUTFILENAME>bacnet.elf</OUTPUTFILENAME><OUTPUTDIR>default\</OUTPUTDIR><ISDIRTY>0</ISDIRTY><OPTIONS><OPTION><FILE>..\..\crc.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>..\..\mstp.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>dlmstp.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>main.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>rs485.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>timer.c</FILE><OPTIONLIST></OPTIONLIST></OPTION></OPTIONS><INCDIRS><INCLUDE>..\..\demo\handler\</INCLUDE><INCLUDE>.\</INCLUDE><INCLUDE>..\..\</INCLUDE><INCLUDE>..\..\demo\object\</INCLUDE></INCDIRS><LIBDIRS/><LIBS/><LINKOBJECTS/><OPTIONSFORALL>-Wall -gdwarf-2  -DMAX_APDU=50  -DBACDL_MSTP  -DBIG_ENDIAN=0 -DF_CPU=7372800UL -O0 -fsigned-char</OPTIONSFORALL><LINKEROPTIONS></LINKEROPTIONS><SEGMENTS/></CONFIG></CONFIGS><LASTCONFIG>default</LASTCONFIG><USES_WINAVR>1</USES_WINAVR><GCC_LOC>C:\WinAVR-20071221rc1\bin\avr-gcc.exe</GCC_LOC><MAKE_LOC>C:\WinAVR-20071221rc1\utils\bin\make.exe</MAKE_LOC></AVRGCCPLUGIN><MANAGEMENT></MANAGEMENT><CODE_CREATION></CODE_CREATION><DEBUG_TARGET></DEBUG_TARGET><Debugger></Debugger><AVRSimulator><FuseExt>0</FuseExt><FuseHigh>28</FuseHigh><FuseLow>216</FuseLow><LockBits>193</LockBits><Frequency>7372800</Frequency><ExtSRAM>0</ExtSRAM><SimBoot>1</SimBoot><SimBootnew>1</SimBootnew></AVRSimulator><AVRDragon><DAISY_CHAIN>0</DAISY_CHAIN><DEVS_BEFORE>0</DEVS_BEFORE><DEVS_AFTER>0</DEVS_AFTER><INSTRBITS_BEFORE>0</INSTRBITS_BEFORE><INSTRBITS_AFTER>0</INSTRBITS_AFTER><BAUDRATE>19200</BAUDRATE><JTAG_FREQ>1000000</JTAG_FREQ><TIMERS_RUNNING>0</TIMERS_RUNNING><PRESERVE_EEPROM>0</PRESERVE_EEPROM><ALWAYS_EXT_RESET>0</ALWAYS_EXT_RESET><PRINT_BRK_CAUSE>1</PRINT_BRK_CAUSE><ENABLE_IDR_IN_RUN_MODE>0</ENABLE_IDR_IN_RUN_MODE><ALLOW_BRK_INSTR>1</ALLOW_BRK_INSTR><STOPIF_ENTRYFUNC_NOTFOUND>1</STOPIF_ENTRYFUNC_NOTFOUND><ENTRY_FUNCTION>main</ENTRY_FUNCTION><REPROGRAM>1</REPROGRAM></AVRDragon><IOView><usergroups/><sort sorted="0" column="0" ordername="1" orderaddress="1" ordergroup="1"/></IOView><Files><File00000><FileId>00000</FileId><FileName>main.c</FileName><Status>259</Status></File00000><File00001><FileId>00001</FileId><FileName>dlmstp.c</FileName><Status>259</Status></File00001><File00003><FileId>00003</FileId><FileName>bv.c</FileName><Status>258</Status></File00003><File00005><FileId>00005</FileId><FileName>apdu.c</FileName><Status>258</Status></File00005><File00006><FileId>00006</FileId><FileName>h_whois.c</FileName><Status>257</Status></File00006><File00007><FileId>00007</FileId><FileName>h_wp.c</FileName><Status>257</Status></File00007></Files><Events><Bookmarks></Bookmarks></Events><Trace><Filters></Filters></Trace></AVRStudio>
