---
description: 
globs: 
alwaysApply: false
---
# EdgeConfig 2.0 项目概览

这是一个基于Vue 3、TypeScript和ElementPlus的前端项目，使用Vite作为构建工具。

## 主要入口点
- [index.html](mdc:index.html) - HTML入口文件
- [src/main.ts](mdc:src/main.ts) - 主要应用程序入口点
- [src/App.vue](mdc:src/App.vue) - 根Vue组件

## 项目配置
- [package.json](mdc:package.json) - 项目依赖和脚本
- [vite.config.ts](mdc:vite.config.ts) - Vite构建配置
- [tsconfig.json](mdc:tsconfig.json) - TypeScript配置

