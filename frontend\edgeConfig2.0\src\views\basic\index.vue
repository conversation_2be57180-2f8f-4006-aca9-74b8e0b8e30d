<template>
	<div class="basic card">
		<div class="basic-info-box">
			<el-card v-if="basicInfo?.env !== 'iEMS-COM4'" class="box-card" shadow="hover">
				<template #header>
					<div class="card-header">
						<span>网关信息</span>
						<div class="btns">
							<el-button type="primary" link :icon="Link" @click="toOther(0)">跳转配置</el-button>
						</div>
					</div>
				</template>
				<el-descriptions :column="1">
					<el-descriptions-item label-class-name="label" label="网关编码">
						{{ basicInfo?.id }}
					</el-descriptions-item>
					<el-descriptions-item label-class-name="label" label="网关名称">
						{{ basicInfo?.name }}
					</el-descriptions-item>
					<el-descriptions-item label-class-name="label">
						<template #label>云端地址</template>
						<div class="item-status">
							<span class="ip" :title="handleAddr(cloudInfo)">{{ handleAddr(cloudInfo) }}</span>
							<span class="status yes" v-if="cloudInfo?.connectStatus">
								<el-icon>
									<SuccessFilled />
								</el-icon>
								<span>已连接</span>
							</span>
							<span class="status no" v-else>
								<el-icon>
									<CircleCloseFilled />
								</el-icon>
								<span>已断开</span>
							</span>
						</div>
					</el-descriptions-item>
					<el-descriptions-item label-class-name="label" v-for="(item, inx) in cloudInfo?.address" :key="inx">
						<template #label> 转发地址{{ inx + 1 }}</template>
						<div class="item-status">
							<span class="ip" :title="handleAddr(item)">{{ handleAddr(item) }}</span>
							<span class="status yes" v-if="item.connectStatus">
								<el-icon>
									<SuccessFilled />
								</el-icon>
								<span>已连接</span>
							</span>
							<span class="status no" v-else>
								<el-icon>
									<CircleCloseFilled />
								</el-icon>
								<span>已断开</span>
							</span>
						</div>
					</el-descriptions-item>
					<el-descriptions-item label-class-name="label" label="云端时间">{{ cloudInfo?.cloudTime
						}}</el-descriptions-item>
					<el-descriptions-item label-class-name="label" label="本地时间">{{ timeInfo?.localTime
						}}</el-descriptions-item>
				</el-descriptions>
			</el-card>
			<el-card class="box-card" shadow="hover" v-if="authStore.enableChange">
				<template #header>
					<div class="card-header">
						<span>连接信息</span>
						<div class="btns">
							<el-button type="primary" link :icon="Link" @click="toOther(1)">跳转配置</el-button>
						</div>
					</div>
				</template>
				<el-descriptions :column="1">
					<el-descriptions-item v-for="(item, inx) in connectList" label-class-name="label">
						<template #label>
							<div class="icon-label">
								<SvgIcon icon-class="svg-icon" :name="netWorkType[item.type]" />
								<span>{{ item.name }}</span>
							</div>
						</template>
						<div class="item-status">
							<span class="ip">{{ item.ipv4 }}</span>
							<span class="status yes" v-if="item.linkDetected">
								<el-icon>
									<SuccessFilled />
								</el-icon>
								<span>已连接</span>
							</span>
							<span class="status no" v-else>
								<el-icon>
									<CircleCloseFilled />
								</el-icon>
								<span>已断开</span>
							</span>
						</div>
					</el-descriptions-item>
				</el-descriptions>
			</el-card>
			<el-card class="box-card" shadow="hover">
				<template #header>
					<div class="card-header">
						<span>运行信息</span>
					</div>
				</template>
				<el-descriptions :column="1">
					<el-descriptions-item label-class-name="label" label="CPU占用">
						<div class="progress-box">
							<el-progress :stroke-width="10" :percentage="CPUUsed" :color="customColors" />
						</div>
					</el-descriptions-item>
				</el-descriptions>
				<el-descriptions :column="1">
					<el-descriptions-item label-class-name="label" label="内存占用">
						<div class="progress-box">
							<el-progress :stroke-width="10" :percentage="memoryUsed" :color="customColors"
								:format="formatMemory" />
						</div>
					</el-descriptions-item>
				</el-descriptions>
				<el-descriptions :column="1">
					<el-descriptions-item label-class-name="label" label="磁盘空间占用">
						<div class="progress-box">
							<el-progress :stroke-width="10" :percentage="diskUsed" :color="customColors"
								:format="formatDisk" />
						</div>
					</el-descriptions-item>
				</el-descriptions>
			</el-card>
			<el-card class="box-card" shadow="hover" v-if="basicInfo?.env !== 'iEMS-COM4'">
				<template #header>
					<div class="card-header">
						<span>网关设置</span>
						<div class="btns">
							<el-button type="primary" link :icon="Link" @click="toOther(2)">跳转配置</el-button>
						</div>
					</div>
				</template>
				<el-descriptions :column="1">
					<el-descriptions-item label-class-name="label" label="断线缓存">
						<div class="item-status">
							<span class="status yes" v-if="disConnCache">
								<el-icon>
									<SuccessFilled />
								</el-icon>
								<span>已启用</span>
							</span>
							<span class="status no" v-else>
								<el-icon>
									<CircleCloseFilled />
								</el-icon>
								<span>未启用</span>
							</span>
						</div>
					</el-descriptions-item>
					<el-descriptions-item label-class-name="label" label="云端控制">
						<div class="item-status">
							<span class="status yes" v-if="cloudControlAuth">
								<el-icon>
									<SuccessFilled />
								</el-icon>
								<span>已启用</span>
							</span>
							<span class="status no" v-else>
								<el-icon>
									<CircleCloseFilled />
								</el-icon>
								<span>未启用</span>
							</span>
						</div>
					</el-descriptions-item>
				</el-descriptions>
			</el-card>
			<el-card class="box-card" shadow="hover" v-if="false">
				<template #header>
					<div class="card-header">
						<span>固件信息</span>
					</div>
				</template>
				<el-descriptions :column="1" class="firmware-info">
					<el-descriptions-item class="aaa" label-class-name="label" label="运行版本号">
						<div class="firmware-no">
							<span class="no">{{ basicInfo?.version }}</span>
							<el-button type="success" :loading="checkLoading" @click="checkVersion">检查更新</el-button>
						</div>
					</el-descriptions-item>
					<el-descriptions-item label-class-name="label" label="云端版本号">
						<div class="firmware-no">
							<span class="no" :class="{ 'update-no': diffVersion }">{{ cloudVersion }}</span>
							<el-button class="update" type="primary" v-if="diffVersion" :loading="updateLoading"
								@click="updateFirmware">升
								级</el-button>
						</div>
					</el-descriptions-item>
				</el-descriptions>
			</el-card>
		</div>
	</div>
</template>

<script setup lang="ts" name="basic">
import { ref, reactive, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import SvgIcon from "@/components/SvgIcon/index.vue";
import { ElMessage, ElMessageBox } from "element-plus";

import { AuthStore } from "@/stores/modules/auth";
import { NetWorkConfig, GatewayBase } from "@/api/interface";
import { netWorkType } from "@/utils/serviceDict";
import { getBasicInfo, getCloudInfo, getGatewayRuntimeInfo, getCloudFirmwareInfo, getOtherConfigInfo, getLocalTimeInfo, updateGateway } from "@/api/modules/basic";
import { handleIp, handleMqtt } from "@/utils/util";
import { showFullScreenLoading, tryHideFullScreenLoading } from "@/config/serviceLoading";
import { Link } from "@element-plus/icons-vue";

const authStore = AuthStore();
const basicInfo = ref<GatewayBase.GatewayBaseInfo>();
const cloudInfo = ref<GatewayBase.GatewayCloudInfo>();

const connectList = ref<NetWorkConfig[]>();

const runInfo = reactive<Partial<Omit<GatewayBase.GatewayRuntimeInfo, "netWorkConfigList">>>({})

const timeInfo = ref<GatewayBase.GatewayTimeInfo>();

const customColors = [
	{ color: "#409eff", percentage: 50 },
	{ color: "#e6a23c", percentage: 75 },
	{ color: "#f56c6c", percentage: 100 },
];

const handleAddr = (item: GatewayBase.GatewayCloudInfo | GatewayBase.CloudConfig | undefined): string => {
	if (!item) {
		return "";
	}
	let result = "",
		httpAddr = handleIp(item.resoureIP),
		mqttAddrObj = handleMqtt(item.mqttURL);
	if (httpAddr == mqttAddrObj.ip) {
		result = `${mqttAddrObj.ip}:${mqttAddrObj.port}`;
	} else {
		result = `${mqttAddrObj.ip}:${mqttAddrObj.port}/${item.resoureIP}`;
	}
	return result;
}

const router = useRouter();
const toOther = (flag: number) => {
	switch (flag) {
		case 0:
		case 2:
			router.push({ name: "gateway" });
			break;
		case 1:
			router.push({ path: "/netport" });
			break;
		default:
			break;
	}
}

const CPUUsed = computed(() => {
	return runInfo.cpuInfo?.usage;
})

const memoryUsed = computed(() => {
	let used = runInfo.memoryInfo?.memUsed, total = runInfo.memoryInfo?.memTotal;
	if (used && total) {
		return Number((used / total * 100).toFixed(2));
	}
	else {
		return 0;
	}
})

const diskUsed = computed(() => {
	let used = runInfo.storageInfo?.used, total = runInfo.storageInfo?.capacity;
	if (used && total) {
		return Number((used / total * 100).toFixed(2));
	}
	else {
		return 0;
	}
})

const formatMemory = (_percentage: any) => {
	let used = runInfo.memoryInfo?.memUsed, total = runInfo.memoryInfo?.memTotal;
	if (used && total) {
		// 格式化used部分 - 根据数量级选择合适的单位
		const formatSize = (sizeInKB: number): string => {
			const sizeInMB: number = sizeInKB / 1024;
			const sizeInGB: number = sizeInMB / 1024;
			// 如果GB值小于1或者小数位数超过2位，使用MB
			if (sizeInGB < 1) {
				return `${sizeInMB.toFixed(2)}M`;
			} else {
				return `${sizeInGB.toFixed(2)}G`;
			}
		};
		
		const usedFormatted = formatSize(used);
		const totalFormatted = `${(total / 1024 / 1024).toFixed(2)}G`;
		
		return `${usedFormatted}/${totalFormatted}`;
	}
	else {
		return `未获取`;
	}

};

const formatDisk = (_percentage: any) => {
	let used = runInfo.storageInfo?.used, total = runInfo.storageInfo?.capacity;
	if (used && total) {
		const formatSize = (sizeInKB: number): string => {
			const sizeInMB: number = sizeInKB / 1024;
			const sizeInGB: number = sizeInMB / 1024;
			// 如果GB值小于1或者小数位数超过2位，使用MB
			if (sizeInGB < 1) {
				return `${sizeInMB.toFixed(2)}M`;
			} else {
				return `${sizeInGB.toFixed(2)}G`;
			}
		};
		const usedFormatted = formatSize(used);
		const totalFormatted = `${(total / 1024 / 1024).toFixed(2)}G`;
		return `${usedFormatted}/${totalFormatted}`;
	}
	else {
		return `未获取`;
	}
};

const cloudControlAuth = ref<boolean>();
const disConnCache = ref<boolean>();
const cloudVersion = ref("");
const diffVersion = computed(() => {
	return cloudVersion.value && basicInfo.value?.version != cloudVersion.value;
})

const getBase = async () => {
	try {
		let res = await getBasicInfo();
		if (res.result && res.result.resultCode == '0') {
			basicInfo.value = res.data;
		}
	} catch (error) {
		console.warn(error)
	}
}

const getCloud = async () => {
	try {
		let res = await getCloudInfo();
		if (res.result && res.result.resultCode == '0') {
			cloudInfo.value = res.data;
		}
	} catch (error) {
		console.warn(error)
	}
}

const getRuntime = async () => {
	try {
		let res = await getGatewayRuntimeInfo();
		if (res.result && res.result.resultCode == '0') {
			connectList.value = res.data.netWorkConfigList;
			runInfo.cpuInfo = res.data.cpuInfo;
			runInfo.memoryInfo = res.data.memoryInfo;
			runInfo.storageInfo = res.data.storageInfo;
		}
	} catch (error) {
		console.warn(error)
	}
}

const getOther = async () => {
	try {
		let res = await getOtherConfigInfo();
		if (res.result && res.result.resultCode == '0') {
			disConnCache.value = res.data.disConnCache;
			cloudControlAuth.value = res.data.cloudControlAuth;
		}
	} catch (error) {
		console.warn(error)
	}
}

const getCloudFirmware = async () => {
	try {
		let res = await getCloudFirmwareInfo();
		if (res.result && res.result.resultCode == '0') {
			cloudVersion.value = res.data.version;
		} else {
			cloudVersion.value = "";
		}
	} catch (error) {
		console.warn(error)
	}
}

const getTimeInfo = async () => {
	try {
		let res = await getLocalTimeInfo();
		if (res.result && res.result.resultCode == '0') {
			timeInfo.value = res.data;
		}
	} catch (error) {
		console.warn(error)
	}
}

const checkLoading = ref(false);

const checkVersion = async () => {
	checkLoading.value = true;
	await getCloudFirmware();
	setTimeout(() => {
		checkLoading.value = false;
		ElMessage({
			type: "success",
			message: diffVersion.value ? "发现新版本！" : "当前已是最新版本！"
		});
	}, 1000)
}

const updateLoading = ref(false);

const updateFirmware = () => {
	updateLoading.value = true;
	ElMessageBox.prompt("填写“升级”确认升级固件", "请注意", {
		showClose: false,
		closeOnClickModal: false,
		confirmButtonText: "确认升级",
		cancelButtonText: "取消",
		inputValidator: value => {
			if (value == "升级") {
				return true;
			} else {
				return false;
			}
		},
		inputErrorMessage: "输入错误，请输入“升级”二字"
	})
		.then(({ value }) => {
			ElMessage({
				type: "warning",
				message: `预计升级一分钟，升级中请勿断开电源，请等待后刷新！`
			});
			showFullScreenLoading();
			let param = `http://${cloudInfo.value?.resoureIP}/supaiot/api/edge/${basicInfo.value?.product}/file_list.xml`;

			updateGateway(param);
			setTimeout(() => {
				updateLoading.value = false;
				ElMessage({
					type: "success",
					message: `升级成功，请刷新页面`
				});
				tryHideFullScreenLoading();
				window.location.reload();
			}, 60000);
		})
		.catch(() => {
			updateLoading.value = false;
			ElMessage({
				type: "info",
				message: "取消升级"
			});
		});
};

onMounted(async () => {
	await getBase();
	getCloud();
	getRuntime();
	getOther();
	// basicInfo.value?.env != 'normal' && getCloudFirmware();
	getTimeInfo();
})
</script>

<style scoped lang="scss">
.basic {
	display: flex;
	align-items: flex-start;
	justify-content: flex-start;
	width: 100%;

	.basic-info-box {
		width: 750px;
		margin: 0 auto;

		.box-card {
			margin-bottom: 20px;

			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	.el-card {
		:deep(.el-card__header) {
			background-color: #f7f7f7;
		}

		.card-header {
			position: relative;
		}

		.btns {
			display: flex;
			justify-content: center;
			position: absolute;
			right: 10px;
			top: 0;

			button+button {
				margin-left: 20px;
			}
		}

		:deep(.label) {
			display: inline-block;
			width: 120px;
			letter-spacing: 2px;
		}

		.icon-label {
			display: inline-block;
			padding-left: 27px;
			position: relative;

			.svg-icon {
				width: 23px;
				height: 23px;
				margin-right: 4px;
				position: absolute;
				left: 0;
				top: 0;
			}
		}

		.item-status {
			display: inline-flex;
			justify-content: space-between;
			width: 370px;

			.ip {
				margin-right: 20px;
				width: 290px;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			.status {
				display: flex;
				line-height: 14px;
				padding-top: 5px;

				span {
					margin-left: 4px;
				}
			}

			.yes {
				color: #67c23a;
			}

			.no {
				color: #f56c6c;
			}
		}

		.progress-box {
			display: inline-block;
			width: 500px;

			:deep(.el-progress__text) {
				min-width: 120px;
			}
		}

		.firmware-info {
			:deep(.el-descriptions__cell) {
				line-height: 32px;
			}

			.firmware-no {
				display: inline-flex;
				justify-content: space-between;
				width: 280px;

				.update-no {
					color: var(--el-color-primary);
				}

				.update {
					width: 88px;
				}
			}
		}
	}
}
</style>
