#pragma once
#include <mutex>
#include <list>
#include <functional>

#include "DriverChannel.h"

namespace DRIVER
{
	class IEngineCallback
	{
	public:
		IEngineCallback() {}
		virtual ~IEngineCallback() {}

		virtual void onEngineStatus(const std::string& channelName, const SStatus& status) = 0;

		virtual void onEngineOnline(const std::string& channelName, bool online) = 0;

		virtual void onEngineData(const std::string& channelName, ChannelDataSetSptr dataSetSptr) = 0;

		virtual void onEngineHistoryData(const std::string& channelName, ChannelHistoryDataSetSptr dataSetSptr) = 0;

		virtual void onEngineEvent(const std::string& channelName, const std::string& deviceName, SEventSptr eventSptr) = 0;

		virtual void onEngineControlFeedback(const std::string& channelName, const SControlFeedback& feedback) = 0;
		
		virtual void onEngineUpdateDeviceOffline(const std::string& channelName, bool isOffline) = 0;

		virtual void onEngineUpdateDeviceScanoff(const std::string& channelName, std::string& deviceName, bool scanoff) = 0;

		virtual void onEngineUpdateDeviceInhibit(const std::string& channelName, std::string& deviceName, bool inhibit) = 0;
		
		virtual void onEngineIEMSLedBrightness(const std::string& deviceName, int brightness) = 0;
	};

	struct SEngineConfig
	{
		std::string logFile;
	};

	struct SCtrlInfo
	{
		enum EType
		{
			eTLSM,
			eKZYZ,
			eJDGP,
			eWXGP,
			eOfflinePoint,
			eOnlinePoint,
			emanset,
			eWrite
		};

		bool async;
		std::string source;
		int sequence;
		EType type;
		int timeout;
		std::string channelName;
		std::string deviceName;
		std::string pointName;
		SValue value;
		std::string raw;
		SCtrlInfo() : async(true), sequence(1) {}
	};

	class CDriverEngine : public IChannelCallback
	{
	private:
		std::shared_ptr<spdlog::logger> logger_;
		std::atomic<bool> running_;
		std::thread* thread_;

		std::mutex mapDriverMutex_;
		std::unordered_map<std::string, CDriverChannelSptr> mapDriver_;//key is driver name
		IEngineCallback* cb_;
		std::mutex controlCBMutex_;

		void loop();

		CDriverChannelSptr getDriver(const std::string& driverName);
		CDriverChannelSptr getDriverByDeviceName(const std::string& deviceName);

	public:
		CDriverEngine();
		~CDriverEngine();
		CDriverEngine(const CDriverEngine&) = delete;   //forbid copy construct
		CDriverEngine& operator = (const CDriverEngine&) = delete;  //forbid copy assigment construct
		CDriverEngine(const CDriverEngine&&) = delete;   //forbid move construct
		CDriverEngine& operator = (const CDriverEngine&&) = delete;  //forbid move assigment construct

		void setCallback(IEngineCallback* cb);
		bool configure(const SEngineConfig& cfg);
		bool run();
		bool stop();
		bool addDriver(const std::string& driverName, const SChannelConfig& cfg);
		void deleteDriver(const std::string& driverName);
		void deleteAllDriver();
		bool hasDriver(const std::string& driverName);
		bool getDriverOpenStatus(const std::string& driverName);
		int getDriverConnectQuality(const std::string& driverName);
		void getChannelNames(std::vector<std::string>& channelNames);
		bool getBufferedData(const std::string& channelName, ChannelDataSet& dataSet);
		bool getBufferedData(const std::string& channelName, const std::string& deviceName, DeviceDataSet& dataSet, bool isWeb = false);
		bool getBufferedData(const std::string& channelName, const std::string& deviceName, const std::string& pointName, SData& data);
		bool control(const std::vector<SCtrlInfo>& ctrlInfos);
		bool controlSet(const std::string& channelName, const std::unordered_map<std::string, std::unordered_map<DRIVER::SCtrlInfo::EType, std::vector<DRIVER::SCtrlInfo>>>& ctrlInfos);
		bool convertSingleControl(const SCtrlInfo& ctrlInfo);
		bool convertBatchControl(const std::vector<SCtrlInfo>& ctrlInfos);
		bool getDeviceOnlineStatus(const std::string& channelName, ChannelDataSet& dataSet);
		bool getChannelOnline(const std::string& channelName, ChannelDataSet& dataSet);
		bool sendAllData(const std::string& channelName);

		// IChannelCallback
		virtual void onChannelStatus(const std::string& channelName, const SStatus& status);
		virtual void onChannelOnline(const std::string& channelName, bool online);
		virtual void onChannelData(const std::string& channelName, ChannelDataSetSptr dataSetSptr);
		virtual void onChannelHistoryData(const std::string& channelName, ChannelHistoryDataSetSptr dataSetSptr);
		virtual void onChannelEvent(const std::string& channelName, const std::string& deviceName, SEventSptr eventSptr);
		virtual void onChannelControlFeedback(const std::string& channelName, const SControlFeedback& feedback);
		virtual void onChannelUpdateDeviceOffline(const std::string& channelName, bool isOffline);
		virtual void onChannelUpdateDeviceScanoff(const std::string& channelName, std::string& deviceName, bool scanoff);
		virtual void onChannelUpdateDeviceInhibit(const std::string& channelName, std::string& deviceName, bool inhibit);
		virtual void onChannelIEMSLedBrightness(const std::string& deviceName, int brightness) override;
	};

}//namespace DRIVER