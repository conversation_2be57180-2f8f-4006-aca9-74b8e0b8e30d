; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; http://docs.platformio.org/page/projectconf.html

[env:esp32thing]
platform = espressif32
board = esp32thing
framework = espidf

upload_port = COM7
upload_speed = 1152000

monitor_baud = 115200