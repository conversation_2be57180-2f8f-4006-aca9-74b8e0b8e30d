#include "DriverEMSBoxModbusTcp_build202528.h"

namespace DRIVER
{
	CDriverEMSBoxModbusTcp_build202528::CDriverEMSBoxModbusTcp_build202528()
	{
	}

	CDriverEMSBoxModbusTcp_build202528::~CDriverEMSBoxModbusTcp_build202528()
	{
	}
}

extern "C"
{
	DRIVER_API DRIVER::IDriver* createDriver()
	{
		return new DRIVER::CDriverEMSBoxModbusTcp_build202528;
	}

	DRIVER_API void destoryDriver(DRIVER::IDriver* p)
	{
		if (p) { delete p; }
	}
}