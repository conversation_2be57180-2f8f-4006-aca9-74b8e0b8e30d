﻿# CMakeList.txt : CMake project for DriverX's plugin called Driver<PERSON><PERSON>645, include source and define
# project specific logic here.
#

project (DriverEMSBoxDLT645_build202528 VERSION 1.0)

set(LIBRARY_OUTPUT_PATH ${OUTPUT_DIR}/Driver)
set(CMAKE_BINARY_DIR ${BUILD_DIR}/DriverEMSBoxDLT645_build202528)

set(CMAKE_POSITION_INDEPENDENT_CODE ON)

if(WIN32)
    add_definitions(-DCOMM_DLL)
elseif(UNIX)
	find_package(Threads)
endif()

include_directories(
	${PROJ_ROOT_DIR}
	${COMMUNICATE_DIR}
	${UTILS_DIR}
)

link_directories(
	${OUTPUT_DIR}
	${OPENSSL_DIR}/lib/${PLATFORM}
    ${MQTT_DIR}/lib/${PLATFORM}
    ${ZLIB_DIR}/lib/${PLATFORM}
)
#to-do : remove deps of deps
link_libraries(
	Communicate
)

aux_source_directory(./ SRCS)

# Add source to this project's library.
add_library (DriverEMSBoxDLT645_build202528 SHARED ${SRCS})

# link library
if(UNIX)
target_link_libraries (DriverEMSBoxDLT645_build202528 ${CMAKE_THREAD_LIBS_INIT})
endif()

# TODO: Add tests and install targets if needed.
