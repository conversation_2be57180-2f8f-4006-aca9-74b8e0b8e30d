---
description: 
globs: 
alwaysApply: false
---
# 组件库

本项目包含多个自定义可复用组件，与ElementPlus组件库结合使用。

## 通用组件
- **ErrorMessage/** - 错误消息显示组件
- **Loading/** - 加载状态组件
- **SvgIcon/** - SVG图标组件

## 表单与数据组件
- **ProTable/** - 增强型表格组件
- **Grid/** - 网格布局组件
- **SearchForm/** - 搜索表单组件
- **SelectFilter/** - 选择过滤器组件
- **TreeFilter/** - 树形过滤器组件

## 功能组件
- **Upload/** - 文件上传组件
- **ImportExcel/** - Excel导入组件
- **SelectIcon/** - 图标选择组件

## 使用示例
这些组件遵循Vue组件使用规范，例如：

```vue
<template>
  <SvgIcon name="icon-name" />
  <ProTable :columns="columns" :request-api="api" />
</template>
```

