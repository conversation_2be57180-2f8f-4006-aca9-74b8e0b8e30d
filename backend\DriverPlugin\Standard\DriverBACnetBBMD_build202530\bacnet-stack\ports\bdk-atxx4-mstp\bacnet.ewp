<?xml version="1.0" encoding="iso-8859-1"?>

<project>
  <fileVersion>2</fileVersion>
  <configuration>
    <name>Debug</name>
    <toolchain>
      <name>AVR</name>
    </toolchain>
    <debug>1</debug>
    <settings>
      <name>General</name>
      <archiveVersion>12</archiveVersion>
      <data>
        <version>10</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>GGEnhancedCore</name>
          <state>1</state>
        </option>
        <option>
          <name>Variant Memory</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>ExePath</name>
          <state>Debug\Exe</state>
        </option>
        <option>
          <name>ObjPath</name>
          <state>Debug\Obj</state>
        </option>
        <option>
          <name>ListPath</name>
          <state>Debug\List</state>
        </option>
        <option>
          <name>GGEnableConfig</name>
          <state>1</state>
        </option>
        <option>
          <name>GG64KFlash</name>
          <state>0</state>
        </option>
        <option>
          <name>GG64BitDoubles</name>
          <state>0</state>
        </option>
        <option>
          <name>GGFPSLICCOnfig</name>
          <version>0</version>
          <state>3</state>
        </option>
        <option>
          <name>LCEnableBitDefs</name>
          <state>1</state>
        </option>
        <option>
          <name>LCHeapSize</name>
          <state>0x10</state>
        </option>
        <option>
          <name>SCCStackSize</name>
          <state>0x400</state>
        </option>
        <option>
          <name>SCExtCStack</name>
          <state>0</state>
        </option>
        <option>
          <name>SCRStackSize</name>
          <state>32</state>
        </option>
        <option>
          <name>SCExtRStack</name>
          <state>0</state>
        </option>
        <option>
          <name>SCEnableBus</name>
          <state>0</state>
        </option>
        <option>
          <name>SCAddWaitstate</name>
          <state>0</state>
        </option>
        <option>
          <name>SCRamBase</name>
          <state>0x0</state>
        </option>
        <option>
          <name>SCRamSize</name>
          <state>0x0</state>
        </option>
        <option>
          <name>SCRomBase</name>
          <state>0x0</state>
        </option>
        <option>
          <name>SCRomSize</name>
          <state>0x0</state>
        </option>
        <option>
          <name>SCNVBase</name>
          <state>0x0</state>
        </option>
        <option>
          <name>SCNVSize</name>
          <state>0x0</state>
        </option>
        <option>
          <name>SCInitWithReti</name>
          <state>1</state>
        </option>
        <option>
          <name>GOutputBinary</name>
          <state>0</state>
        </option>
        <option>
          <name>GGEepromUtil</name>
          <state>1</state>
        </option>
        <option>
          <name>GGEepromUtilSize</name>
          <state>4096</state>
        </option>
        <option>
          <name>GRuntimeLibSelect</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>RTDescription</name>
          <state>Use the normal configuration of the C/EC++</state>
          <state>runtime library. No locale interface,</state>
          <state>C locale, no file descriptor support,</state>
          <state>no multibytes in printf and scanf, and</state>
          <state>no hex floats in strtod.</state>
        </option>
        <option>
          <name>RTConfigPath</name>
          <state>$TOOLKIT_DIR$\LIB\DLIB\dlAVR-3s-ec_mul-n.h</state>
        </option>
        <option>
          <name>RTLibraryPath</name>
          <state>$TOOLKIT_DIR$\LIB\DLIB\dlAVR-3s-ec_mul-n.r90</state>
        </option>
        <option>
          <name>Input variant</name>
          <version>36</version>
          <state>6</state>
        </option>
        <option>
          <name>Input description</name>
          <state>No specifier n, no float nor long long, no scan set, no assignment suppressing.</state>
        </option>
        <option>
          <name>Output variant</name>
          <version>36</version>
          <state>6</state>
        </option>
        <option>
          <name>Output description</name>
          <state>No specifier a, A, no specifier n, no float nor long long.</state>
        </option>
        <option>
          <name>GRuntimeLibSelectSlave</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>GeneralEnableMisra</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraVerbose</name>
          <state>0</state>
        </option>
        <option>
          <name>LCTinyHeapSize</name>
          <state>0x10</state>
        </option>
        <option>
          <name>LCNearHeapSize</name>
          <state>0x20</state>
        </option>
        <option>
          <name>LCFarHeapSize</name>
          <state>0x1000</state>
        </option>
        <option>
          <name>LCHugeHeapSize</name>
          <state>0x1000</state>
        </option>
        <option>
          <name>LCsHeapConfigText</name>
          <state>1</state>
        </option>
        <option>
          <name>GGNoMULInstruction</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraVer</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraRules04</name>
          <version>0</version>
          <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
        </option>
        <option>
          <name>GeneralMisraRules98</name>
          <version>0</version>
          <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
        </option>
        <option>
          <name>GenDeviceSelectMenu</name>
          <state>m1284p	ATmega1284P</state>
        </option>
        <option>
          <name>OGProductVersion</name>
          <state>6.80.1.1057</state>
        </option>
        <option>
          <name>OGLastSavedByProductVersion</name>
          <state>6.80.1.1057</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>ICCAVR</name>
      <archiveVersion>6</archiveVersion>
      <data>
        <version>17</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CCVariantProcessor</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEnhancedCore</name>
          <state>0</state>
        </option>
        <option>
          <name>CCVariantMemory</name>
          <state>0</state>
        </option>
        <option>
          <name>CCObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>CCDefines</name>
          <state>BACDL_MSTP</state>
          <state>MAX_ANALOG_INPUTS=100</state>
          <state>MAX_APDU=50</state>
          <state>MAX_TSM_TRANSACTIONS=0</state>
          <state>MSTP_PDU_PACKET_COUNT=2</state>
          <state>MAX_CHARACTER_STRING_BYTES=64</state>
          <state>MAX_OCTET_STRING_BYTES=64</state>
          <state>BACAPP_BOOLEAN</state>
          <state>BACAPP_REAL</state>
          <state>BACAPP_OBJECT_ID</state>
          <state>BACAPP_UNSIGNED</state>
          <state>BACAPP_ENUMERATED</state>
          <state>BACAPP_CHARACTER_STRING</state>
          <state>WRITE_PROPERTY</state>
        </option>
        <option>
          <name>CCPreprocFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocComments</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCMnemonics</name>
          <state>1</state>
        </option>
        <option>
          <name>CCListCMessages</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListAssFile</name>
          <state>1</state>
        </option>
        <option>
          <name>CCListAssSource</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEnableRemarks</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDiagSuppress</name>
          <state></state>
        </option>
        <option>
          <name>CCDiagRemark</name>
          <state></state>
        </option>
        <option>
          <name>CCDiagWarning</name>
          <state></state>
        </option>
        <option>
          <name>CCDiagError</name>
          <state></state>
        </option>
        <option>
          <name>CCWarnAsError</name>
          <state>0</state>
        </option>
        <option>
          <name>CCConstInRAM</name>
          <state>1</state>
        </option>
        <option>
          <name>CCInitInFlash</name>
          <state>1</state>
        </option>
        <option>
          <name>CCForceVariables</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOldCallConv</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLockRegs</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCAllowList</name>
          <version>3</version>
          <state>000000</state>
        </option>
        <option>
          <name>CCCrossCallPassesList</name>
          <version>8</version>
          <state>5</state>
        </option>
        <option>
          <name>CCObjUseModuleName</name>
          <state>0</state>
        </option>
        <option>
          <name>CCObjModuleName</name>
          <state></state>
        </option>
        <option>
          <name>CCDebugInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCNoErrorMsg</name>
          <state>0</state>
        </option>
        <option>
          <name>CC64BitDoubles</name>
          <state>0</state>
        </option>
        <option>
          <name>CC64KFlash</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEnableExtBus</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEnableBitDefs</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptForceCrossCall</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCharIs</name>
          <state>1</state>
        </option>
        <option>
          <name>CCExt</name>
          <state>0</state>
        </option>
        <option>
          <name>IExtraOptions</name>
          <state></state>
        </option>
        <option>
          <name>IExtraOptionsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMultibyteSupport</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRequirePrototypes</name>
          <state>1</state>
        </option>
        <option>
          <name>CCCompilerRuntimeInfo</name>
          <state>0</state>
        </option>
        <option>
          <name>newCCIncludePaths</name>
          <state>$PROJ_DIR$</state>
          <state>$PROJ_DIR$\..\..\src</state>
        </option>
        <option>
          <name>CCStdIncCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEepromSize</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLockRegsSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>CCOutputFile</name>
          <state>$FILE_BNAME$.r90</state>
        </option>
        <option>
          <name>CompilerMisraOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLibConfigHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>PreInclude</name>
          <state></state>
        </option>
        <option>
          <name>CCOverrideModuleTypeDefault</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRadioModuleType</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRadioModuleTypeSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>OCCAdditionalCommandLineOptionsSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>CompilerMisraRules04</name>
          <version>0</version>
          <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
        </option>
        <option>
          <name>CompilerMisraRules98</name>
          <version>0</version>
          <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
        </option>
        <option>
          <name>IccLang</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCDialect</name>
          <state>1</state>
        </option>
        <option>
          <name>IccCppDialect</name>
          <state>1</state>
        </option>
        <option>
          <name>IccAllowVLA</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCppInlineSemantics</name>
          <state>0</state>
        </option>
        <option>
          <name>IccStaticDestr</name>
          <state>1</state>
        </option>
        <option>
          <name>IccFloatSemantics</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptStrategy</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CCOptLevel</name>
          <state>3</state>
        </option>
        <option>
          <name>CCOptLevelSlave</name>
          <state>3</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>AAVR</name>
      <archiveVersion>5</archiveVersion>
      <data>
        <version>11</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>IProcessor</name>
          <state>0</state>
        </option>
        <option>
          <name>AObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>ACaseSensitivity</name>
          <state>1</state>
        </option>
        <option>
          <name>AWarnEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>AWarnWhat</name>
          <state>0</state>
        </option>
        <option>
          <name>AWarnOne</name>
          <state></state>
        </option>
        <option>
          <name>AWarnRange1</name>
          <state></state>
        </option>
        <option>
          <name>AWarnRange2</name>
          <state></state>
        </option>
        <option>
          <name>CDebug</name>
          <state>1</state>
        </option>
        <option>
          <name>ADefines</name>
          <state></state>
        </option>
        <option>
          <name>MacroChars</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>UndefAsm</name>
          <state>1</state>
        </option>
        <option>
          <name>UndefFile</name>
          <state>1</state>
        </option>
        <option>
          <name>UndefLine</name>
          <state>1</state>
        </option>
        <option>
          <name>UndefTime</name>
          <state>1</state>
        </option>
        <option>
          <name>UndefDate</name>
          <state>1</state>
        </option>
        <option>
          <name>UndefTid</name>
          <state>1</state>
        </option>
        <option>
          <name>AList</name>
          <state>0</state>
        </option>
        <option>
          <name>AListHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>AListing</name>
          <state>1</state>
        </option>
        <option>
          <name>Includes</name>
          <state>0</state>
        </option>
        <option>
          <name>MacDefs</name>
          <state>0</state>
        </option>
        <option>
          <name>MacExps</name>
          <state>1</state>
        </option>
        <option>
          <name>MacExec</name>
          <state>0</state>
        </option>
        <option>
          <name>OnlyAssed</name>
          <state>0</state>
        </option>
        <option>
          <name>MultiLine</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLengthCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLength</name>
          <state>80</state>
        </option>
        <option>
          <name>TabSpacing</name>
          <state>8</state>
        </option>
        <option>
          <name>AXRef</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDefines</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefInternal</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDual</name>
          <state>0</state>
        </option>
        <option>
          <name>AExtraOptions</name>
          <state></state>
        </option>
        <option>
          <name>OAEnhancedCore</name>
          <state>1</state>
        </option>
        <option>
          <name>AExtraOptionsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>AMaxErrOn</name>
          <state>0</state>
        </option>
        <option>
          <name>AMaxErrNum</name>
          <state>100</state>
        </option>
        <option>
          <name>ANewIncludes</name>
          <state>$TOOLKIT_DIR$\INC\</state>
        </option>
        <option>
          <name>AsmMultiByteSupport</name>
          <state>0</state>
        </option>
        <option>
          <name>AavrVariantMemory</name>
          <state>0</state>
        </option>
        <option>
          <name>AsmHasElpm</name>
          <state>0</state>
        </option>
        <option>
          <name>AsmOutputFile</name>
          <state>$FILE_BNAME$.r90</state>
        </option>
        <option>
          <name>AStdIncCheck</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>CUSTOM</name>
      <archiveVersion>3</archiveVersion>
      <data>
        <extensions></extensions>
        <cmdline></cmdline>
        <hasPrio>0</hasPrio>
      </data>
    </settings>
    <settings>
      <name>BICOMP</name>
      <archiveVersion>0</archiveVersion>
      <data/>
    </settings>
    <settings>
      <name>BUILDACTION</name>
      <archiveVersion>1</archiveVersion>
      <data>
        <prebuild></prebuild>
        <postbuild></postbuild>
      </data>
    </settings>
    <settings>
      <name>XLINK</name>
      <archiveVersion>3</archiveVersion>
      <data>
        <version>16</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>XOutOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>OutputFile</name>
          <state>bacnet.d90</state>
        </option>
        <option>
          <name>OutputFormat</name>
          <version>11</version>
          <state>25</state>
        </option>
        <option>
          <name>FormatVariant</name>
          <version>9</version>
          <state>2</state>
        </option>
        <option>
          <name>SecondaryOutputFile</name>
          <state>(None for the selected format)</state>
        </option>
        <option>
          <name>XDefines</name>
          <state></state>
        </option>
        <option>
          <name>AlwaysOutput</name>
          <state>0</state>
        </option>
        <option>
          <name>OverlapWarnings</name>
          <state>0</state>
        </option>
        <option>
          <name>NoGlobalCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>XList</name>
          <state>0</state>
        </option>
        <option>
          <name>SegmentMap</name>
          <state>1</state>
        </option>
        <option>
          <name>ListSymbols</name>
          <state>2</state>
        </option>
        <option>
          <name>PageLengthCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLength</name>
          <state>80</state>
        </option>
        <option>
          <name>XIncludes</name>
          <state>$TOOLKIT_DIR$\LIB\</state>
        </option>
        <option>
          <name>ModuleStatus</name>
          <state>0</state>
        </option>
        <option>
          <name>XclOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>XclFile</name>
          <state>$TOOLKIT_DIR$\src\template\cfgm1284p.xcl</state>
        </option>
        <option>
          <name>XclFileSlave</name>
          <state></state>
        </option>
        <option>
          <name>DoFill</name>
          <state>0</state>
        </option>
        <option>
          <name>FillerByte</name>
          <state>0xFF</state>
        </option>
        <option>
          <name>DoCrc</name>
          <state>0</state>
        </option>
        <option>
          <name>CrcSize</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CrcAlgo</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcPoly</name>
          <state>0x11021</state>
        </option>
        <option>
          <name>CrcCompl</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>RangeCheckAlternatives</name>
          <state>0</state>
        </option>
        <option>
          <name>SuppressAllWarn</name>
          <state>0</state>
        </option>
        <option>
          <name>SuppressDiags</name>
          <state></state>
        </option>
        <option>
          <name>TreatAsWarn</name>
          <state></state>
        </option>
        <option>
          <name>TreatAsErr</name>
          <state></state>
        </option>
        <option>
          <name>ModuleLocalSym</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CrcBitOrder</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>OXSysConfig</name>
          <state>1</state>
        </option>
        <option>
          <name>XExtraOptionsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>XExtraOptions</name>
          <state></state>
        </option>
        <option>
          <name>IncludeSuppressed</name>
          <state>0</state>
        </option>
        <option>
          <name>ModuleSummary</name>
          <state>0</state>
        </option>
        <option>
          <name>xcProgramEntryLabel</name>
          <state>__program_start</state>
        </option>
        <option>
          <name>DebugInformation</name>
          <state>0</state>
        </option>
        <option>
          <name>RuntimeControl</name>
          <state>1</state>
        </option>
        <option>
          <name>IoEmulation</name>
          <state>1</state>
        </option>
        <option>
          <name>AllowExtraOutput</name>
          <state>0</state>
        </option>
        <option>
          <name>GenerateExtraOutput</name>
          <state>0</state>
        </option>
        <option>
          <name>XExtraOutOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>ExtraOutputFile</name>
          <state>bacnet.a90</state>
        </option>
        <option>
          <name>ExtraOutputFormat</name>
          <version>11</version>
          <state>25</state>
        </option>
        <option>
          <name>ExtraFormatVariant</name>
          <version>9</version>
          <state>2</state>
        </option>
        <option>
          <name>xcOverrideProgramEntryLabel</name>
          <state>0</state>
        </option>
        <option>
          <name>xcProgramEntryLabelSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>ListOutputFormat</name>
          <state>0</state>
        </option>
        <option>
          <name>BufferedTermOutput</name>
          <state>0</state>
        </option>
        <option>
          <name>XcRTLibraryFile</name>
          <state>1</state>
        </option>
        <option>
          <name>OXLibIOConfig</name>
          <state>1</state>
        </option>
        <option>
          <name>XLinkMisraHandler</name>
          <state>0</state>
        </option>
        <option>
          <name>OverlaySystemMap</name>
          <state>0</state>
        </option>
        <option>
          <name>RawBinaryFile</name>
          <state></state>
        </option>
        <option>
          <name>RawBinarySymbol</name>
          <state></state>
        </option>
        <option>
          <name>RawBinarySegment</name>
          <state></state>
        </option>
        <option>
          <name>RawBinaryAlign</name>
          <state></state>
        </option>
        <option>
          <name>CrcAlign</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcInitialValue</name>
          <state>0x00</state>
        </option>
        <option>
          <name>CrcUnitSize</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>XlinkLogEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>XlinkLogInputFiles</name>
          <state>0</state>
        </option>
        <option>
          <name>XlinkLogModuleSelection</name>
          <state>0</state>
        </option>
        <option>
          <name>XlinkLogPrintfScanf</name>
          <state>0</state>
        </option>
        <option>
          <name>XlinkLogSegmentSelection</name>
          <state>0</state>
        </option>
        <option>
          <name>XlinkLogStackDepth</name>
          <state>0</state>
        </option>
        <option>
          <name>XlinkStackUsageEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>XlinkControlFiles</name>
          <state></state>
        </option>
        <option>
          <name>XlinkCallGraphFileEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>XlinkCallGraphFileName</name>
          <state>$LIST_DIR$\$PROJ_FNAME$.call_graph.cgx</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>XAR</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>XAROutOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>XARInputs</name>
          <state></state>
        </option>
        <option>
          <name>OutputFile</name>
          <state></state>
        </option>
      </data>
    </settings>
    <settings>
      <name>BILINK</name>
      <archiveVersion>0</archiveVersion>
      <data/>
    </settings>
  </configuration>
  <configuration>
    <name>Release</name>
    <toolchain>
      <name>AVR</name>
    </toolchain>
    <debug>0</debug>
    <settings>
      <name>General</name>
      <archiveVersion>12</archiveVersion>
      <data>
        <version>10</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>GGEnhancedCore</name>
          <state>1</state>
        </option>
        <option>
          <name>Variant Memory</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>ExePath</name>
          <state>Release\Exe</state>
        </option>
        <option>
          <name>ObjPath</name>
          <state>Release\Obj</state>
        </option>
        <option>
          <name>ListPath</name>
          <state>Release\List</state>
        </option>
        <option>
          <name>GGEnableConfig</name>
          <state>1</state>
        </option>
        <option>
          <name>GG64KFlash</name>
          <state>0</state>
        </option>
        <option>
          <name>GG64BitDoubles</name>
          <state>0</state>
        </option>
        <option>
          <name>GGFPSLICCOnfig</name>
          <version>0</version>
          <state>3</state>
        </option>
        <option>
          <name>LCEnableBitDefs</name>
          <state>1</state>
        </option>
        <option>
          <name>LCHeapSize</name>
          <state>0x10</state>
        </option>
        <option>
          <name>SCCStackSize</name>
          <state>0x400</state>
        </option>
        <option>
          <name>SCExtCStack</name>
          <state>0</state>
        </option>
        <option>
          <name>SCRStackSize</name>
          <state>32</state>
        </option>
        <option>
          <name>SCExtRStack</name>
          <state>0</state>
        </option>
        <option>
          <name>SCEnableBus</name>
          <state>0</state>
        </option>
        <option>
          <name>SCAddWaitstate</name>
          <state>0</state>
        </option>
        <option>
          <name>SCRamBase</name>
          <state>0x0</state>
        </option>
        <option>
          <name>SCRamSize</name>
          <state>0x0</state>
        </option>
        <option>
          <name>SCRomBase</name>
          <state>0x0</state>
        </option>
        <option>
          <name>SCRomSize</name>
          <state>0x0</state>
        </option>
        <option>
          <name>SCNVBase</name>
          <state>0x0</state>
        </option>
        <option>
          <name>SCNVSize</name>
          <state>0x0</state>
        </option>
        <option>
          <name>SCInitWithReti</name>
          <state>1</state>
        </option>
        <option>
          <name>GOutputBinary</name>
          <state>0</state>
        </option>
        <option>
          <name>GGEepromUtil</name>
          <state>1</state>
        </option>
        <option>
          <name>GGEepromUtilSize</name>
          <state>4096</state>
        </option>
        <option>
          <name>GRuntimeLibSelect</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>RTDescription</name>
          <state>Use the normal configuration of the C/EC++</state>
          <state>runtime library. No locale interface,</state>
          <state>C locale, no file descriptor support,</state>
          <state>no multibytes in printf and scanf, and</state>
          <state>no hex floats in strtod.</state>
        </option>
        <option>
          <name>RTConfigPath</name>
          <state>$TOOLKIT_DIR$\LIB\DLIB\dlAVR-3s-ec_mul-n.h</state>
        </option>
        <option>
          <name>RTLibraryPath</name>
          <state>$TOOLKIT_DIR$\LIB\DLIB\dlAVR-3s-ec_mul-sf-n.r90</state>
        </option>
        <option>
          <name>Input variant</name>
          <version>36</version>
          <state>1</state>
        </option>
        <option>
          <name>Input description</name>
          <state>Full formatting</state>
        </option>
        <option>
          <name>Output variant</name>
          <version>36</version>
          <state>6</state>
        </option>
        <option>
          <name>Output description</name>
          <state>No specifier a, A, no specifier n, no float nor long long.</state>
        </option>
        <option>
          <name>GRuntimeLibSelectSlave</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>GeneralEnableMisra</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraVerbose</name>
          <state>0</state>
        </option>
        <option>
          <name>LCTinyHeapSize</name>
          <state>0x10</state>
        </option>
        <option>
          <name>LCNearHeapSize</name>
          <state>0x20</state>
        </option>
        <option>
          <name>LCFarHeapSize</name>
          <state>0x1000</state>
        </option>
        <option>
          <name>LCHugeHeapSize</name>
          <state>0x1000</state>
        </option>
        <option>
          <name>LCsHeapConfigText</name>
          <state>1</state>
        </option>
        <option>
          <name>GGNoMULInstruction</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraVer</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraRules04</name>
          <version>0</version>
          <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
        </option>
        <option>
          <name>GeneralMisraRules98</name>
          <version>0</version>
          <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
        </option>
        <option>
          <name>GenDeviceSelectMenu</name>
          <state>m1284p	ATmega1284P</state>
        </option>
        <option>
          <name>OGProductVersion</name>
          <state>6.80.1.1057</state>
        </option>
        <option>
          <name>OGLastSavedByProductVersion</name>
          <state></state>
        </option>
      </data>
    </settings>
    <settings>
      <name>ICCAVR</name>
      <archiveVersion>6</archiveVersion>
      <data>
        <version>17</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CCVariantProcessor</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEnhancedCore</name>
          <state>0</state>
        </option>
        <option>
          <name>CCVariantMemory</name>
          <state>0</state>
        </option>
        <option>
          <name>CCObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>CCDefines</name>
          <state>NDEBUG</state>
          <state>BACDL_MSTP</state>
          <state>MAX_APDU=128</state>
          <state>MAX_TSM_TRANSACTIONS=0</state>
          <state>MSTP_PDU_PACKET_COUNT=2</state>
          <state>MAX_CHARACTER_STRING_BYTES=64</state>
          <state>MAX_OCTET_STRING_BYTES=64</state>
          <state>BACAPP_BOOLEAN</state>
          <state>BACAPP_REAL</state>
          <state>BACAPP_OBJECT_ID</state>
          <state>BACAPP_UNSIGNED</state>
          <state>BACAPP_ENUMERATED</state>
          <state>BACAPP_CHARACTER_STRING</state>
          <state>WRITE_PROPERTY</state>
        </option>
        <option>
          <name>CCPreprocFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocComments</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCMnemonics</name>
          <state>1</state>
        </option>
        <option>
          <name>CCListCMessages</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListAssFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListAssSource</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEnableRemarks</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDiagSuppress</name>
          <state></state>
        </option>
        <option>
          <name>CCDiagRemark</name>
          <state></state>
        </option>
        <option>
          <name>CCDiagWarning</name>
          <state></state>
        </option>
        <option>
          <name>CCDiagError</name>
          <state></state>
        </option>
        <option>
          <name>CCWarnAsError</name>
          <state>0</state>
        </option>
        <option>
          <name>CCConstInRAM</name>
          <state>1</state>
        </option>
        <option>
          <name>CCInitInFlash</name>
          <state>1</state>
        </option>
        <option>
          <name>CCForceVariables</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOldCallConv</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLockRegs</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCAllowList</name>
          <version>3</version>
          <state>111111</state>
        </option>
        <option>
          <name>CCCrossCallPassesList</name>
          <version>8</version>
          <state>5</state>
        </option>
        <option>
          <name>CCObjUseModuleName</name>
          <state>0</state>
        </option>
        <option>
          <name>CCObjModuleName</name>
          <state></state>
        </option>
        <option>
          <name>CCDebugInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCNoErrorMsg</name>
          <state>0</state>
        </option>
        <option>
          <name>CC64BitDoubles</name>
          <state>0</state>
        </option>
        <option>
          <name>CC64KFlash</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEnableExtBus</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEnableBitDefs</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptForceCrossCall</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCharIs</name>
          <state>1</state>
        </option>
        <option>
          <name>CCExt</name>
          <state>0</state>
        </option>
        <option>
          <name>IExtraOptions</name>
          <state></state>
        </option>
        <option>
          <name>IExtraOptionsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMultibyteSupport</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRequirePrototypes</name>
          <state>1</state>
        </option>
        <option>
          <name>CCCompilerRuntimeInfo</name>
          <state>0</state>
        </option>
        <option>
          <name>newCCIncludePaths</name>
          <state>$PROJ_DIR$</state>
          <state>$PROJ_DIR$\..\..\src</state>
        </option>
        <option>
          <name>CCStdIncCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEepromSize</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLockRegsSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>CCOutputFile</name>
          <state>$FILE_BNAME$.r90</state>
        </option>
        <option>
          <name>CompilerMisraOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLibConfigHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>PreInclude</name>
          <state></state>
        </option>
        <option>
          <name>CCOverrideModuleTypeDefault</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRadioModuleType</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRadioModuleTypeSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>OCCAdditionalCommandLineOptionsSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>CompilerMisraRules04</name>
          <version>0</version>
          <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
        </option>
        <option>
          <name>CompilerMisraRules98</name>
          <version>0</version>
          <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
        </option>
        <option>
          <name>IccLang</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCDialect</name>
          <state>1</state>
        </option>
        <option>
          <name>IccCppDialect</name>
          <state>1</state>
        </option>
        <option>
          <name>IccAllowVLA</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCppInlineSemantics</name>
          <state>0</state>
        </option>
        <option>
          <name>IccStaticDestr</name>
          <state>1</state>
        </option>
        <option>
          <name>IccFloatSemantics</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptStrategy</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CCOptLevel</name>
          <state>3</state>
        </option>
        <option>
          <name>CCOptLevelSlave</name>
          <state>3</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>AAVR</name>
      <archiveVersion>5</archiveVersion>
      <data>
        <version>11</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>IProcessor</name>
          <state>0</state>
        </option>
        <option>
          <name>AObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>ACaseSensitivity</name>
          <state>1</state>
        </option>
        <option>
          <name>AWarnEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>AWarnWhat</name>
          <state>0</state>
        </option>
        <option>
          <name>AWarnOne</name>
          <state></state>
        </option>
        <option>
          <name>AWarnRange1</name>
          <state></state>
        </option>
        <option>
          <name>AWarnRange2</name>
          <state></state>
        </option>
        <option>
          <name>CDebug</name>
          <state>0</state>
        </option>
        <option>
          <name>ADefines</name>
          <state></state>
        </option>
        <option>
          <name>MacroChars</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>UndefAsm</name>
          <state>1</state>
        </option>
        <option>
          <name>UndefFile</name>
          <state>1</state>
        </option>
        <option>
          <name>UndefLine</name>
          <state>1</state>
        </option>
        <option>
          <name>UndefTime</name>
          <state>1</state>
        </option>
        <option>
          <name>UndefDate</name>
          <state>1</state>
        </option>
        <option>
          <name>UndefTid</name>
          <state>1</state>
        </option>
        <option>
          <name>AList</name>
          <state>0</state>
        </option>
        <option>
          <name>AListHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>AListing</name>
          <state>1</state>
        </option>
        <option>
          <name>Includes</name>
          <state>0</state>
        </option>
        <option>
          <name>MacDefs</name>
          <state>0</state>
        </option>
        <option>
          <name>MacExps</name>
          <state>1</state>
        </option>
        <option>
          <name>MacExec</name>
          <state>0</state>
        </option>
        <option>
          <name>OnlyAssed</name>
          <state>0</state>
        </option>
        <option>
          <name>MultiLine</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLengthCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLength</name>
          <state>80</state>
        </option>
        <option>
          <name>TabSpacing</name>
          <state>8</state>
        </option>
        <option>
          <name>AXRef</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDefines</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefInternal</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDual</name>
          <state>0</state>
        </option>
        <option>
          <name>AExtraOptions</name>
          <state></state>
        </option>
        <option>
          <name>OAEnhancedCore</name>
          <state>1</state>
        </option>
        <option>
          <name>AExtraOptionsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>AMaxErrOn</name>
          <state>0</state>
        </option>
        <option>
          <name>AMaxErrNum</name>
          <state>100</state>
        </option>
        <option>
          <name>ANewIncludes</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>AsmMultiByteSupport</name>
          <state>0</state>
        </option>
        <option>
          <name>AavrVariantMemory</name>
          <state>0</state>
        </option>
        <option>
          <name>AsmHasElpm</name>
          <state>0</state>
        </option>
        <option>
          <name>AsmOutputFile</name>
          <state></state>
        </option>
        <option>
          <name>AStdIncCheck</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>CUSTOM</name>
      <archiveVersion>3</archiveVersion>
      <data>
        <extensions></extensions>
        <cmdline></cmdline>
        <hasPrio>0</hasPrio>
      </data>
    </settings>
    <settings>
      <name>BICOMP</name>
      <archiveVersion>0</archiveVersion>
      <data/>
    </settings>
    <settings>
      <name>BUILDACTION</name>
      <archiveVersion>1</archiveVersion>
      <data>
        <prebuild></prebuild>
        <postbuild></postbuild>
      </data>
    </settings>
    <settings>
      <name>XLINK</name>
      <archiveVersion>3</archiveVersion>
      <data>
        <version>16</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>XOutOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>OutputFile</name>
          <state>bacnet.d90</state>
        </option>
        <option>
          <name>OutputFormat</name>
          <version>11</version>
          <state>25</state>
        </option>
        <option>
          <name>FormatVariant</name>
          <version>9</version>
          <state>2</state>
        </option>
        <option>
          <name>SecondaryOutputFile</name>
          <state>(None for the selected format)</state>
        </option>
        <option>
          <name>XDefines</name>
          <state></state>
        </option>
        <option>
          <name>AlwaysOutput</name>
          <state>0</state>
        </option>
        <option>
          <name>OverlapWarnings</name>
          <state>0</state>
        </option>
        <option>
          <name>NoGlobalCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>XList</name>
          <state>0</state>
        </option>
        <option>
          <name>SegmentMap</name>
          <state>1</state>
        </option>
        <option>
          <name>ListSymbols</name>
          <state>2</state>
        </option>
        <option>
          <name>PageLengthCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLength</name>
          <state>80</state>
        </option>
        <option>
          <name>XIncludes</name>
          <state>$TOOLKIT_DIR$\LIB\</state>
        </option>
        <option>
          <name>ModuleStatus</name>
          <state>0</state>
        </option>
        <option>
          <name>XclOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>XclFile</name>
          <state>$TOOLKIT_DIR$\src\template\cfgm644p.xcl</state>
        </option>
        <option>
          <name>XclFileSlave</name>
          <state></state>
        </option>
        <option>
          <name>DoFill</name>
          <state>0</state>
        </option>
        <option>
          <name>FillerByte</name>
          <state>0xFF</state>
        </option>
        <option>
          <name>DoCrc</name>
          <state>0</state>
        </option>
        <option>
          <name>CrcSize</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CrcAlgo</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcPoly</name>
          <state>0x11021</state>
        </option>
        <option>
          <name>CrcCompl</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>RangeCheckAlternatives</name>
          <state>0</state>
        </option>
        <option>
          <name>SuppressAllWarn</name>
          <state>0</state>
        </option>
        <option>
          <name>SuppressDiags</name>
          <state></state>
        </option>
        <option>
          <name>TreatAsWarn</name>
          <state></state>
        </option>
        <option>
          <name>TreatAsErr</name>
          <state></state>
        </option>
        <option>
          <name>ModuleLocalSym</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CrcBitOrder</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>OXSysConfig</name>
          <state>1</state>
        </option>
        <option>
          <name>XExtraOptionsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>XExtraOptions</name>
          <state></state>
        </option>
        <option>
          <name>IncludeSuppressed</name>
          <state>0</state>
        </option>
        <option>
          <name>ModuleSummary</name>
          <state>0</state>
        </option>
        <option>
          <name>xcProgramEntryLabel</name>
          <state>__program_start</state>
        </option>
        <option>
          <name>DebugInformation</name>
          <state>0</state>
        </option>
        <option>
          <name>RuntimeControl</name>
          <state>1</state>
        </option>
        <option>
          <name>IoEmulation</name>
          <state>1</state>
        </option>
        <option>
          <name>AllowExtraOutput</name>
          <state>0</state>
        </option>
        <option>
          <name>GenerateExtraOutput</name>
          <state>0</state>
        </option>
        <option>
          <name>XExtraOutOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>ExtraOutputFile</name>
          <state>bacnet.a90</state>
        </option>
        <option>
          <name>ExtraOutputFormat</name>
          <version>11</version>
          <state>25</state>
        </option>
        <option>
          <name>ExtraFormatVariant</name>
          <version>9</version>
          <state>2</state>
        </option>
        <option>
          <name>xcOverrideProgramEntryLabel</name>
          <state>0</state>
        </option>
        <option>
          <name>xcProgramEntryLabelSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>ListOutputFormat</name>
          <state>0</state>
        </option>
        <option>
          <name>BufferedTermOutput</name>
          <state>0</state>
        </option>
        <option>
          <name>XcRTLibraryFile</name>
          <state>1</state>
        </option>
        <option>
          <name>OXLibIOConfig</name>
          <state>1</state>
        </option>
        <option>
          <name>XLinkMisraHandler</name>
          <state>0</state>
        </option>
        <option>
          <name>OverlaySystemMap</name>
          <state>0</state>
        </option>
        <option>
          <name>RawBinaryFile</name>
          <state></state>
        </option>
        <option>
          <name>RawBinarySymbol</name>
          <state></state>
        </option>
        <option>
          <name>RawBinarySegment</name>
          <state></state>
        </option>
        <option>
          <name>RawBinaryAlign</name>
          <state></state>
        </option>
        <option>
          <name>CrcAlign</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcInitialValue</name>
          <state>0x00</state>
        </option>
        <option>
          <name>CrcUnitSize</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>XlinkLogEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>XlinkLogInputFiles</name>
          <state>0</state>
        </option>
        <option>
          <name>XlinkLogModuleSelection</name>
          <state>0</state>
        </option>
        <option>
          <name>XlinkLogPrintfScanf</name>
          <state>0</state>
        </option>
        <option>
          <name>XlinkLogSegmentSelection</name>
          <state>0</state>
        </option>
        <option>
          <name>XlinkLogStackDepth</name>
          <state>0</state>
        </option>
        <option>
          <name>XlinkStackUsageEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>XlinkControlFiles</name>
          <state></state>
        </option>
        <option>
          <name>XlinkCallGraphFileEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>XlinkCallGraphFileName</name>
          <state>$LIST_DIR$\$PROJ_FNAME$.call_graph.cgx</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>XAR</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>XAROutOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>XARInputs</name>
          <state></state>
        </option>
        <option>
          <name>OutputFile</name>
          <state></state>
        </option>
      </data>
    </settings>
    <settings>
      <name>BILINK</name>
      <archiveVersion>0</archiveVersion>
      <data/>
    </settings>
  </configuration>
  <group>
    <name>BACnet Basic</name>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\basic\service\h_apdu.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\basic\service\h_dcc.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\basic\service\h_noserv.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\basic\npdu\h_npdu.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\basic\service\h_rd.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\basic\service\h_rp.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\basic\service\h_rpm.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\basic\service\h_whohas.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\basic\service\h_whois.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\basic\service\h_wp.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\basic\service\s_iam.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\basic\service\s_ihave.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\basic\tsm\tsm.c</name>
    </file>
  </group>
  <group>
    <name>BACnet Basic Sys</name>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\basic\sys\fifo.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\basic\sys\mstimer.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\basic\sys\ringbuf.c</name>
    </file>
  </group>
  <group>
    <name>BACnet Datalink</name>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\datalink\crc.c</name>
    </file>
  </group>
  <group>
    <name>BACnet-Core</name>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\abort.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\bacaddr.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\bacapp.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\bacdcode.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\bacerror.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\bacint.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\bacreal.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\bacstr.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\dcc.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\iam.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\ihave.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\memcopy.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\npdu.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\proplist.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\rd.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\reject.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\rp.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\rpm.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\whohas.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\whois.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\src\bacnet\wp.c</name>
    </file>
  </group>
  <file>
    <name>$PROJ_DIR$\adc.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\ai.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\av.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\bacnet.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\bi.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\bname.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\bo.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\device.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\dlmstp.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\eeprom.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\fuses.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\init.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\input.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\led.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\main.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\mstimer-init.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\rs485.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\seeprom.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\serial.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\stack.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\test.c</name>
  </file>
  <file>
    <name>$PROJ_DIR$\watchdog.c</name>
  </file>
</project>


