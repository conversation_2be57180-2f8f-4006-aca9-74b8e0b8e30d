---
description: 
globs: 
alwaysApply: false
---
# 状态管理

本项目使用Pinia进行状态管理，并集成了持久化存储插件。

## 主入口
- [src/stores/index.ts](mdc:src/stores/index.ts) - Pinia存储的主配置和全局状态

## 状态模块
- [src/stores/modules/auth.ts](mdc:src/stores/modules/auth.ts) - 用户认证和权限相关状态
- [src/stores/modules/tabs.ts](mdc:src/stores/modules/tabs.ts) - 标签页管理状态
- [src/stores/modules/keepAlive.ts](mdc:src/stores/modules/keepAlive.ts) - 组件缓存状态管理
- [src/stores/modules/watchdog.ts](mdc:src/stores/modules/watchdog.ts) - 监控狗状态管理

## 特性
- 使用组合式API的方式定义和使用状态
- 集成持久化存储，部分状态在页面刷新后保留
- 支持TypeScript类型定义

