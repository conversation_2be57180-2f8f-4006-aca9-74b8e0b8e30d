#include "iEMSSetting.h"

#include <iostream>
#include <string>
#include <algorithm>
#include <typeinfo>
#include <string>
#include <vector>
#include <sstream>
#include <cstring>
#include <iostream>
#include <chrono>
#include <iomanip>
#include <ctime>
#include <fstream>
#include <cctype>
#include <map>
#include <unordered_map>

CiEMSSetting::CiEMSSetting() :
	isValid_(false)
{

}

bool CiEMSSetting::isValid()
{
	return isValid_;
}

bool CiEMSSetting::initialize(Json::Value root)
{
	iemsConfig_.product = root["product"].asString();
	iemsConfig_.dev_module = root["dev_module"].asString();
	iemsConfig_.register_max_num = root["register_max_num"].asUInt64();

	if (root.isMember("com_link_param") && root["com_link_param"].type() == Json::arrayValue)
	{
		for (auto& com : root["com_link_param"])
		{
			SIEMSCOMInfo ci;
			if (iemsStringToTransferInterfaceMap.find(com["type"].asString()) !=
				iemsStringToTransferInterfaceMap.end())
			{
				ci.type = iemsStringToTransferInterfaceMap[com["type"].asString()];
			}
			else
			{
				continue;
			}
			ci.com_name = com["com_name"].asString();
			ci.tty_channel = com["tty_channel"].asString();
			ci.led = com["led"].asString();

			iemsConfig_.com_link_param[ci.com_name] = ci;
		}
	}

	if (root.isMember("slave_max_limit") && root["slave_max_limit"].type() == Json::objectValue)
	{
		iemsConfig_.slave_max_limit.rs232 = root["slave_max_limit"]["rs232"].asUInt64();
		iemsConfig_.slave_max_limit.rs485 = root["slave_max_limit"]["rs485"].asUInt64();
		iemsConfig_.slave_max_limit.tcp = root["slave_max_limit"]["tcp"].asUInt64();
	}

	if (root.isMember("protocol_down") && root["protocol_down"].type() == Json::objectValue)
	{
		for (auto proIter = root["protocol_down"].begin(); proIter != root["protocol_down"].end(); proIter++)
		{
			std::string protocolName = proIter.name();
			SIEMSProtocolDown pd;
			pd.enable = root["protocol_down"][protocolName]["enable"].asBool();
			for (auto& proType : root["protocol_down"][protocolName]["type"])
			{
				if (iemsStringToTransferInterfaceMap.find(proType.asString()) !=
					iemsStringToTransferInterfaceMap.end())
				{
					pd.type[iemsStringToTransferInterfaceMap[proType.asString()]] = true;
				}
				iemsConfig_.protocol_down[protocolName] = pd;
			}
		}
	}

	isValid_ = true;
	return true;
}

bool CiEMSSetting::controlRunningStateLed(EIEMSRunningStateLed led, int brightness)
{
	if (iemsRunningStateToStringMap.find(led) != iemsRunningStateToStringMap.end())
		execLedControl(iemsRunningStateToStringMap[led], brightness);

	return true;
}

bool CiEMSSetting::controlCOMStateLed(std::string name, int brightness)
{
	if (!isValid_)
		return false;

	for (auto com : iemsConfig_.com_link_param)
	{
		if (com.second.tty_channel == name)
		{
			execLedControl(com.second.led, brightness);
		}
	}

	return true;
}

void CiEMSSetting::execLedControl(std::string led, int brightness)
{
	std::string cmd = "echo " + std::to_string(brightness) + " > " + led;

	//std::cout << cmd << std::endl;
#if defined(_WIN32) || defined(_WIN64)
#else
	system(cmd.c_str());
#endif
}