#!/bin/bash


# 增强的DNS提取逻辑：过滤注释行/空行，仅处理有效的nameserver行
dnsArray=()
while read -r line; do
    if [[ "$line" =~ ^[[:space:]]*nameserver[[:space:]]+([^[:space:]#]+) ]]; then
        dnsArray+=("${BASH_REMATCH[1]}")
    fi
done < /etc/resolv.conf

# 合并DNS为分号分隔字符串（示例输出：**********;*******）
dns=$(IFS=';'; echo "${dnsArray[*]}")

    name=$1
    ipv4=""
    netmask="*************"
    mac="0:0:0:0"
    ipv6="::0"
    type=0
    rxPackets=0
    rxBytes=0
    txPackets=0
    txBytes=0
    linkDetected="no"
    dhcp="no"
    gateway=""
    enableChange="yes"
    ssid=""

#    if [ $name == "eth0" ]; then
#        enableChange="no"
#    fi

    # 关键修改2：添加样本数据验证
    eval $(ifconfig $name 2>/dev/null | grep -w inet |awk -F'[ :]+' '{printf("ipv4=%s\n",$3)}')
   # eval $(ifconfig $name 2>/dev/null | grep -w inet |awk -F'Mask:' '{printf("netmask=%s\n",$1)}')
    eval $(ifconfig $name 2>/dev/null | grep -w inet | awk '{for(i=1; i<=NF; i++) { if ($i == "netmask") { printf("netmask=%s\n", $(i+1)); exit } }}')

    eval $(ifconfig $name 2>/dev/null | grep -w inet6 | awk '{printf("ipv6=%s\n",$3)}')
    eval $(ifconfig $name 2>/dev/null | grep -w "RX packets" | awk '{printf("rxPackets=%d\nrxBytes=%d\n",$3,$5)}')
    eval $(ifconfig $name 2>/dev/null | grep -w "TX packets" | awk '{printf("txPackets=%d\ntxBytes=%d\n",$3,$5)}')

    eval $(ethtool $name 2>/dev/null |grep "Link detected"| awk '{printf("linkDetected=%s\n",$3)}')
    eval $(ip route show dev $name 2>/dev/null | grep default | awk '{printf("gateway=%s\n",$3)}')
    type=$(cat /sys/class/net/$name/type 2>/dev/null)

    if [ $type -ne 772 ]; then
        mac=$(cat /sys/class/net/$name/address 2>/dev/null)
    fi
    if [ $type -eq 512 ]; then
#        enableChange="no"
        linkDetected="yes"
    fi

    output=$(printf "{\"enableChange\":%s,\"name\":%s,\"ipv4\":%s,\"netmask\":%s,\"mac\":%s,\"ipv6\":%s,\"type\":%d,\"rxPackets\":%d,\"rxBytes\":%d,\"txPackets\":%d,\"txBytes\":%d,\"linkDetected\":%s,\"dhcp\":%s,\"dns\":%s,\"gateway\":%s,\"ssid\":%s}" \"$enableChange\" \"$name\" \"$ipv4\" \"$netmask\" \"$mac\" \"$ipv6\" $type $rxPackets $rxBytes $txPackets $txBytes \"$linkDetected\" \"$dhcp\" \"$dns\" \"$gateway\" \"$ssid\")

echo $output

