---
description: 
globs: 
alwaysApply: false
---
# 项目结构

## 源代码目录结构
- **src/api/** - API请求和接口定义
- **src/assets/** - 静态资源（图片、字体等）
- **src/components/** - 可复用Vue组件
- **src/config/** - 应用程序配置
- **src/directives/** - 自定义Vue指令
- **src/enums/** - 枚举类型定义
- **src/hooks/** - 自定义Vue组合式API钩子
- **src/layouts/** - 布局组件
- **src/routers/** - Vue路由配置
- **src/stores/** - Pinia状态管理
- **src/styles/** - 全局样式文件
- **src/typings/** - TypeScript类型定义
- **src/utils/** - 工具函数
- **src/views/** - 页面视图组件

## 其他目录
- **public/** - 静态资源，不经过Vite处理
- **.vscode/** - VS Code配置

