/* definitions generated by preprocessor, copy into defines.h */
#ifndef PPINC
#define _ATMEGA644P     /* device select: _ATMEGAxxxx */
#define _B2048  /* boot size select: _Bxxxx (words), powers of two only */
#ifdef __ICCAVR__
#include "iom644.h"
#endif
#if __GNUC__
#include <avr/io.h>

#if (__GNUC__ <= 4) && (__GNUC_MINOR__ < 3)

#if !defined(EEWE) && defined(EEPE)
#define EEWE EEPE
#endif

#if !defined(EEMWE) && defined(EEMPE)
#define EEMWE EEMPE
#endif

#endif
#endif

/* define pin for enter-self-prog-mode */
#define PROGPORT PORTB
#define PROGPIN PINB
#define PROG_NO PB0

/* baud rate register value calculation */
#define CPU_FREQ 18430000
#define BAUD_RATE 115200
#define BRREG_VALUE 9

/* definitions for UART control */
#define BAUD_RATE_LOW_REG UBRR1
#define UART_CONTROL_REG UCSR1B
#define ENABLE_TRANSMITTER_BIT TXEN1
#define ENABLE_RECEIVER_BIT RXEN1
#define UART_STATUS_REG UCSR1A
#define TRANSMIT_COMPLETE_BIT TXC1
#define RECEIVE_COMPLETE_BIT RXC1
#define UART_DATA_REG UDR1

/* definitions for SPM control */
#define SPMCR_REG SPMCSR
#define PAGESIZE 256
#define APP_END 61440
/*#define LARGE_MEMORY */

/* definitions for device recognition */
#define PARTCODE 0
#define SIGNATURE_BYTE_1 0x1E
#define SIGNATURE_BYTE_2 0x96
#define SIGNATURE_BYTE_3 0x0A

/* indicate that preprocessor result is included */
#define PPINC
#endif
