#include "ConfigurationCaching.h"

#include "Utils/String.hpp"

int CConfigurationCaching::callback(void* NotUsed, int argc, char** argv, char** azColName)
{
	int i;
	for (i = 0; i < argc; i++) {
		printf("%s = %s\n", azColName[i], argv[i] ? argv[i] : "NULL");
	}
	printf("\n");
	return 0;
}

int CConfigurationCaching::callbackSelecTemplate(void* ud, int argc, char** argv, char** azColName)
{
	std::map<std::string, std::string>* map = static_cast<std::map<std::string, std::string>*>(ud);

	std::string id, content;
	for (int i = 0; i < argc; i++)
	{
		if (strcmp(azColName[i], "id") == 0)
		{
			id = argv[i];
		}
		else if (strcmp(azColName[i], "content") == 0)
		{
			content = argv[i];
		}
		if (!id.empty() && !content.empty())
			map->insert(std::make_pair(id, content));
	}
	return 0;
}

bool CConfigurationCaching::exec(char* sql, CB cb, void* ud)
{
	bool ret = false;
	char* zErrMsg = 0;
	int rc = sqlite3_exec(db_, sql, cb, ud, &zErrMsg);
	if (rc != SQLITE_OK) {
		fprintf(stderr, "exec SQL error: %s\n", zErrMsg);
		sqlite3_free(zErrMsg);
		ret = false;
	}
	else {
		//fprintf(stdout, "exec SQL successfully\n");
		ret = true;
	}
	return ret;
}

CConfigurationCaching::CConfigurationCaching()
{
}

CConfigurationCaching::~CConfigurationCaching()
{
}

bool CConfigurationCaching::initial(std::string name)
{
    /* Open database */
    int rc = sqlite3_open((name + ".db").c_str(), &db_);
    if (rc) 
	{
        fprintf(stderr, "Can't open database: %s\n", sqlite3_errmsg(db_));
        return false;
    }

    /* Execute SQL statement */
    bool ret = exec((char*)UTILS::STRING::SFormat(sqlCreateDeviceTemplate, name).c_str(), callback, 0);
    if (!ret)
    {
        return false;
    }

	ret = exec((char*)UTILS::STRING::SFormat(sqlCreateConfigurationTemplate, name).c_str(), callback, 0);
	if (!ret)
	{
		return false;
	}
    return true;
}

bool CConfigurationCaching::uninitial()
{
	sqlite3_close(db_);
	return true;
}

bool CConfigurationCaching::updateDeviceTemplate(std::string id, std::string body)
{
	return exec((char*)UTILS::STRING::SFormat(sqlUpdateDeviceTemplate, id, body).c_str(), callback, 0);	
}

bool CConfigurationCaching::deleteDeviceTemplate(std::string id)
{
	return exec((char*)UTILS::STRING::SFormat(sqlDeleteDeviceTemplate, id).c_str(), callback, 0);
}

std::map<std::string, std::string> CConfigurationCaching::selectDeviceTemplate(std::string id)
{
	std::map<std::string, std::string> map;
	if (id.empty())
	{
		// select all
		exec((char*)UTILS::STRING::SFormat(sqlSelectDeviceTemplateAll).c_str(), callbackSelecTemplate, &map);
	}
	else
	{
		// select one
		exec((char*)UTILS::STRING::SFormat(sqlSelectDeviceTemplateOne, id).c_str(), callbackSelecTemplate, &map);
	}
	return map;
}

bool CConfigurationCaching::updateConfigurationTemplate(std::string id, std::string body)
{
	return exec((char*)UTILS::STRING::SFormat(sqlUpdateConfigurationTemplate, id, body).c_str(), callback, 0);
}

bool CConfigurationCaching::deleteConfigurationTemplate(std::string id)
{
	return exec((char*)UTILS::STRING::SFormat(sqlDeleteConfigurationTemplate, id).c_str(), callback, 0);
}

std::map<std::string, std::string> CConfigurationCaching::selectConfigurationTemplate(std::string id)
{
	std::map<std::string, std::string> map;
	if (id.empty())
	{
		// select all
		exec((char*)UTILS::STRING::SFormat(sqlSelectConfigurationTemplateAll).c_str(), callbackSelecTemplate, &map);
	}
	else
	{
		// select one
		exec((char*)UTILS::STRING::SFormat(sqlSelectConfigurationTemplateOne, id).c_str(), callbackSelecTemplate, &map);
	}
	return map;
}
