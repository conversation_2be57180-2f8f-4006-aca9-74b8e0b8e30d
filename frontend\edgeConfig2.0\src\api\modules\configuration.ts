import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";

//* 获取组态配置信息
export const getConfiguration = () => {
    return http.post<any>(`${PORT1}/getConfiguration`, {});
};

//* 导出组态配置
export const exportConfiguration = (params: { configId: string }) => {
    return http.download(`${PORT1}/exportConfiguration`, params);
};

//* 导入组态配置
export const importConfiguration = (params: FormData) => {
    return http.put<any>(`${PORT1}/importConfiguration`, params, {
        timeout: 3600000,
        headers: { "Content-Type": "multipart/form-data" }
    });
};

//* 启动组态采集
export const startCollection = (params: { configId: string }) => {
    return http.post<any>(`${PORT1}/startCollection`, params);
};

//* 保存设备模板
export const saveDeviceTemplate = (params: any) => {
    return http.post<any>(`${PORT1}/setDeviceTemplate`, params);
};

//* 删除设备模板
export const deleteDeviceTemplate = (params: any) => {
    return http.post<any>(`${PORT1}/deleteDeviceTemplate`, params);
};

// 获取IEMS固件配置信息
export const getIEMSConfig = ()=>{
    return http.post<any>(`${PORT1}/getIEMSConfig`, {});
}

// 获取日志信息
export const getLogInfos = (params:{
    linkId:string,
    deviceId:string
})=>{
    return http.post<any>(`${PORT1}/getIEMSDeviceLogs`, params, { headers: { noLoading: true } });
} 

//* 获取设备模板列表
export const getDeviceTemplateList = () => {
    return http.post<any>(`${PORT1}/getDeviceTemplate`, {});
};

//* 停止组态采集
export const stopCollection = (params: { configId: string }) => {
    return http.post<any>(`${PORT1}/stopCollection`, params);
};

//* 获取组态报文
export const getConfigurationMessages = (params: { configId: string }) => {
    return http.post<any>(`${PORT1}/getConfigurationMessages`, params);
};

//* 获取设备点位实时数据
export const getConfigPoints = (params: { link: string }) => {
    // 确保参数正确传递，API 调用时会自动将对象转换为 JSON
    return http.post<any>(`${PORT1}/getConfigPoints`, params, { headers: { noLoading: true } });
};

//* 保存组态配置模板
export const saveConfigurationTemplate = (params: any) => {
    return http.post<any>(`${PORT1}/setConfigurationTemplate`, params);
};

//* 获取组态配置模板
export const getConfigurationTemplate = () => {
    return http.post<any>(`${PORT1}/getConfigurationTemplate`, {});
};

//* 删除组态配置模板
export const deleteConfigurationTemplate = (params: any) => {
    return http.post<any>(`${PORT1}/deleteConfigurationTemplate`, params);
};

//* 获取组态配置链路模板
export const getConfigLinkTemplate = () => {
    return http.post<any>(`${PORT1}/getConfigLinkTemplate`, {});
};

//* 应用组态配置
export const applyLocalConfiguration = (params: any) => {
    return http.post<any>(`${PORT1}/setLocalConfiguration`, params);
};

//* 启动本地组态采集
export const startLocalConfigurationWork = (params: { configId: string }) => {
    return http.post<any>(`${PORT1}/startLocalConfigurationWork`, params);
};

//* 停止本地组态采集
export const stopLocalConfigurationWork = (params: { configId: string }) => {
    return http.post<any>(`${PORT1}/stopLocalConfigurationWork`, params);
};

//* 获取组态运行状态
export const getConfigRunningState = () => {
    return http.post<any>(`${PORT1}/getConfigRunningState`, {}, { headers: { noLoading: true } });
};

//* 获取OPC服务器状态
export const getConfigOPCServerState = () => {
    return http.post<any>(`${PORT1}/getConfigOPCServerState`, {}, { headers: { noLoading: true } });
};

//* 设置OPC服务器状态
export const setConfigOPCServerState = (params: { state: boolean, endpoint: string }) => {
    return http.post<any>(`${PORT1}/setConfigOPCServerState`, params);
};

//* 设置组态点位控制值
export const setConfigPointValue = (params: any = {}) => {
    return http.post<any>(`${PORT1}/setConfigPointValue`, params);
};





