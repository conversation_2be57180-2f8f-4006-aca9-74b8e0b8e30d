---
description: 
globs: 
alwaysApply: false
---
# 开发指南

## 开发命令
```bash
# 安装依赖
yarn install

# 启动开发服务器
yarn dev

# 构建生产版本
yarn build

# 预览生产版本
yarn preview
```

## 项目规范
1. **代码风格**: 项目使用Prettier进行代码格式化，配置见[.prettierrc](mdc:.prettierrc)
2. **TypeScript**: 遵循项目的TypeScript配置，见[tsconfig.json](mdc:tsconfig.json)
3. **组件命名**: 
   - 公共组件使用PascalCase命名方式
   - 组件文件使用.vue扩展名
4. **样式规范**:
   - 使用SCSS预处理器
   - 全局样式在src/styles目录
   - 组件样式使用scoped属性隔离

## 目录结构约定
- 新的视图组件应放在`src/views/`目录下
- 新的可复用组件应放在`src/components/`目录下
- 新的API请求方法应放在`src/api/modules/`目录下
- 新的状态管理模块应放在`src/stores/modules/`目录下

