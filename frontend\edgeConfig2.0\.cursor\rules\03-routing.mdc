---
description: 
globs: 
alwaysApply: false
---
# 路由配置

本项目使用Vue Router进行路由管理，包含静态路由和动态路由。

## 路由主文件
- [src/routers/index.ts](mdc:src/routers/index.ts) - 主路由配置，包含路由守卫和初始化逻辑

## 路由模块
- [src/routers/modules/staticRouter.ts](mdc:src/routers/modules/staticRouter.ts) - 静态路由配置
- [src/routers/modules/dynamicRouter.ts](mdc:src/routers/modules/dynamicRouter.ts) - 动态路由配置和权限控制

## 路由特性
- 使用`createWebHashHistory`作为历史模式
- 实现路由守卫进行权限控制
- 支持动态加载路由
- 集成NProgress进度条

