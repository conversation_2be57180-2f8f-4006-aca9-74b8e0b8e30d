# CMakeList.txt : CMake project for SystemSetting, include source and define
# project specific logic here.
#

# do not specify the project language
project (iEMSSetting VERSION 1.0)

set(LIBRARY_OUTPUT_PATH ${OUTPUT_DIR})
set(CMAKE_BINARY_DIR ${BUILD_DIR}/iEMSSetting)

set(CMAKE_POSITION_INDEPENDENT_CODE ON)

if(WIN32)
    
elseif(UNIX)
    find_package(Threads)
endif()

include_directories(
    ${JSONCPP_DIR}
)

link_directories(
)

SET(LIBRARIES
    
)

SET(IEMS_SETTING_SRC
    iEMSSetting.cpp
)

add_library(iEMSSetting SHARED ${IEMS_SETTING_SRC} ${JSONCPP_SRC}) 
# link system library
if(UNIX)
    target_link_libraries (iEMSSetting PRIVATE ${CMAKE_THREAD_LIBS_INIT} ${CMAKE_DL_LIBS} ${LIBRARIES})
elseif(WIN32)
    target_link_libraries (iEMSSetting PRIVATE ${LIBRARIES})
endif()