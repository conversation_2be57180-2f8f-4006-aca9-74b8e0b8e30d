#include "GatewayIOT.h"

#include <iostream>
#include <cstdlib>

#include "json/json.h"

namespace GATEWAY
{
	CGatewayIOT::CGatewayIOT() :
		mqttClient_(nullptr),
		connect_(false)
	{
	}

	CGatewayIOT::~CGatewayIOT()
	{
	}

	bool CGatewayIOT::initial(GatewayConfig& cfg)
	{
		CGateway::initial(cfg);

		brokerUri_ = cfg["BrokerUri"];

		//inner
		mqttClient_ = dynamic_cast<COMM::ICommMqtt*>(COMM::CFactory::getInstance().produce("CommMqtt"));
		mqttClient_->configure(getGatewayId(), brokerUri_);
		mqttClient_->setCallback(this);

		//DataUploadModule
		{
			Json::Value cfg, root;
			cfg["BrokerUri"] = brokerUri_;
			cfg["GatewayName"] = getGatewayId();
			cfg["Username"] = cfg["username"];
			cfg["Password"] = cfg["password"];
			root["OutToIOT"] = std::move(cfg);
			initDataUploadModule(std::make_shared<Json::Value>(root));
		}

		return true;
	}

	bool CGatewayIOT::uninitial(void)
	{
		if (mqttClient_)
		{
			mqttClient_->disconnect();
			COMM::CFactory::getInstance().destory(mqttClient_);
			mqttClient_ = nullptr;
		}

		CGateway::uninitial();
		return true;
	}

	bool CGatewayIOT::start()
	{
		CGateway::start();
		//startTest(); return true;
		// Start the connection.
		// When completed, the callback will subscribe to topic.
		mqttClient_->connect(100);
		return true;
	}

	bool CGatewayIOT::stop()
	{
		mqttClient_->disconnect();
		CGateway::stop();
		return true;
	}

	void CGatewayIOT::onEngineStatus(const std::string& channelName, const DRIVER::SStatus& status)
	{

	}

	void CGatewayIOT::onEngineOnline(const std::string& channelName, bool online)
	{

	}

	void CGatewayIOT::onEngineData(const std::string& channelName, DRIVER::ChannelDataSetSptr dataSetSptr)
	{
		CGateway::onEngineData(channelName, dataSetSptr);
	}

	void CGatewayIOT::onEngineHistoryData(const std::string& channelName, DRIVER::ChannelHistoryDataSetSptr dataSetSptr)
	{

	}

	void CGatewayIOT::onEngineEvent(const std::string& channelName, const std::string& deviceName, SEventSptr eventSptr)
	{
	}

	void CGatewayIOT::onEngineControlFeedback(const std::string& channelName, const SControlFeedback& feedback)
	{

	}

	void CGatewayIOT::onEngineUpdateDeviceOffline(const std::string& channelName, bool isOffline)
	{

	}

	void CGatewayIOT::onEngineUpdateDeviceScanoff(const std::string& channelName, std::string& deviceName, bool scanoff)
	{
	}

	void CGatewayIOT::onEngineUpdateDeviceInhibit(const std::string& channelName, std::string& deviceName, bool inhibit)
	{
	}

	void CGatewayIOT::onEngineIEMSLedBrightness(const std::string& deviceName, int brightness)
	{
	}

	void CGatewayIOT::connected()
	{
		connect_ = true;
		std::cout << "on connected" << std::endl;
		std::string topic = getGatewayId() + "/" + MQTT_PUSHCONFIG;
		mqttClient_->subscribe(topic);
		topic = getGatewayId() + "/" + MQTT_CONTROL;
		mqttClient_->subscribe(topic);
		topic = getGatewayId() + "/" + "singleControl";
		mqttClient_->subscribe(topic);
		topic = getGatewayId() + "/" + "multiControl";
		mqttClient_->subscribe(topic);

		//request config
		topic = getGatewayId() + "/" + MQTT_PULLCONFIG;
		std::shared_ptr<Json::Value> payload = std::make_shared<Json::Value>();
		(*payload)[PROTOCAL_HEAD1] = 1;
		(*payload)[PROTOCAL_HEAD2] = 1;
		mqttClient_->publish(topic, payload->toStyledString(),0,0);
	}

	void CGatewayIOT::disconnected()
	{

	}

	void CGatewayIOT::connectFailed()
	{
		connect_ = false;
	}

	void CGatewayIOT::connectionLost()
	{
		connect_ = false;
		std::cout << "\nConnection lost" << std::endl;
		std::cout << "Reconnecting..." << std::endl;
		std::this_thread::sleep_for(std::chrono::milliseconds(2000));
		mqttClient_->connect(100);
	}


	void CGatewayIOT::messageArrived(const std::string& topic, std::string&& payload)
	{
		std::string gatewayId = topic.substr(0, topic.rfind("/"));
		if (gatewayId != getGatewayId())
			return;
		std::string protocolType = topic.substr(topic.rfind("/") + 1, topic.length());
		JSONCPP_STRING err;
		Json::Value root;
		Json::CharReaderBuilder builder;
		std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
		if (!reader->parse(payload.c_str(), payload.c_str() + payload.size(), &root, &err))
		{
			std::cout << "parse comming message error = " << err << std::endl;
			return;
		}

		if (protocolType == MQTT_PUSHCONFIG)
		{
			if (root[PROTOCAL_HEAD1].asInt() == 1)	//notify config is changed
			{
				//request config
				std::string topic = getGatewayId() + "/" + MQTT_PULLCONFIG;
				std::shared_ptr<Json::Value> payload = std::make_shared<Json::Value>();
				(*payload)[PROTOCAL_HEAD1] = 1;
				(*payload)[PROTOCAL_HEAD2] = 1;
				mqttClient_->publish(topic, payload->toStyledString(),0,0);
			}
			else if (root[PROTOCAL_HEAD1].asInt() == 2) //new config
			{
				//interpret cfg
				std::map<std::string, DRIVER::SChannelConfig> sccs;
				{
					Json::Value gatewayInfo;
					if (root.removeMember(getGatewayId(), &gatewayInfo) == false)
					{
						return;
					}

					Json::Value links;
					gatewayInfo.removeMember("devices", &links);
					for (auto linkIter = links.begin(); linkIter != links.end(); linkIter++)
					{
						DRIVER::SChannelConfig slc;
						{
							Json::Value channelProtocol;
							linkIter->removeMember("protocol", &channelProtocol);
							slc.driverType = channelProtocol["driverType"].asString();
							//slc.pollCycle = channelProtocol["pollCycle"].asInt();
							for (auto lpIter= channelProtocol.begin();  lpIter != channelProtocol.end(); lpIter++)
							{
								if(!lpIter->isObject())
									slc.channelProtocol[lpIter.name()] = lpIter->asString();
							}
						}
						
						Json::Value devices;
						linkIter->removeMember("devices", &devices);
						for (auto deviceIter = devices.begin(); deviceIter != devices.end(); deviceIter++)
						{
							DRIVER::SDeviceConfig sdc;
							Json::Value state;
							(*deviceIter)["protocol"].removeMember("ctrl");
							(*deviceIter)["protocol"].removeMember("state", &state);
							for (auto pointIter = state.begin(); pointIter != state.end(); pointIter++)
							{
								//todo extract "coeff" and "added"
								DRIVER::SPointConfig spc;
								for (auto ppIter = pointIter->begin(); ppIter != pointIter->end(); ppIter++)
								{
									spc.pointProtocol[ppIter.name()] = ppIter->asString();
								}
								sdc.pointTable[pointIter.name()] = std::move(spc);
							}
							slc.deviceTable[deviceIter.name()] = std::move(sdc);
						}
						sccs[linkIter.name()] = std::move(slc);
					}
				}
				
				configure(sccs);
			}
				
		}
		else if (protocolType == MQTT_CONTROL)
		{
			Json::Value protocol;
			if (!reader->parse(root["protocol"].asString().c_str(), root["protocol"].asString().c_str() + root["protocol"].asString().size(), &protocol, &err))
			{
				std::cout << "parse control protocol error = " << err << std::endl;
				return;
			}

			for (auto iter = protocol.begin(); iter != protocol.end(); ++iter)
			{
				Json::Value ctrlInfo;
				ctrlInfo["timeout"] = 1;
				ctrlInfo["deviceName"] = root["url"];
				ctrlInfo["pointName"] = iter.name();
				ctrlInfo["value"] = *iter;
				//control(std::make_shared<Json::Value>(ctrlInfo));
			}
		}
		else
		{

		}
	}

} //namespace GATEWAY end