#pragma once
#include <vector>
#include <map>
#include <memory>
#include <unordered_map>
#include <thread>

#include "EMSBOXTool.h"

#include "IDriver.h"
#include "ICommModbus.h"
#include "ACommFactory.h"

enum EModBusConnectType
{
	MBCT_Unknow,
	MBCT_TCP,
	MBCT_RTU,
};

namespace DRIVER
{
	struct SDriverEMSBoxModbus_build202528ChannelProtocol : public IProtocol
	{
		// tcp
		std::string serverIp;
		int serverPort;
		//rtu
		std::string deviceName;
		int baudRate;
		char parity;
		int dataBit;
		int stopBit;

		int maxClients;
		int timeoutMs;
	};

	enum EModbusAddrType
	{
		eOutputCoils = 0,		// Read/Write
		eInputCoils = 1,		// Read Only
		eInternalRegisters = 3, // Read Only
		eHoldingRegisters = 4	// Read/Write
	};

	struct SDriverEMSBoxModbus_build202528PointProtocol : public IProtocol
	{
		int slaveId;
		EModbusAddrType addrType;
		int address;
		std::string valueType;
		char swapMode;			//byte and word swap : a,  byte swap : b, word swap : w, default : n
		int8_t offset;
		int8_t bits;

		int mixAddress;
		std::string key;

		std::string deviceId;
	};

	struct SContiguousAddr 
	{
		const int minBitAddr;
		const int minInputBitAddr;
		const int minInputRegisterAddr;
		const int minRegisterAddr;
		const int maxBitAddr;
		const int maxInputBitAddr;
		const int maxInputRegisterAddr;
		const int maxRegisterAddr;
		SContiguousAddr(int minBitAddr, int minInputBitAddr, int minInputRegisterAddr, int minRegisterAddr,
			int maxBitAddr, int maxInputBitAddr, int maxInputRegisterAddr, int maxRegisterAddr) :
			minBitAddr(minBitAddr), minInputBitAddr(minInputBitAddr), minInputRegisterAddr(minInputRegisterAddr), minRegisterAddr(minRegisterAddr),
			maxBitAddr(maxBitAddr), maxInputBitAddr(maxInputBitAddr), maxInputRegisterAddr(maxInputRegisterAddr), maxRegisterAddr(maxRegisterAddr)
		{}
	};
	struct SContiguousDataTable
	{
		std::string maxRegistersValueType;
		std::string maxInputRegistersValueType;
		SContiguousAddr *scaddr;
		int minBitAddr;
		int minInputBitAddr;
		int minInputRegisterAddr;
		int minRegisterAddr;
		int maxBitAddr;
		int maxInputBitAddr;
		int maxInputRegisterAddr;
		int maxRegisterAddr;
		std::vector<uint8_t> tableBits;
		std::vector<uint8_t> tableInputBits;
		std::vector<uint16_t> tableInputRegisters;
		std::vector<uint16_t> tableRegisters;

		SContiguousDataTable() : minBitAddr(65535), minInputBitAddr(65535), minInputRegisterAddr(65535),
			minRegisterAddr(65535), maxBitAddr(-65535), maxInputBitAddr(-65535), maxInputRegisterAddr(-65535), maxRegisterAddr(-65535), scaddr(nullptr) {}
		
		~SContiguousDataTable()
		{
			if (scaddr)
				delete scaddr;
		}
	};

	struct SNonContiguousDataIndexTable
	{
		struct SSegment
		{
			int minAddr;
			int maxAddr;
		};

		std::vector<SSegment> bits;
		std::vector<SSegment> inputBits;
		std::vector<SSegment> registers;
		std::vector<SSegment> inputRegisters;
	};

	struct SIEMSDeviceArg 
	{
		int slaveId;
		// generic
		bool isContiguousAddress;
		char swapMode;			//byte and word swap : a,  byte swap : b, word swap : w, default : n
		int quantityPerRequest;
		int requestInterval;
		int baseAddress;

		SIEMSDeviceArg(): slaveId(255), isContiguousAddress(true),
			swapMode('n'), quantityPerRequest(120),
			requestInterval(1000),baseAddress(0)
		{}
	};

	class CDriverEMSBoxModbus_build202528 : public IDriver
	{
	protected:
		EFlag flag_;
		IDriverCallback* cb_;
		COMM::ICommModbus* modbusClient_;
		EModBusConnectType mbct_;
		int errCount_;
		int timeoutMs_;

		bool isFirstOpen_;

		std::unordered_map<std::string, std::vector<SDriverEMSBoxModbus_build202528PointProtocol*>> devicePointsMap_;
		std::unordered_map<std::string, std::unordered_map<int, SContiguousDataTable>> deviceContiguousDataTables_;
		std::unordered_map<std::string, std::unordered_map<int, SNonContiguousDataIndexTable>> deviceNonContiguousDataIndexTables_;
		std::unordered_map<std::string, SIEMSDeviceArg> deviceIsContiguous_;
		std::unordered_map<std::string, SIEMSDeviceArg>::iterator deviceIter_;

		SValue data2svalue(const uint16_t* data, const std::string& valueType, char swapMode, int offset = -1);
		EStatusCode requestContiguousData(std::string deviceId);
		EStatusCode requestNonContiguousData(std::string deviceId);
		bool parseData(const SDriverEMSBoxModbus_build202528PointProtocol* pointProtocol, std::string deviceId, const SIEMSDeviceArg& config, SValue& value);

		SDriverEMSBoxModbus_build202528PointProtocol* createPointProtocol(OriginProtocol& originProtocol);
		
		void logIEMSPacket(std::string& deviceID, std::vector<uint8_t>& send_data, std::vector<uint8_t>& recv_data);

		std::string ledName;
		std::thread* ledControlThread_ = nullptr;
		bool exitLedControl_ = false;
	public:
		void modbusLog(std::string str);
		void controlLedThread();

	public:
		CDriverEMSBoxModbus_build202528();
		virtual ~CDriverEMSBoxModbus_build202528();

		//IDriver
		virtual bool open(const SProtocolNode& pn);
		virtual bool close(const SProtocolNode& pn);
		virtual EStatusCode control(const IProtocol* const channelProtocol, const IProtocol* const deviceProtocol, const IProtocol* const pointProtocol, const SControlInfo& controlInfo);
		virtual EStatusCode controlSet(const std::unordered_map<std::string, std::vector<SControlSetInfo>>& controlValues);
		virtual void setCallback(IDriverCallback* cb);
		virtual EStatusCode poll(const SProtocolNode& pn);
		virtual std::string getPointKey(const IProtocol* const channelProtocol, const IProtocol* const deviceProtocol, const IProtocol* const pointProtocol);
		virtual IProtocol* createProtocol(EProtocolType type, OriginProtocol& originProtocol);
		virtual void destoryProtocol(EProtocolType type, IProtocol* protocol);
		virtual void setFlag(EFlag flag);
	};

}//namespace DRIVER end
