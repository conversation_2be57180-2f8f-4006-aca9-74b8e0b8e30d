{"name": "ModbusTCP(Build-202316)", "type": "empty", "category": "Modbus协议", "categoryNumber": 5, "scriptType": "c++", "systemClass": 1, "information": "Modbus是一种串行通信协议，是Modicon公司（现在的施耐德电气Schneider Electric）于1979年为使用可编程逻辑控制器（PLC）通信而发表。Modbus已经成为工业领域通信协议的业界标准（De facto），并且现在是工业电子设备之间常用的连接方式。", "version": 1, "iemsProtocolClass": "modbus_tcp", "iemsProtocolInterfaceType": "tcp", "argument": [{"name": "IsContiguousAddress", "label": "组包模式", "description": "组包模式", "required": true, "type": "string", "visible": true, "defaultValue": "true", "inputType": "select", "items": [{"text": "模式一", "value": "true"}, {"text": "模式二", "value": "false"}], "_level": 2}, {"name": "QuantityPerRequest", "label": "单次请求个数", "description": "一次请求寄存器的个数", "required": true, "type": "number", "inputType": "numberInput", "visible": true, "min": 1, "max": 120, "defaultValue": 120, "_level": 2}, {"name": "RequestInterval", "label": "采集间隔", "description": "请求时间间隔,单位：毫秒", "required": true, "type": "number", "inputType": "numberInput", "visible": true, "min": 0, "max": 60000, "defaultValue": 0, "_level": 2}, {"name": "BaseAddress", "label": "起始地址", "description": "请求的起始地址,0或者1", "required": true, "type": "number", "visible": true, "defaultValue": 0, "inputType": "select", "items": [{"text": "0", "value": 0}, {"text": "1", "value": 1}], "_level": 2}, {"name": "SlaveId", "label": "从站号", "description": "从站地址,从0开始，如无填255", "required": true, "type": "number", "inputType": "numberInput", "min": 0, "max": 255, "precision": 0, "defaultValue": 255, "_level": 2}, {"name": "Address", "label": "寄存器地址", "description": "寄存器地址", "required": true, "type": "string", "inputType": "input", "defaultValue": 0, "_level": 3}, {"name": "ByteOrder", "label": "大小端交换", "description": "大小端交换模式，不填写默认为ABCD模式", "required": true, "type": "string", "defaultValue": "ABCD", "inputType": "select", "items": [{"text": "AB模式", "value": "AB"}, {"text": "BA模式", "value": "BA"}, {"text": "ABCD模式", "value": "ABCD"}, {"text": "CDAB模式", "value": "CDAB"}, {"text": "BADC模式", "value": "BADC"}, {"text": "DCBA模式", "value": "DCBA"}], "_level": 2}, {"name": "ValueType", "label": "值类型", "description": "数值类型", "required": true, "type": "string", "defaultValue": "Int16", "inputType": "select", "items": [{"text": "Boolean", "value": "Boolean"}, {"text": "Int16", "value": "Int16"}, {"text": "UInt16", "value": "UInt16"}, {"text": "Int32", "value": "Int32"}, {"text": "UInt32", "value": "UInt32"}, {"text": "Float", "value": "Float"}], "_level": 4}], "attribute": [{"code": "_online", "name": "在线状态", "type": "DI", "value": {"ON": "在线", "OFF": "离线"}, "history": {"change": false}, "pointType": "internalState", "description": "设备在线状态"}], "classType": "link", "connection": [{"name": "ServerIp", "label": "地址", "description": "服务器ip地址", "required": true, "inputType": "input", "type": "string", "defaultValue": "127.0.0.1", "visible": true, "_level": 1}, {"name": "ServerPort", "label": "端口", "description": "服务器端口号", "required": true, "type": "number", "inputType": "numberInput", "visible": true, "min": 0, "max": 65535, "defaultValue": 502, "_level": 1}, {"name": "TimeoutMs", "label": "超时时间", "description": "连接超时时间", "required": true, "type": "number", "inputType": "numberInput", "visible": true, "min": 0, "max": 60000, "defaultValue": 0, "_level": 1}], "driverFile": {"Linux x86_64": "DriverEMSBoxModbusRtu_build202528"}, "description": "ModbusTCP标准驱动", "creationTime": 1682045469078, "systemVersion": "0.0.1", "modificationTime": 1682045469078, "argumentAscription": "point"}