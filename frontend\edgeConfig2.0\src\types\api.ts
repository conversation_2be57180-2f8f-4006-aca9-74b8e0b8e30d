// API响应基础接口
export interface ApiResponse<T = any> {
  result: {
    resultCode: string;
    resultError?: string;
  };
  data: T;
}

// OPC服务器状态接口
export interface OPCServerState {
  state: boolean;
  endpoint: string;
}

// 点位数据接口
export interface PointData {
  code: string;
  name: string;
  value: any;
  quality: string[];
  timestamp: number;
  protocol?: Array<{
    name: string;
    value: string;
    level: number;
  }>;
}

// 组态配置接口
export interface ConfigurationData {
  link: {
    [key: string]: {
      instances: Array<{
        id: string;
        name: string;
        devices: Array<{
          id: string;
          name: string;
          points: PointData[];
        }>;
      }>;
    };
  };
}

// 设备模板接口
export interface DeviceTemplate {
  id: string;
  name: string;
  description?: string;
  createTime?: string;
  updateTime?: string;
  points?: PointData[];
}

// 设备模板列表响应
export interface DeviceTemplateListResponse extends ApiResponse<{
  templates: DeviceTemplate[];
}> {}

// 点位列表响应
export interface PointListResponse extends ApiResponse<{
  points: PointData[];
}> {}

// 组态配置响应
export interface ConfigurationResponse extends ApiResponse<ConfigurationData> {} 