#pragma once
#ifndef _GATEWAY_TEST_H_
#define _GATEWAY_TEST_H_

#include <string>

#include "Gateway.h"
#include "AGatewayFactory.h"
#include "Gateway.h"


namespace GATEWAY
{
	class CGatewayTest : public CGateway
	{
	private:
		std::mutex gatewayMutex_;
		std::string cfgPath_;

	public:
		CGatewayTest();
		virtual ~CGatewayTest();

		//Gateway
		virtual bool initial(GatewayConfig& cfg) override;
		virtual bool uninitial(void) override;
		virtual bool start() override;
		virtual bool stop() override;

		virtual void onEngineStatus(const std::string& channelName, const DRIVER::SStatus& status);
		virtual void onEngineOnline(const std::string& channelName, bool online);
		virtual void onEngineData(const std::string& channelName, DRIVER::ChannelDataSetSptr dataSetSptr);
		virtual void onEngineHistoryData(const std::string& channelName, DRIVER::ChannelHistoryDataSetSptr dataSetSptr);
		virtual void onEngineEvent(const std::string& channelName, const std::string& deviceName, SEventSptr eventSptr);
		virtual void onEngineControlFeedback(const std::string& channelName, const SControlFeedback& feedback);
		virtual void onEngineUpdateDeviceOffline(const std::string& channelName, bool isOffline) override;
		virtual void onEngineUpdateDeviceScanoff(const std::string& channelName, std::string& deviceName, bool scanoff) override;
		virtual void onEngineUpdateDeviceInhibit(const std::string& channelName, std::string& deviceName, bool inhibit) override;
		virtual void onEngineIEMSLedBrightness(const std::string& deviceName, int brightness) override;
	};

	REGISTER_GATEWAY(CGatewayTest, "GatewayTest")

} //namespace GATEWAY end


#endif