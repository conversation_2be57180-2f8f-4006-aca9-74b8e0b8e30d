#include "CommModbus.h"

#include <iostream>
#include <stdlib.h>

namespace COMM
{
	CCommModbus::CCommModbus() :
		ctx_(nullptr),
		tableBitsSize_(-1),
		tableInputBitsSize_(-1),
		tableInputRegistersSize_(-1),
		tableRegistersSize_(-1),
		byteTimeoutSec_(5),
		byteTimeoutUsec_(0),
		responseTimeoutSec_(8),
		responseTimeoutUsec_(0),
		logCb_(nullptr)
	{
	}

	CCommModbus::~CCommModbus()
	{
		modbus_free(ctx_);
	}

	bool CCommModbus::getSpaceSize()
	{
		uint8_t* bitsBuf = new uint8_t[MODBUS_MAX_READ_BITS];
		int nb = MODBUS_MAX_READ_BITS;
		int max = MODBUS_MAX_READ_BITS;
		while (true)
		{
			int rc = modbus_read_bits(ctx_, 0, max, bitsBuf);
			if (rc > 0)
			{

			}
		}
		

		
	}

	std::string CCommModbus::getLastError()
	{
		return lastError_;
	}

	void CCommModbus::setup(const std::string& ip, int port)
	{
		if (ctx_)
		{
			modbus_free(ctx_);
			ctx_ = nullptr;
		}

		ctx_ = modbus_new_tcp(ip.c_str(), port);
	}

	void CCommModbus::setup(const std::string& port, int baudrate, int dataBits, char parity, int stopBits)
	{
		if (ctx_)
		{
			modbus_free(ctx_);
			ctx_ = nullptr;
		}
		std::string portRaw = port;

#if defined(_MSC_VER) || defined(__MINGW32__)
		if (portRaw.length() < 4)
		{
			return;
		}
		int portNum = atoi(portRaw.substr(3, portRaw.size()).c_str());
		if (portNum > 9)
		{
			portRaw.insert(0, "\\\\.\\");
		}
#endif // WIN

		ctx_ = modbus_new_rtu(portRaw.c_str(), baudrate, parity, dataBits, stopBits);
	}

	void CCommModbus::setByteTimeout(int sec, int usec)
	{
		byteTimeoutSec_ = sec;
		byteTimeoutUsec_ = usec;
	}

	void CCommModbus::setResponseTimeout(int sec, int usec)
	{
		responseTimeoutSec_ = sec;
		responseTimeoutUsec_ = usec;
	}

	bool CCommModbus::connect()
	{
		bool ret = true;
		modbus_set_byte_timeout(ctx_, byteTimeoutSec_, byteTimeoutUsec_);
		modbus_set_response_timeout(ctx_, responseTimeoutSec_, responseTimeoutUsec_);
		//modbus_set_debug(ctx_, 1);
		if (modbus_connect(ctx_) == -1) 
		{
			lastError_ = modbus_strerror(errno);
			modbus_free(ctx_);
			ctx_ = nullptr;
			ret = false;
		}
		else
		{
			//modbus_set_error_recovery(ctx_, MODBUS_ERROR_RECOVERY_LINK);
		}
		
		return ret;
	}

	void CCommModbus::disconnect()
	{
		modbus_close(ctx_);
	}

	void CCommModbus::setSlaveId(int slaveId)
	{
		modbus_set_slave(ctx_, slaveId);
		//getSpaceSize();
	}

	int CCommModbus::readBits(int addr, int nb, std::vector<uint8_t>& data)
	{
		if (data.size() != nb)
		{
			data.resize(nb);
		}
		int rc = modbus_read_bits(ctx_, addr, nb, data.data());
		if (rc < 0)
		{
			lastError_ = modbus_strerror(errno);
			if (errno == 0 || errno == EBADF || errno == ECONNRESET || errno == EPIPE)
			{
				rc = ERR_LINK;
			}
		}
		else
		{
			//onLog(data);
			onLog("readBits", "recv", std::to_string(addr), data);
		}
		return rc;
	}

	int CCommModbus::readInputBits(int addr, int nb, std::vector<uint8_t>& data)
	{
		if (data.size() != nb)
		{
			data.resize(nb);
		}
		int rc = modbus_read_input_bits(ctx_, addr, nb, data.data());
		if (rc < 0)
		{
			lastError_ = modbus_strerror(errno);
			if (errno == 0 || errno == EBADF || errno == ECONNRESET || errno == EPIPE)
			{
				rc = ERR_LINK;
			}
		}
		else
		{
			//onLog(data);
			onLog("readInputBits", "recv", std::to_string(addr), data);
		}
		return rc;
	}

	int CCommModbus::readRegisters(int addr, int nb, std::vector<uint16_t>& data)
	{
		if (data.size() != nb)
		{
			data.resize(nb);
		}
		int rc = modbus_read_registers(ctx_, addr, nb, data.data());
		if (rc < 0)
		{
			lastError_ = modbus_strerror(errno);
			if (errno == 0 || errno == EBADF || errno == ECONNRESET || errno == EPIPE)
			{
				rc = ERR_LINK;
			}
		}
		else
		{
			std::vector<uint8_t> data8;
			for (auto& val : data) {
				data8.push_back(val >> 8);
				data8.push_back(val & 0xff);
			}
			onLog("readRegisters", "recv", std::to_string(addr), data8);
		}
		return rc;
	}

	int CCommModbus::readInputRegisters(int addr, int nb, std::vector<uint16_t>& data)
	{
		if (data.size() != nb)
		{
			data.resize(nb);
		}
		int rc = modbus_read_input_registers(ctx_, addr, nb, data.data());
		if (rc < 0)
		{
			lastError_ = modbus_strerror(errno);
			if (errno == 0 || errno == EBADF || errno == ECONNRESET || errno == EPIPE)
			{
				rc = ERR_LINK;
			}
		}
		else
		{
			std::vector<uint8_t> data8;
			for (auto& val : data) {
				data8.push_back(val >> 8);
				data8.push_back(val & 0xff);
			}
			onLog("readInputRegisters", "recv", std::to_string(addr), data8);
		}
		return rc;
	}

	int CCommModbus::writeBit(int addr, bool status)
	{
		int rc = modbus_write_bit(ctx_, addr, status);
		if (rc < 0)
		{
			lastError_ = modbus_strerror(errno);
			if (errno == 0 || errno == EBADF || errno == ECONNRESET || errno == EPIPE)
			{
				rc = ERR_LINK;
			}
		}
		return rc;
	}

	int CCommModbus::writeRegister(int addr, const uint16_t value)
	{
		int rc = modbus_write_register(ctx_, addr, value);
		if (rc < 0)
		{
			lastError_ = modbus_strerror(errno);
			if (errno == 0 || errno == EBADF || errno == ECONNRESET || errno == EPIPE)
			{
				rc = ERR_LINK;
			}
		}
		return rc;
	}

	int CCommModbus::writeBits(int addr, const std::vector<uint8_t>& data)
	{
		int rc = modbus_write_bits(ctx_, addr, data.size(), data.data());
		if (rc < 0)
		{
			lastError_ = modbus_strerror(errno);
			if (errno == 0 || errno == EBADF || errno == ECONNRESET || errno == EPIPE)
			{
				rc = ERR_LINK;
			}
		}
		return rc;
	}

	int CCommModbus::writeRegisters(int addr, const std::vector<uint16_t>& data)
	{
		int rc = modbus_write_registers(ctx_, addr, data.size(), data.data());
		if (rc < 0)
		{
			lastError_ = modbus_strerror(errno);
			if (errno == 0 || errno == EBADF || errno == ECONNRESET || errno == EPIPE)
			{
				rc = ERR_LINK;
			}
		}
		return rc;
	}

	void CCommModbus::setLogCallBack(DRIVER::IDriverCallback* cb)
	{
		logCb_ = cb;
	}

	void CCommModbus::onLog(const std::vector<uint8_t>& data)
	{
		if (logCb_)
		{
			logCb_->onLog(DRIVER::ELogLevel::eLogPacket, bytesToHexString(data.data(), data.size()));
		}
	}

	void CCommModbus::onLog(const std::string& func, const std::string& type, const std::string& addr, const std::vector<uint8_t>& data)
	{
		if (logCb_)
		{
			logCb_->onLog(DRIVER::ELogLevel::eLogPacket, "[" + func + "] [" + type + "] [" + addr + "] [ " + bytesToHexString(data.data(), data.size()) + "]");
		}
	}

	int CCommModbus::readBits(int addr, int nb, std::vector<uint8_t>& data, std::vector<uint8_t>& packet_send, std::vector<uint8_t>& packet_recv)
	{
		if (data.size() != nb)
		{
			data.resize(nb);
		}
		uint8_t send_data[COMM_MODBUS_MIN_REQ_LENGTH];
		int send_len = -1;
		uint8_t recv_data[COMM_MODBUS_MAX_MESSAGE_LENGTH];
		int recv_len = -1;
		int rc = modbus_read_bits_data(ctx_, addr, nb, data.data(), send_data, &send_len, recv_data, &recv_len);
		if (rc < 0)
		{
			lastError_ = modbus_strerror(errno);
			if (errno == 0 || errno == EBADF || errno == ECONNRESET || errno == EPIPE)
			{
				rc = ERR_LINK;
			}
		}
		if (send_len > 0)
			packet_send = { send_data, send_data + send_len };
		if (recv_len > 0)
			packet_recv = { recv_data, recv_data + recv_len };

		//std::cout << "---------------------------------------------" << std::endl;
		//if (packet_send.size() > 0)
		//	std::cout << bytesToHexString(packet_send.data(), packet_send.size()) << std::endl;
		//if (packet_recv.size() > 0)
		//	std::cout << bytesToHexString(packet_recv.data(), packet_recv.size()) << std::endl;
		//std::cout << "---------------------------------------------" << std::endl;

		return rc;
	}

	int CCommModbus::readInputBits(int addr, int nb, std::vector<uint8_t>& data, std::vector<uint8_t>& packet_send, std::vector<uint8_t>& packet_recv)
	{
		if (data.size() != nb)
		{
			data.resize(nb);
		}
		uint8_t send_data[COMM_MODBUS_MIN_REQ_LENGTH];
		int send_len = -1;
		uint8_t recv_data[COMM_MODBUS_MAX_MESSAGE_LENGTH];
		int recv_len = -1;
		int rc = modbus_read_input_bits_data(ctx_, addr, nb, data.data(), send_data, &send_len, recv_data, &recv_len);
		if (rc < 0)
		{
			lastError_ = modbus_strerror(errno);
			if (errno == 0 || errno == EBADF || errno == ECONNRESET || errno == EPIPE)
			{
				rc = ERR_LINK;
			}
		}

		if (send_len > 0)
			packet_send = { send_data, send_data + send_len };
		if (recv_len > 0)
			packet_recv = { recv_data, recv_data + recv_len };

		//std::cout << "---------------------------------------------" << std::endl;
		//if (packet_send.size() > 0)
		//	std::cout << bytesToHexString(packet_send.data(), packet_send.size()) << std::endl;
		//if (packet_recv.size() > 0)
		//	std::cout << bytesToHexString(packet_recv.data(), packet_recv.size()) << std::endl;
		//std::cout << "---------------------------------------------" << std::endl;

		return rc;
	}

	int CCommModbus::readRegisters(int addr, int nb, std::vector<uint16_t>& data, std::vector<uint8_t>& packet_send, std::vector<uint8_t>& packet_recv)
	{
		if (data.size() != nb)
		{
			data.resize(nb);
		}
		uint8_t send_data[COMM_MODBUS_MIN_REQ_LENGTH];
		int send_len = -1;
		uint8_t recv_data[COMM_MODBUS_MAX_MESSAGE_LENGTH];
		int recv_len = -1;
		int rc = modbus_read_registers_data(ctx_, addr, nb, data.data(), send_data, &send_len, recv_data, &recv_len);
		if (rc < 0)
		{
			lastError_ = modbus_strerror(errno);
			if (errno == 0 || errno == EBADF || errno == ECONNRESET || errno == EPIPE)
			{
				rc = ERR_LINK;
			}
		}

		if (send_len > 0)
			packet_send = { send_data, send_data + send_len };
		if (recv_len > 0)
			packet_recv = { recv_data, recv_data + recv_len };

		//std::cout << "---------------------------------------------" << std::endl;
		//if (packet_send.size() > 0)
		//	std::cout << bytesToHexString(packet_send.data(), packet_send.size()) << std::endl;
		//if (packet_recv.size() > 0)
		//	std::cout << bytesToHexString(packet_recv.data(), packet_recv.size()) << std::endl;
		//std::cout << "---------------------------------------------" << std::endl;

		return rc;
	}

	int CCommModbus::readInputRegisters(int addr, int nb, std::vector<uint16_t>& data, std::vector<uint8_t>& packet_send, std::vector<uint8_t>& packet_recv)
	{
		if (data.size() != nb)
		{
			data.resize(nb);
		}
		uint8_t send_data[COMM_MODBUS_MIN_REQ_LENGTH];
		int send_len = -1;
		uint8_t recv_data[COMM_MODBUS_MAX_MESSAGE_LENGTH];
		int recv_len = -1;
		int rc = modbus_read_input_registers_data(ctx_, addr, nb, data.data(), send_data, &send_len, recv_data, &recv_len);
		if (rc < 0)
		{
			lastError_ = modbus_strerror(errno);
			if (errno == 0 || errno == EBADF || errno == ECONNRESET || errno == EPIPE)
			{
				rc = ERR_LINK;
			}
		}

		if (send_len > 0)
			packet_send = { send_data, send_data + send_len };
		if (recv_len > 0)
			packet_recv = { recv_data, recv_data + recv_len };

		//std::cout << "---------------------------------------------" << std::endl;
		//if (packet_send.size() > 0)
		//	std::cout << bytesToHexString(packet_send.data(), packet_send.size()) << std::endl;
		//if (packet_recv.size() > 0)
		//	std::cout << bytesToHexString(packet_recv.data(), packet_recv.size()) << std::endl;
		//std::cout << "---------------------------------------------" << std::endl;

		return rc;
	}


} //namespace COMM end