BACnet Stack - Win32

This directory contains a demo program that compiles with a Win32 compiler.
It was tested with the freely downloadable Borland C++ 5.5, as well as
Borland C++ 5 and Visual C++ 6.0.

The makefile.mak file is used with the Borland command line tools.  
Run setvars.bat to configure the environment for the Borland tools.
Edit it if necessary to set the correct location of your tools.

The bacnet.ide file is used with the Borland IDE.

The bacnet directory is used with Visual C++ 6 tools, and there is a
workspace file bacnet.dsw that is used to compile the demo program.

