#pragma once

#include <iostream>
#include <string>

class CPathManager 
{
public:
	static CPathManager& getInstance()
	{
		static CPathManager instance;
		return instance;
	}

	void initialize(const std::string& launchPath);
	void initialize(const std::string& driverXId,
		const std::string& listenPort,
		const std::string& managerPort,
		const std::string& launchPath);

	std::string getMainLogPath();
	std::string getDriverXConfigPath();
	std::string getEngineLogPath();
	std::string getGatewayLogPath();
	std::string getAIOTConfigPath();
	std::string getCloudConfigPath();
	std::string getCloudVersionPath();
	std::string getDownloadConfigTarName(std::string gid);
	std::string getPluginPath();
	std::string getUserInfoFile();
	std::string getChannelLogPath(std::string channelName, std::string alias);
	std::string getChannelLog(std::string channelName, std::string alias, std::string logLevel, std::string name);
	std::string getGatewayIDLogPath(std::string gid);
	std::string getCacheDBPath();
	std::string getLogDBPath();
	std::string getConfigurationDBPath();
	std::string getWebPath();
	std::string getAIOTFlagPath();
	std::string getGatewayVersion();
	std::string getCrtPath();
	std::string getManagerCrtPath();
	std::string getPackVersionFile();
	std::string getPackInfoFile();
	std::string getLocalDriverTemplatePath();
	std::string getLocalConfigPath();
	std::string getLocalConfigStatePath();

	std::string normalizePath(std::string path);


private:
	CPathManager();
	~CPathManager() = default;

	CPathManager(const CPathManager&) = delete;
	CPathManager& operator=(const CPathManager&) = delete;

private:
	bool isMultiGateway_;
	std::string driverXId_;
	std::string listenPort_;
	std::string managerPort_;
	std::string launchPath_;

	std::string mainLogPath_ = "logs/main.log";
	std::string driverXConfigPath_ = "DriverXConfig.json";
	std::string enginelogFilePath_ = "logs/DriverEngine.log";
	std::string gatewayLogFilePath_ = "logs/gateway.log";
	std::string aiotConfigFile_ = "AIOTConfig.json";
	std::string cloudConfigPath_ = "cloudConfig/";
	std::string cloudVersionPath_ = "cloudConfig/version";
	std::string pluginPath_ = "Driver";
	std::string userInfoFile_ = "UserInfo.json";
	std::string cacheDBPath_ = "caching";
	std::string logDBPath_ = "log";
	std::string configurationDBPath_ = "configuration";
	std::string webPath_ = "scanner_web";
	std::string aiotFlagPath_ = "aiotflag";
	std::string gatewayVersion_ = "file_list.xml";
	std::string crtPath_ = "crt/";
	std::string managerCrtPath_ = "ManageConfig.json";
	std::string packVersionFile_ = "version";
	std::string packInfoFile_ = "packageInfo";
	std::string localDriverTemplatePath_ = "DriverTemplate";
	std::string localConfigPath_ = "LocalConfig.json";
	std::string localConfigStatePath_ = "LocalConfigState.json";
};