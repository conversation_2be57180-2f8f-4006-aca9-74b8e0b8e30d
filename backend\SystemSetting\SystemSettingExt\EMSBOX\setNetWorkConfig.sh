#!/bin/bash
# 用法：./setNetWorkConfig.sh "conn_name=eth0,new_ip=***********4,netmask=*************,gateway=***********,dns=**********"
# 注意：gateway和dns参数可以为空，如：gateway=,dns=
# DNS支持多个服务器，用空格分隔，如：dns=*********** ***********

# 定义十进制转二进制函数（无bc依赖）
decimal_to_bin() {
    local n=$1
    local bin=""
    for ((i=7; i>=0; i--)); do
        mask=$((1 << i))
        bin+=$(( (n & mask) != 0 ? 1 : 0 ))
    done
    echo "$bin"
}

# 验证掩码有效性
validate_mask() {
    local mask_bin="$1"
    # 必须满足整体为连续的1跟随0
    [[ $mask_bin =~ ^1+0+$ ]] || return 1
    return 0
}

# 核心掩码转换函数
mask_to_cidr() {
    local mask=$1

    # 处理CIDR数字格式
    if [[ $mask =~ ^[0-9]+$ ]]; then
        (( mask >= 0 && mask <= 32 )) && echo "$mask" || return 1
        return 0
    fi

    # 校验点分十进制格式
    if ! [[ $mask =~ ^([0-9]{1,3}\.){3}[0-9]{1,3}$ ]]; then
        return 1
    fi

    local mask_bin="" cidr=0
    IFS=. read -r o1 o2 o3 o4 <<< "$mask"

    # 处理每个二进制段
    for octet in $o1 $o2 $o3 $o4; do
        (( octet < 0 || octet > 255 )) && return 1

        local octet_bin=$(decimal_to_bin "$octet")
        # 验证当前段的二进制有效（前导1没有中断）
        [[ $octet_bin =~ ^1*0*$ ]] || return 1
        
        # 记录总二进制字符串
        mask_bin+="$octet_bin"
        # 统计当前段的cidr贡献
        ones=${octet_bin%%0*}
        cidr=$((cidr + ${#ones}))
    done

    # 验证总二进制符合条件
    validate_mask "$mask_bin" || return 1

    echo "$cidr"
}

# 解析参数函数
parse_params() {
    local input="$1"
    
    # 初始化变量
    conn_name=""
    new_ip=""
    subnet_mask=""
    gateway=""
    dns=""
    
    # 按逗号分割字段
    IFS=',' read -ra FIELDS <<< "$input"
    
    for field in "${FIELDS[@]}"; do
        # 去除字段前后空格
        field=$(echo "$field" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
        
        if [[ $field =~ ^conn_name=(.*)$ ]]; then
            conn_name="${BASH_REMATCH[1]}"
        elif [[ $field =~ ^new_ip=(.*)$ ]]; then
            new_ip="${BASH_REMATCH[1]}"
        elif [[ $field =~ ^netmask=(.*)$ ]]; then
            subnet_mask="${BASH_REMATCH[1]}"
        elif [[ $field =~ ^gateway=(.*)$ ]]; then
            gateway="${BASH_REMATCH[1]}"
        elif [[ $field =~ ^dns=(.*)$ ]]; then
            dns="${BASH_REMATCH[1]}"
        fi
    done
}

#====================== 参数校验 ======================
if [ $# -ne 1 ]; then
    echo "错误：需要1个参数" >&2
    echo "格式：conn_name=值,new_ip=值,netmask=值,gateway=值,dns=值" >&2
    echo "注意：gateway和dns可以为空，如：gateway=,dns=" >&2
    exit 1
fi

# 解析参数
parse_params "$1"

# 空参数检查
check_empty() { [ -z "$1" ] && { echo "参数错误：$2 不能为空" >&2; exit 2; } }
check_empty "$conn_name" "连接名称"
check_empty "$new_ip" "IP地址"
check_empty "$subnet_mask" "子网掩码"
# 注意：gateway和dns允许为空

#====================== 掩码转换 ======================
if ! cidr=$(mask_to_cidr "$subnet_mask"); then
    echo "错误：无效的子网掩码格式 '$subnet_mask'" >&2
    echo "正确示例：24 或 *************" >&2
    exit 3
fi

#====================== 接口检查 ======================
if ! ip link show "$conn_name" &>/dev/null; then
    echo "警告：物理接口 $conn_name 不存在（可能是新建连接）" >&2
fi

#====================== 配置网络 ======================
# 清理旧配置
#nmcli con delete "$conn_name" &>/dev/null

# 构建nmcli命令
nmcli_cmd="nmcli con mod $conn_name ipv4.method manual ipv4.addresses \"${new_ip}/${cidr}\""

# 添加网关（如果不为空）
if [[ -n "$gateway" ]]; then
    nmcli_cmd+=" ipv4.gateway $gateway"
fi

# 添加DNS（如果不为空）
if [[ -n "$dns" ]]; then
    # 处理多个DNS服务器（空格分隔转换为逗号分隔）
    dns_formatted=$(echo "$dns" | sed 's/[[:space:]]\+/,/g')
    nmcli_cmd+=" ipv4.dns $dns_formatted"
fi

# 执行配置命令
if ! eval "$nmcli_cmd" &>/dev/null; then
    echo "错误：网络配置失败（请检查IP是否冲突）" >&2
    exit 4
fi

# 激活连接（带重试）
retry_count=3
for ((i=1; i<=retry_count; i++)); do
    if nmcli con up $conn_name &>/dev/null; then
        echo "配置成功！${conn_name} [${new_ip}/${cidr}]" >&2
        exit 0
    fi
    sleep 1
done

#echo "错误：无法激活连接（检查物理链路）" >&2
#nmcli con delete "$conn_name" &>/dev/null
#exit 5
