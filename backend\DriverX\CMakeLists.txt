﻿# CMakeList.txt : CMake project for DriverXGateway, include source and define
# project specific logic here.
#

# do not specify the project language
project (DriverX VERSION 1.0)

aux_source_directory(./ SRCS)

if(DEBUG_DRIVERX)
    add_definitions(-DDEBUG_DRIVERX)
endif(DEBUG_DRIVERX)

add_definitions(-DCPPHTTPLIB_OPENSSL_SUPPORT)
# quickjs
add_definitions(-DCONFIG_VERSION="2021-03-27")
add_definitions(-D_GNU_SOURCE)

set(EXECUTABLE_OUTPUT_PATH ${OUTPUT_DIR})
set(CMAKE_BINARY_DIR ${BUILD_DIR}/DriverX)

if(WIN32)
    add_definitions(-DCOMM_DLL)
elseif(UNIX)
    find_package(Threads)
endif()

# for common include way
include_directories(
    ${PROJ_ROOT_DIR}
    ${COMMUNICATE_DIR}
    ${JSONCPP_DIR}
    ${LIBMODBUS_DIR}
    ${LOGGING_DIR}
    ${OPEN62541_DIR}
    ${PROJ_ROOT_DIR}/include
    ${LIBCURL_DIR}/include
    ${ZIP_DIR}
    ${HTTP_DIR}
    ${SQLITE_DIR}/include
    ${JS_DIR}
    ${OPENSSL_DIR}/include
    ${FASTJSON_DIR}
    ${SYSTEMSETTING_DIR}
    ${TINYXML_DIR}
    ${OPCUA_SERVER_DIR}
    ${IEMS_SETTING_DIR}
)

link_directories(
    ${LIBRARY_OUTPUT_PATH}
    ${OPENSSL_DIR}/lib/${PLATFORM}
    ${MQTT_DIR}/lib/${PLATFORM}
    ${ZLIB_DIR}/lib/${PLATFORM}
    ${LIBCURL_DIR}/lib/${PLATFORM}
    ${SQLITE_DIR}/lib/${PLATFORM}
)

#to-do : remove deps of deps, try to recompile paho.mqtt
link_libraries(
    crypto
    ssl 
    Communicate
    curl
    sqlite3
    SystemSetting
    iEMSSetting
)

# Add source to this project's executable.
if(WIN32)
    add_executable (DriverX ${SRCS} ${JSONCPP_SRC} ${LOG4Z_SRC} ${OPEN62541_SRC} ${ZIP_SRC} ${FASTJSON_SRC} ${SYSTEM_SETTING_SRC} ${TINYXML_SRC} ${OPCUA_SERVER_SRC} "ConfigurationCaching.cpp")
else()
    add_executable (DriverX ${SRCS} ${JSONCPP_SRC} ${LOG4Z_SRC} ${OPEN62541_SRC} ${ZIP_SRC} ${FASTJSON_SRC} ${JS_SRC} ${SYSTEM_SETTING_SRC} ${TINYXML_SRC} ${OPCUA_SERVER_SRC})
endif()


# link system library
if(UNIX)
    target_link_libraries (DriverX ${CMAKE_THREAD_LIBS_INIT} ${CMAKE_DL_LIBS})
elseif(WIN32)
    target_link_libraries(${PROJECT_NAME} PUBLIC wsock32)
endif()


# TODO: Add tests and install targets if needed.
