#pragma once
#include "ICommunicate.h"

#include "IDriver.h"

namespace COMM
{
	class ICommModbus : public ICommunicate
	{
	public:
		const static int ERR_LINK = -2;

	public:
		ICommModbus() {}
		virtual ~ICommModbus() {}

		virtual void setup(const std::string& ip, int port) = 0;
		virtual void setup(const std::string& port, int baudrate, int dataBits, char parity, int stopBits) = 0;
		virtual void setByteTimeout(int sec, int usec) = 0;
		virtual void setResponseTimeout(int sec, int usec) = 0;
		virtual bool connect() = 0;
		virtual void disconnect() = 0;
		virtual void setSlaveId(int slaveId) = 0;
		virtual int readBits(int addr, int nb, std::vector<uint8_t>& data) = 0;
		virtual int readInputBits(int addr, int nb, std::vector<uint8_t>& data) = 0;
		virtual int readRegisters(int addr, int nb, std::vector<uint16_t>& data) = 0;
		virtual int readInputRegisters(int addr, int nb, std::vector<uint16_t>& data) = 0;
		virtual int writeBit(int addr, bool status) = 0;
		virtual int writeRegister(int addr, const uint16_t value) = 0;
		virtual int writeBits(int addr, const std::vector<uint8_t>& data) = 0;
		virtual int writeRegisters(int addr, const std::vector<uint16_t>& data) = 0;
		virtual void setLogCallBack(DRIVER::IDriverCallback *cb) = 0;
		virtual void onLog(const std::vector<uint8_t>& data) = 0;
		virtual void onLog(const std::string& func, const std::string& type, const std::string& addr, const std::vector<uint8_t>& data) = 0;
		virtual int readBits(int addr, int nb, std::vector<uint8_t>& data, std::vector<uint8_t>& packet_send, std::vector<uint8_t>& packet_recv) = 0;
		virtual int readInputBits(int addr, int nb, std::vector<uint8_t>& data, std::vector<uint8_t>& packet_send, std::vector<uint8_t>& packet_recv) = 0;
		virtual int readRegisters(int addr, int nb, std::vector<uint16_t>& data, std::vector<uint8_t>& packet_send, std::vector<uint8_t>& packet_recv) = 0;
		virtual int readInputRegisters(int addr, int nb, std::vector<uint16_t>& data, std::vector<uint8_t>& packet_send, std::vector<uint8_t>& packet_recv) = 0;
	};

} //namespace COMM end