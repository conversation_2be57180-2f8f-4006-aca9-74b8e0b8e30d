﻿# CMakeList.txt : CMake project for DriverX's plugin called DriverVirtual, include source and define
# project specific logic here.
#

project (DriverEMSBoxModbusRtu_build202528 VERSION 1.0)

add_definitions(-DDRIVER_DLL_BUILD)

set(LIBRARY_OUTPUT_PATH ${OUTPUT_DIR}/Driver)
set(CMAKE_BINARY_DIR ${BUILD_DIR}/DriverEMSBoxModbusRtu_build202528)

set(CMAKE_POSITION_INDEPENDENT_CODE ON)

if(WIN32)
    add_definitions(-DCOMM_DLL)
elseif(UNIX)
	find_package(Threads)
endif()

include_directories(
	${PROJ_ROOT_DIR}
	${COMMUNICATE_DIR}
	${UTILS_DIR}
)

link_directories(
	${OUTPUT_DIR}
)

#to-do : remove deps of deps
link_libraries(
	Communicate
)

aux_source_directory(./ SRCS)
aux_source_directory(../ ModBusSRCS)

# Add source to this project's library.
add_library (DriverEMSBoxModbusRtu_build202528 SHARED ${SRCS} ${ModBusSRCS})

# link library
if(UNIX)
target_link_libraries (DriverEMSBoxModbusRtu_build202528 ${CMAKE_THREAD_LIBS_INIT})
endif()

# TODO: Add tests and install targets if needed.
