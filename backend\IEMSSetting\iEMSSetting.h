#pragma once

#define IEMSSETTING_EXPORTS

#if defined(IEMSSETTING_EXPORTS)
#if defined(_MSC_VER) || defined(__MINGW32__)
#define IEMSSETTING_API __declspec(dllexport)
#elif defined(__GNUC__) || defined(__clang__)
#define IEMSSETTING_API __attribute__((visibility("default")))
#endif // if defined(_MSC_VER)

#elif defined(IEMSSETTING_API)
#if defined(_MSC_VER) || defined(__MINGW32__)
#define IEMSSETTING_API __declspec(dllimport)
#endif // if defined(_MSC_VER)
#endif // ifdef IEMSSETTING_EXPORTS

#include <iostream>
#include <string>

#include "json/json.h"

#include "iEMS-COM4.h"

class IEMSSETTING_API CiEMSSetting
{
public:
	static CiEMSSetting& getInstance()
	{
		static CiEMSSetting instance;
		return instance;
	}

	bool isValid();
	bool initialize(Json::Value root);

	bool controlRunningStateLed(EIEMSRunningStateLed led, int brightness);
	bool controlCOMStateLed(std::string name, int brightness);

private:
	void execLedControl(std::string led, int brightness);

private:
	CiEMSSetting();
	~CiEMSSetting() = default;

	CiEMSSetting(const CiEMSSetting&) = delete;
	CiEMSSetting& operator=(const CiEMSSetting&) = delete;

private:
	bool isValid_;
	SIEMSConfig iemsConfig_;
};