project (OPCUAServer VERSION 1.0)

set(EXECUTABLE_OUTPUT_PATH ${OUTPUT_DIR})
set(CMAKE_BINARY_DIR ${BUILD_DIR}/${PROJECT_NAME})

if(WIN32)
    
elseif(UNIX)
    find_package(Threads)
endif()

include_directories(
	${PROJ_ROOT_DIR}
    ${OPEN62541_DIR}
)

link_directories(
	${OUTPUT_DIR}
)

link_libraries(
	
)

aux_source_directory(./ SRCS)

if(WIN32)
    add_executable (${PROJECT_NAME} ${SRCS} ${OPEN62541_SRC})
else()
    add_executable (${PROJECT_NAME} ${SRCS} ${OPEN62541_SRC})
endif()

# link system library
if(UNIX)
    target_link_libraries(${PROJECT_NAME} ${CMAKE_THREAD_LIBS_INIT} ${CMAKE_DL_LIBS})
elseif(WIN32)
    target_link_libraries(${PROJECT_NAME} PUBLIC wsock32 ws2_32)
endif()