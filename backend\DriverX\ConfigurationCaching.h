#pragma once

#include <string>
#include <vector>
#include <map>

#include "json/json.h"
#include "sqlite3.h"

class CConfigurationCaching
{
public:
	typedef int (*CB)(void*, int, char**, char**);

	const char* sqlCreateDeviceTemplate = "CREATE TABLE IF NOT EXISTS device_template("  \
		"id TEXT PRIMARY KEY," \
		"content    TEXT    NOT NULL)";
	const char* sqlSelectDeviceTemplateOne = "SELECT * FROM device_template where id = '{0}'";
	const char* sqlSelectDeviceTemplateAll = "SELECT * FROM device_template";
	const char* sqlUpdateDeviceTemplate = "INSERT OR REPLACE INTO device_template(id, content) VALUES ('{0}', '{1}');";
	const char* sqlDeleteDeviceTemplate = "DELETE FROM device_template WHERE id IN ('{0}')";

	const char* sqlCreateConfigurationTemplate = "CREATE TABLE IF NOT EXISTS configuration_template("  \
		"id TEXT PRIMARY KEY," \
		"content    TEXT    NOT NULL)";
	const char* sqlSelectConfigurationTemplateOne = "SELECT * FROM configuration_template where id = '{0}'";
	const char* sqlSelectConfigurationTemplateAll = "SELECT * FROM configuration_template";
	const char* sqlUpdateConfigurationTemplate = "INSERT OR REPLACE INTO configuration_template(id, content) VALUES ('{0}', '{1}');";
	const char* sqlDeleteConfigurationTemplate = "DELETE FROM configuration_template WHERE id IN ('{0}')";

private:
	sqlite3* db_;
	static int callback(void* NotUsed, int argc, char** argv, char** azColName);
	static int callbackSelecTemplate(void* ud, int argc, char** argv, char** azColName);
	bool exec(char* sql, CB cb, void* ud);

public:
	CConfigurationCaching();
	~CConfigurationCaching();

	bool initial(std::string name);
	bool uninitial();

	bool updateDeviceTemplate(std::string id, std::string body);
	bool deleteDeviceTemplate(std::string id);
	std::map<std::string, std::string> selectDeviceTemplate(std::string id = "");

	bool updateConfigurationTemplate(std::string id, std::string body);
	bool deleteConfigurationTemplate(std::string id);
	std::map<std::string, std::string> selectConfigurationTemplate(std::string id = "");
};