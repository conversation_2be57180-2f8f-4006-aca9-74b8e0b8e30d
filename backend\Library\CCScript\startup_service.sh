#!/bin/sh

# 当前目录
CURRENT_DIR=$(pwd)

# 获取参数
ACTION=$1
TARGET=$2

# 检查ACTION参数是否有效
if [ "$ACTION" != "start" ] && [ "$ACTION" != "status" ] && [ "$ACTION" != "stop" ]; then
    echo "Invalid action: $ACTION. Must be one of: start, status, stop"
    exit 1
fi

# 如果只有一个参数，读取startup.config文件
if [ -z "$TARGET" ]; then
    if [ -f "$CURRENT_DIR/startup.config" ]; then
        # 读取startup.config文件中的startup字段
        STARTUP_VALUE=$(grep "startup" "$CURRENT_DIR/startup.config" | cut -d '=' -f 2 | tr -d ' ')
        
        # 检查startup字段是否为manager，否则使用DriverX
        if [ "$STARTUP_VALUE" = "manager" ]; then
            TARGET="manager"
            echo "Using startup value from config: $TARGET"
        else
            # 如果startup字段不是manager，使用默认值DriverX
            TARGET="DriverX"
            echo "Using default target: $TARGET"
        fi
    else
        # 如果startup.config文件不存在，使用默认值DriverX
        TARGET="DriverX"
        echo "No startup.config file found, using default: $TARGET"
    fi
fi

# 检查TARGET参数是否有效
if [ "$TARGET" != "DriverX" ] && [ "$TARGET" != "manager" ]; then
    echo "Invalid target: $TARGET. Must be one of: DriverX, manager"
    exit 1
fi

# 如果是start操作，先在bin和launch目录下执行stop
if [ "$ACTION" = "start" ]; then
    echo "Stopping all processes before starting $TARGET..."
    
    # 先执行bin目录下的stop
    echo "Stopping processes in bin directory..."
    cd "$CURRENT_DIR/bin"
    sh ./service.sh stop
    
    # 再执行launch目录下的stop
    echo "Stopping processes in launch directory..."
    cd "$CURRENT_DIR/launch"
    sh ./service.sh stop
    
    # 返回当前目录
    cd "$CURRENT_DIR"
fi

# 根据目标选择目录并执行命令
if [ "$TARGET" = "DriverX" ]; then
    echo "Executing $ACTION for DriverX in bin directory..."
    cd "$CURRENT_DIR/bin"
    sh ./service.sh $ACTION
    EXIT_CODE=$?
elif [ "$TARGET" = "manager" ]; then
    echo "Executing $ACTION for manager in launch directory..."
    cd "$CURRENT_DIR/launch"
    sh ./service.sh $ACTION
    EXIT_CODE=$?
fi

# 返回原目录
cd "$CURRENT_DIR"
exit $EXIT_CODE 