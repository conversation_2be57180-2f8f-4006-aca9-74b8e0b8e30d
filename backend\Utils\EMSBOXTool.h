#pragma once
#include <stdio.h>

#include <typeinfo>
#include <string>
#include <vector>
#include <sstream>
#include <cstring>
#include <iostream>
#include <chrono>
#include <iomanip>
#include <ctime>
#include <fstream>
#include <cctype>
#include <map>
#include <unordered_map>

namespace UTILS
{
	static std::unordered_map<std::string, std::string> IEMSBOXLedMap_ =
	{
		{ "/dev/ttymxc6",	"/sys/class/leds/led1/brightness"},
		{ "/dev/ttymxc7",	"/sys/class/leds/led2/brightness"},
		{ "/dev/ttymxc1",	"/sys/class/leds/led3/brightness"},
		{ "/dev/ttymxc3",	"/sys/class/leds/led4/brightness"},
		{"run",				"/sys/class/leds/run/brightness"},
		{"err",				"/sys/class/leds/err/brightness"}
	};


	inline void controlLed(std::string name, int light)
	{
		std::string ledname;
		if (IEMSBOXLedMap_.find(name) != IEMSBOXLedMap_.end())
		{
			ledname = IEMSBOXLedMap_.at(name);
		}
		else
			return;
#if defined(_WIN32) || defined(_WIN64)
#else
		std::string cmd = "echo " + std::to_string(light) + " > " + ledname;
		system(cmd.c_str());
#endif
	}
} 
