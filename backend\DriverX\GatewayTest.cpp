#include "GatewayTest.h"

#include <iostream>
#include <future>
#include <cstdlib>
#include <functional>
#include <thread>
#include <fstream>

namespace GATEWAY
{
	CGatewayTest::CGatewayTest()
	{
	}

	CGatewayTest::~CGatewayTest()
	{
	}

	bool CGatewayTest::initial(GatewayConfig& cfg)
	{
		CGateway::initial(cfg);
		printf("CGatewayTest::initial;\n");

		cfgPath_ = cfg["CfgPath"];
		printf("cfgPath_:%s.\n", cfgPath_.c_str());
		return true;
	}

	bool CGatewayTest::uninitial(void)
	{
		printf("CGatewayTest::uninitial;\n");
		CGateway::uninitial();
		return true;
	}

	static std::string readFile(const std::string& filename) {
		std::ifstream t{ filename };
		return std::string{ std::istreambuf_iterator<char>(t), std::istreambuf_iterator<char>() };
	}

	bool CGatewayTest::start()
	{
		CGateway::start();
		printf("CGatewayTest::start;\n");
		auto configFunc = [this]() {
			std::string json;
			std::ifstream ifs(cfgPath_);
			if (ifs.is_open())
			{
				std::istreambuf_iterator<char> beg(ifs), end;
				json = std::string(beg, end);
				ifs.close();
			}
			else
			{
				return;
			}
			ifs.close();

			std::string cfg = readFile(cfgPath_);
			JSONCPP_STRING err;
			Json::Value root;
			Json::CharReaderBuilder builder;
			std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
			if (!reader->parse(json.c_str(), json.c_str() + json.size(), &root, &err))
			{
				std::cout << "parse comming message error = " << err << std::endl;
			}

			auto& links = root["links"];
			std::map<std::string, DRIVER::SChannelConfig> sccs;
			DRIVER::SChannelConfig slc;
			for (auto linkIter = links.begin(); linkIter != links.end(); ++linkIter)
			{
				slc.name = linkIter.name();
				slc.driverType = (*linkIter)["type"].asString();
				slc.pollCycle = (*linkIter)["pollCycle"].asInt();
				auto& lp = (*linkIter)["protocol"];
				for (auto configItemIter = lp.begin(); configItemIter != lp.end(); ++configItemIter)
				{
					slc.channelProtocol[configItemIter.name()] = configItemIter->asString();
				}

				DRIVER::SDeviceConfig sdc;
				auto& devices = (*linkIter)["devices"];
				for (auto deviceIter = devices.begin(); deviceIter != devices.end(); ++deviceIter)
				{
					sdc.name = deviceIter.name();
					auto& dp = (*deviceIter)["protocol"];
					for (auto configItemIter = dp.begin(); configItemIter != dp.end(); ++configItemIter)
					{
						sdc.deviceProtocol[configItemIter.name()] = configItemIter->asString();
					}

					DRIVER::SPointConfig spc;
					auto& points = (*deviceIter)["points"];
					for (auto pointIter = points.begin(); pointIter != points.end(); ++pointIter)
					{
						spc.name = pointIter.name();
						spc.readOnly = (*pointIter)["readOnly"].asBool();
						spc.coefficient = (*pointIter)["coeff"].asFloat();
						spc.added = (*pointIter)["added"].asFloat();
						auto& pp = (*pointIter)["protocol"];
						for (auto configItemIter = pp.begin(); configItemIter != pp.end(); ++configItemIter)
						{
							spc.pointProtocol[configItemIter.name()] = configItemIter->asString();
						}
						sdc.pointTable[spc.name] = spc;
					}
					slc.deviceTable[sdc.name] = sdc;
				}
				sccs[linkIter.name()] = slc;
			}
			

			//opcua
			//const std::string data = "{\"command\": 2,\"sequence\" : 1,\"GWTest\" : {\"version\": 901,\"type\" : \"gateway\",\"devices\" : {\"LINKTest\": {\"version\": 64,\"type\" : \"link\",\"protocol\" : {\"driverType\":\"DriverOpcUa\",\"EndpointUrl\": \"opc.tcp:\/\/***********:49320\"},\"devices\" : {\"TESTDeviceA\": {\"type\": \"normal\",\"protocol\" : {\"state\": {\"POINTTestA\": {\"IOAddress\" : \"Channel_1.Device_1.MyWord\"},\"POINTTestB\" : {\"IOAddress\" : \"Channel_1.Device_1.TEST_AM1\"},\"POINTTestC\" : {\"IOAddress\" : \"Channel_1.Device_1.Bool_1\"}}}}}}}}}";
			//modbus rtu
			//const std::string data = "{\"command\": 2,\"sequence\" : 1,\"GWTest\" : {\"version\": 901,\"type\" : \"gateway\",\"devices\" : {\"LINKTest\": {\"version\": 64,\"type\" : \"link\",\"protocol\" : {\"driverType\":\"DriverModbus\",\"modbus\": \"rtu\",\"deviceName\":\"COM3\", \"baudRate\":9600, \"parity\":\"E\", \"dataBit\":8, \"stopBit\":1},\"devices\" : {\"TESTDeviceA\": {\"type\": \"normal\",\"protocol\" : {\"ctrl\": {\"POINTTestA\": {\"SlaveId\": 1, \"FuncCode\":6,\"Address\" : 3,\"ValueType\":\"Int16\"},\"POINTTestB\" : {\"SlaveId\": 1, \"FuncCode\":6,\"Address\" : 5,\"ValueType\":\"Int16\"}},\"state\": {\"POINTTestA\": {\"SlaveId\": 1, \"FuncCode\":3,\"Address\" : 3,\"ValueType\":\"Int16\"},\"POINTTestB\" : {\"SlaveId\": 1, \"FuncCode\":3,\"Address\" : 5,\"ValueType\":\"Int16\"}}}}}}}}}";
			//bacnet
			//const std::string data = "{\"command\": 2,\"sequence\" : 1,\"GWTest\" : {\"version\": 901,\"type\" : \"gateway\",\"devices\" : {\"LINKTest\": {\"version\": 64,\"type\" : \"link\",\"protocol\" : {\"driverType\":\"DriverBACnet\", \"Interface\":\"***********\", \"Port\":\"47808\"},\"devices\" : {\"TESTDeviceA\": {\"type\": \"normal\",\"protocol\" : {\"state\": {\"POINTTestA\": {\"DeviceId\": 2, \"ObjectType\":1,\"ObjectId\" : 0,\"PropertyId\":85},\"POINTTestB\" : {\"DeviceId\": 2, \"ObjectType\":0,\"ObjectId\" : 0,\"PropertyId\":85}}}}}}}}}";
			//opcda
			//const std::string data = "{\"command\": 2,\"sequence\" : 1,\"GWTest\" : {\"version\": 901,\"type\" : \"gateway\",\"devices\" : {\"LINKTest\": {\"version\": 64,\"type\" : \"link\",\"protocol\" : {\"driverType\":\"DriverOpcDa\",\"HostName\":\"DESKTOP-H2AKV6Q\",\"ServerName\": \"Kepware.KEPServerEX.V6\"},\"devices\" : {\"TESTDeviceA\": {\"type\": \"normal\",\"protocol\" : {\"ctrl\": {\"POINTTestA\": {\"IOAddress\" : \"Channel1.Device1.point0\"},\"POINTTestB\" : {\"IOAddress\" : \"Channel1.Device1.point1\"}},\"state\": {\"POINTTestA\": {\"IOAddress\" : \"Channel1.Device1.point0\"},\"POINTTestB\" : {\"IOAddress\" : \"Channel1.Device1.point1\"}}}}}},\"LINKTest1\": {\"version\": 64,\"type\" : \"link\",\"protocol\" : {\"driverType\":\"DriverOpcDa\",\"HostName\":\"DESKTOP-H2AKV6Q\",\"ServerName\": \"Kepware.KEPServerEX.V6\"},\"devices\" : {\"TESTDeviceA\": {\"type\": \"normal\",\"protocol\" : {\"ctrl\": {\"POINTTestA\": {\"IOAddress\" : \"Channel2.Device1.point1\"},\"POINTTestB\" : {\"IOAddress\" : \"Channel1.Device1.point1\"}},\"state\": {\"POINTTestA\": {\"IOAddress\" : \"Channel2.Device1.point1\"},\"POINTTestB\" : {\"IOAddress\" : \"Channel1.Device1.point1\"}}}}}}}}}";
			//rockwell plc
			//const std::string data = "{\"command\": 2,\"sequence\" : 1,\"GWTest\" : {\"version\": 901,\"type\" : \"gateway\",\"devices\" : {\"LINKTest\": {\"version\": 64,\"type\" : \"link\",\"protocol\" : {\"driverType\":\"DriverRockwellPlc\", \"PlcIp\":\"127.0.0.1\", \"cpu\":\"lgx\",\"plcPath\":\"1,0\"},\"devices\" : {\"TESTDeviceA\": {\"type\": \"normal\",\"protocol\" : {\"state\": {\"POINTTestA\": {\"TagName\": \"MyTag\", \"DataType\":\"uint32\"}}}}}}}}}";
			//siemens plc
			//const std::string data = "{\"command\": 2,\"sequence\" : 1,\"GWTest\" : {\"version\": 901,\"type\" : \"gateway\",\"devices\" : {\"LINKTest\": {\"version\": 64,\"type\" : \"link\",\"protocol\" : {\"driverType\":\"DriverSiemensPlc\", \"plcUri\":\"127.0.0.1:102\", \"plcType\":\"S7-200\"},\"devices\" : {\"TESTDeviceA\": {\"type\": \"normal\",\"protocol\" : {\"state\": {\"POINTTestA\": {\"Address\": \"V2634\", \"DataType\":\"float\"},\"POINTTestB\": {\"Address\": \"V2638\", \"DataType\":\"float\"},\"POINTTestC\": {\"Address\": \"V2642\", \"DataType\":\"float\"}}}}}}}}}";
			//secp
			//const std::string data = "{\"command\": 2,\"sequence\" : 1,\"GWTest\" : {\"version\": 901,\"type\" : \"gateway\",\"devices\" : {\"LINKTest\": {\"version\": 64,\"type\" : \"link\",\"protocol\" : {\"driverType\":\"DriverSECP\",\"serverUrl\":\"************:1000\"},\"devices\" : {\"TESTDeviceA\": {\"type\": \"normal\",\"protocol\" : {\"state\": {\"POINTTestA\": {\"TagName\" : \"AI01\"},\"POINTTestB\" : {\"TagName\" : \"DO01_06\"}, \"POINTTestC\": {\"TagName\" : \"MAI01\"}, \"POINTTestD\": {\"TagName\" : \"MAI448\"}, \"POINTTestE\": {\"TagName\" : \"MAO01\"}, \"POINTTestF\": {\"TagName\" : \"MDI01_01\", \"POINTTestG\": {\"TagName\" : \"MDO01_01\"}}}}}}}}}}";
			//shun tong mqtt
			//const std::string data = "{\"command\": 2,\"sequence\" : 1,\"GWTest\" : {\"version\": 901,\"type\" : \"gateway\",\"devices\" : {\"LINKTest\": {\"version\": 64,\"type\" : \"link\",\"protocol\" : {\"driverType\":\"DriverShunTongMqtt\",\"brokerUri\": \"***********:1883\", \"stationId\": \"3\"},\"devices\" : {\"TESTDeviceA\": {\"type\": \"normal\",\"protocol\" : {\"state\": {\"POINTTestA\": {\"IOAddress\" : \"5001.10004\"},\"POINTTestB\" : {\"IOAddress\" : \"5001.10020\"},\"POINTTestC\" : {\"IOAddress\" : \"5001.10021\"}}}}}}}}}";

			std::this_thread::sleep_for(std::chrono::milliseconds(2000));
			for (size_t i = 0; i < 1; i++)
			{
				{
					std::lock_guard<std::mutex> lock(gatewayMutex_);
					configure(sccs);
				}
				std::this_thread::sleep_for(std::chrono::seconds(1));
			}

		};

		auto singleControlFunc = [this]() {
			const std::string data = "{\"sequence\": 1,\"timeout\" : 4,\"channelName\" : \"LINKTest\",\"deviceName\" : \"TESTDeviceA\",\"pointName\" : \"POINTTestA\",\"value\" : 12}";
			std::this_thread::sleep_for(std::chrono::milliseconds(3333));
			for (size_t i = 0; i < 20; i++)
			{
				{
					std::lock_guard<std::mutex> lock(gatewayMutex_);
					
				}
				std::this_thread::sleep_for(std::chrono::milliseconds(rand() % 999));
			}

		};

		auto multiControlFunc = [this]() {
			const std::string data = "{\"sequence\": 1,\"timeout\" : 2,\"ctrlInfo\" : {\"LINKTest\": {\"TESTDeviceA\": {\"POINTTestA\": 1,\"POINTTestB\" : 2}}}}";
			std::this_thread::sleep_for(std::chrono::milliseconds(5555));
			for (size_t i = 0; i < 20; i++)
			{
				{
					std::lock_guard<std::mutex> lock(gatewayMutex_);
					
				}
				std::this_thread::sleep_for(std::chrono::milliseconds(rand() % 999));
			}

		};

		//auto ret = std::async(std::launch::async, );
		configFunc();
		//auto ret1 = std::async(std::launch::async, singleControlFunc);
		//auto ret2 = std::async(std::launch::async, multiControlFunc);
		return true;
	}

	bool CGatewayTest::stop()
	{
		printf("CGatewayTest::stop;\n");
		CGateway::stop();
		return true;
	}
	
	void CGatewayTest::onEngineStatus(const std::string& channelName, const DRIVER::SStatus& status)
	{
		CGateway::onEngineStatus(channelName, status);
	}

	void CGatewayTest::onEngineOnline(const std::string& channelName, bool online)
	{
		CGateway::onEngineOnline(channelName, online);
	}

	void CGatewayTest::onEngineData(const std::string& channelName, DRIVER::ChannelDataSetSptr dataSetSptr)
	{
		CGateway::onEngineData(channelName, dataSetSptr);
	}

	void CGatewayTest::onEngineHistoryData(const std::string& channelName, DRIVER::ChannelHistoryDataSetSptr dataSetSptr)
	{
		for (auto deviceIter = dataSetSptr->begin(); deviceIter != dataSetSptr->end(); deviceIter++)
		{
			Json::Value history_data;
			int index = 0;
			while (!deviceIter->second.empty())
			{
				Json::Value state;
				Json::Value quality;
				Json::Value updatetime;
				for (auto pointIter = deviceIter->second.begin(); pointIter != deviceIter->second.end(); )
				{
					DRIVER::SData data = pointIter->second.back();
					pointIter->second.pop_back();
					switch (data.value.getType())
					{
					case SValue::eValueInt:
						state[pointIter->first] = data.value.asInt(); break;
					case SValue::eValueReal:
						state[pointIter->first] = data.value.asFloat(); break;
					case SValue::eValueString:
						state[pointIter->first] = data.value.asString(); break;
					case SValue::eValueBoolean:
						state[pointIter->first] = data.value.asBool(); break;
					default: break;
					}

					Json::Value qualityArr;
					qualityArr[0] = data.quality == -1 ? "scanoff" : "";
					quality[pointIter->first] = qualityArr;
					updatetime[pointIter->first] = data.timestamp;

					if (pointIter->second.empty())
					{
						pointIter = deviceIter->second.erase(pointIter);
					}
				}

				Json::Value arrElem;
				arrElem["timestamp"] = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
				arrElem["state"] = std::move(state);
				arrElem["quality"] = std::move(quality);
				arrElem["updatetime"] = std::move(updatetime);
				history_data[index++] = std::move(arrElem);
			}

			Json::Value pkg;
			pkg["sequence"] = 1;
			pkg["history-data"] = std::move(history_data);
			std::string topic = deviceIter->first + "/history";
			std::string message = pkg.toStyledString();
			std::cout << message;
		}
	}

	void CGatewayTest::onEngineEvent(const std::string& channelName, const std::string& deviceName, SEventSptr eventSptr)
	{
	}

	void CGatewayTest::onEngineControlFeedback(const std::string& channelName, const SControlFeedback& feedback)
	{

	}

	void CGatewayTest::onEngineUpdateDeviceOffline(const std::string& channelName, bool isOffline)
	{

	}

	void CGatewayTest::onEngineUpdateDeviceScanoff(const std::string& channelName, std::string& deviceName, bool scanoff)
	{
	}

	void CGatewayTest::onEngineUpdateDeviceInhibit(const std::string& channelName, std::string& deviceName, bool inhibit)
	{
	}

	void CGatewayTest::onEngineIEMSLedBrightness(const std::string& deviceName, int brightness)
	{
	}

} //namespace GATEWAY end
