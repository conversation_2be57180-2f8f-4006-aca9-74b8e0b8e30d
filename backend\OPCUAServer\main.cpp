#include "OpcUaServer.h"

int main() {
    // 创建服务器实例

    std::unordered_map<std::string, std::unordered_map<std::string, std::set<std::string>>> allNodeName;

    // 添加测试数据
    // 链路1：工厂A
    allNodeName["Factory_A"]["Device_001"] = {"Temperature", "Pressure", "Status"};
    allNodeName["Factory_A"]["Device_002"] = {"Voltage", "Current", "Power"};
    allNodeName["Factory_A"]["Device_003"] = {"Flow_Rate", "Level", "Alarm"};

    // 链路2：工厂B
    allNodeName["Factory_B"]["Sensor_101"] = {"Humidity", "CO2_Level", "Light_Intensity"};
    allNodeName["Factory_B"]["Sensor_102"] = {"Vibration", "Noise_Level"};
    allNodeName["Factory_B"]["Controller_201"] = {"SetPoint", "Output", "Mode", "Error_Code"};

    // 链路3：楼宇系统
    allNodeName["Building_System"]["HVAC_Unit_01"] = {"Supply_Temp", "Return_Temp", "Fan_Speed", "Damper_Position"};
    allNodeName["Building_System"]["HVAC_Unit_02"] = {"Supply_Temp", "Return_Temp", "Fan_Speed"};
    allNodeName["Building_System"]["Lighting_Zone_01"] = {"Brightness", "Switch_Status", "Energy_Consumption"};

    // 链路4：水处理系统
    allNodeName["Water_Treatment"]["Pump_Station_A"] = {"Flow_Rate", "Pressure_In", "Pressure_Out", "Motor_Current"};
    allNodeName["Water_Treatment"]["Filter_Unit_01"] = {"Inlet_Pressure", "Outlet_Pressure", "Backwash_Status"};
    allNodeName["Water_Treatment"]["Chemical_Dosing"] = {"pH_Value", "Chlorine_Level", "Dosing_Rate"};

    COpcUaServer svr;
    
    svr.init();

    svr.start();

    // 添加变量到OPC UA服务器
    svr.addVariable(allNodeName);

    uint64_t t = 1;
    while (1)
    {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        svr.updateVariable("Factory_A.Device_001.Temperature", SubData{ t++ });
    }

    return 0;
}