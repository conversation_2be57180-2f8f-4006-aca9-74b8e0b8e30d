#pragma once

#include <spdlog/common.h>
#include <spdlog/details/file_helper.h>
#include <spdlog/details/null_mutex.h>
#include <spdlog/fmt/fmt.h>
#include <spdlog/fmt/chrono.h>
#include <spdlog/sinks/base_sink.h>
#include <spdlog/details/os.h>
#include <spdlog/details/circular_q.h>
#include <spdlog/details/synchronous_factory.h>

#include <chrono>
#include <cstdio>
#include <ctime>
#include <mutex>
#include <string>
#include <regex>

#include "spdlog_utils.h"
#include "CTinyDirHelper.h"

namespace spdlog {
namespace sinks {

template<typename Mutex>
class daily_block_level_file_sink final : public base_sink<Mutex>
{
public:
	daily_block_level_file_sink(std::string base_dir, filename_t base_filename, std::size_t max_size, std::size_t max_files)
		: base_dir_(format_dir(base_dir))
		, base_filename_(std::move(base_filename))
		, max_size_(max_size)
		, max_files_(max_files)
	{

		for (auto i = 0; i < level::level_enum::n_levels; i++)
		{
			multi_current_size_[i] = 0;
			multi_initialized_[i] = false;
			multi_filenames_q_initialized_[i] = false;
		}
	}


	~daily_block_level_file_sink()
	{

		for (auto i = 0; i < level::level_enum::n_levels; i++)
		{


			if (multi_initialized_[i])
			{
				multi_file_helper_[i].close();
			}
		}
	}

	filename_t filename(int level_index)
	{
		std::lock_guard<Mutex> lock(base_sink<Mutex>::mutex_);
		if (multi_initialized_[level_index])
		{
			return multi_file_helper_[level_index].filename();
		}
		return filename_t{};
	}

protected:
	void sink_it_(const details::log_msg& msg) override
	{
		memory_buf_t formatted;
		base_sink<Mutex>::formatter_->format(msg, formatted);

		int logLevel = msg.level;
		

		if (logLevel < 0 || logLevel >= level::level_enum::n_levels)
		{

			logLevel = level::info;
		}
		

		if (!multi_initialized_[logLevel])
		{
			initialize_level_(logLevel);
		}

		size_t& currentSize = multi_current_size_[logLevel];
		details::file_helper& fileHelper = multi_file_helper_[logLevel];
		currentSize += formatted.size();

		auto time = msg.time;
		bool should_rotate = false;

		auto eNameView = level::to_string_view(static_cast<level::level_enum>(logLevel));
		std::string eName(eNameView.data(), eNameView.size());
		auto src_name = daily_filename_(base_dir_ + "/" + eName + "/" + base_filename_, now_tm(time));
		auto target_name = calc_filename_(base_dir_ + "/" + eName + "/" + base_filename_, now_tm(time));
		
		if (currentSize >= max_size_)
		{
			should_rotate = true;
			std::string oldfn = fileHelper.filename();
			fileHelper.close();
			if (!rename_file_(oldfn, target_name))
			{
				details::os::sleep_for_millis(200);
				if (!rename_file_(oldfn, target_name))
				{
					src_name = target_name;
				}
			}
			fileHelper.open(src_name, false);
			currentSize = fileHelper.size();
		}
		fileHelper.write(formatted);
		

		if (should_rotate && max_files_ > 0 && multi_filenames_q_initialized_[logLevel])
		{
			delete_old_(multi_filenames_q_[logLevel], target_name);
		}
	}

	void flush_() override
	{
		for (auto i = 0; i < level::level_enum::n_levels; i++)
		{

			if (multi_initialized_[i])
			{
				multi_file_helper_[i].flush();
			}
		}
	}

private:

	void initialize_level_(int level_index)
	{
		if (multi_initialized_[level_index])
		{
			return;
		}

		try
		{
			auto now = log_clock::now();
			auto eNameView = level::to_string_view(static_cast<level::level_enum>(level_index));
			std::string eName(eNameView.data(), eNameView.size());
			auto filename = daily_filename_(base_dir_ + "/" + eName + "/" + base_filename_, now_tm(now));
			


			multi_file_helper_[level_index].open(filename, false);
			multi_current_size_[level_index] = multi_file_helper_[level_index].size();
			

			multi_initialized_[level_index] = true;
			

			if (max_files_ > 0)
			{
				std::vector<filename_t> filenames;
				multi_filenames_q_[level_index] = details::circular_q<filename_t>(static_cast<size_t>(max_files_));
				CTinyDirHelper ctdh(base_dir_ + "/" + eName + "/");
				auto l = ctdh.listDir();
				std::regex r("^" + base_filename_ + "_[0-9]{4}_[0-9]{2}_[0-9]{2}_[0-9]{2}_[0-9]{2}_[0-9]{2}.log$");
				for (auto it = l.rbegin(); it != l.rend() && filenames.size() < max_files_; it++)
				{
					if (std::regex_match(*it, r))
						filenames.push_back(base_dir_ + "/" + eName + "/" + *it);
				}
				for (auto it = filenames.rbegin(); it != filenames.rend(); ++it)
				{
					multi_filenames_q_[level_index].push_back(std::move(*it));
				}
				multi_filenames_q_initialized_[level_index] = true;
			}
		}
		catch (const std::exception& ex)
		{

			if (multi_initialized_[level_index])
			{
				multi_file_helper_[level_index].close();
				multi_initialized_[level_index] = false;
			}
			multi_filenames_q_initialized_[level_index] = false;
			

			

			(void)ex;
		}
	}

	tm now_tm(log_clock::time_point tp)
	{
		time_t tnow = log_clock::to_time_t(tp);
		return spdlog::details::os::localtime(tnow);
	}

	static filename_t daily_filename_(const filename_t& filename, const tm& now_tm)
	{
		filename_t basename, ext;
		std::tie(basename, ext) = details::file_helper::split_by_extension(filename);
		return fmt::format(SPDLOG_FILENAME_T("{}_{:04d}_{:02d}_{:02d}{}"),
			basename,
			now_tm.tm_year + 1900,
			now_tm.tm_mon + 1,
			now_tm.tm_mday,
			ext);
	}

	static filename_t calc_filename_(const filename_t& filename, const tm& now_tm)
	{
		filename_t basename, ext;
		std::tie(basename, ext) = details::file_helper::split_by_extension(filename);
		return fmt::format(SPDLOG_FILENAME_T("{}_{:04d}_{:02d}_{:02d}_{:02d}_{:02d}_{:02d}{}.log"),
			basename,
			now_tm.tm_year + 1900,
			now_tm.tm_mon + 1,
			now_tm.tm_mday,
			now_tm.tm_hour,
			now_tm.tm_min,
			now_tm.tm_sec,
			ext);
	}

	static bool rename_file_(const filename_t& src_filename, const filename_t& target_filename)
	{
		(void)details::os::remove(target_filename);
		return details::os::rename(src_filename, target_filename) == 0;
	}

	void delete_old_(details::circular_q<filename_t>& filenames_q, std::string target_name)
	{
		using details::os::filename_to_str;
		using details::os::remove_if_exists;
		if (filenames_q.full())
		{
			auto old_filename = std::move(filenames_q.front());
			filenames_q.pop_front();
			bool ok = remove_if_exists(old_filename) == 0;
			if (!ok)
			{
				filenames_q.push_back(std::move(old_filename));
				throw_spdlog_ex("Failed removing daily file " + filename_to_str(old_filename), errno);
			}
		}
		filenames_q.push_back(std::move(target_name));
	}

	std::string format_dir(std::string dir)
	{
		dir = spp(dir, "\\\\", "/");
		dir = spp(dir, "\\", "/");
		dir = spp(dir, "//", "/");
		return dir;
	}

private:
	std::string base_dir_;
	filename_t base_filename_;
	std::size_t max_size_;
	std::size_t max_files_;

	size_t multi_current_size_[level::level_enum::n_levels];
	details::file_helper multi_file_helper_[level::level_enum::n_levels];
	details::circular_q<filename_t> multi_filenames_q_[level::level_enum::n_levels];
	bool multi_initialized_[level::level_enum::n_levels];
	bool multi_filenames_q_initialized_[level::level_enum::n_levels];
};

using daily_block_level_file_sink_mt = daily_block_level_file_sink<std::mutex>;
using daily_block_level_file_sink_st = daily_block_level_file_sink<details::null_mutex>;

} // namespace sinks

template<typename Factory = spdlog::synchronous_factory>
inline std::shared_ptr<logger> daily_block_level_file_sink_mt(
	const std::string& logger_name, std::string base_dir, filename_t base_filename, std::size_t max_size, std::size_t max_files)
{
	return Factory::template create<sinks::daily_block_level_file_sink_mt>(logger_name, base_dir, base_filename, max_size, max_files);
}

template<typename Factory = spdlog::synchronous_factory>
inline std::shared_ptr<logger> daily_block_level_file_sink_st(
	const std::string& logger_name, std::string base_dir, filename_t base_filename, std::size_t max_size, std::size_t max_files)
{
	return Factory::template create<sinks::daily_block_level_file_sink_st>(logger_name, base_dir, base_filename, max_size, max_files);
}

} // namespace spdlog