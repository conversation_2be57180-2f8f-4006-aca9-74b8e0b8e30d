#pragma once

#include <thread>
#include <atomic>
#include <string>
#include <memory>

#include "modbus.h"

#include "ICommModbus.h"
#include "ACommFactory.h"

#define COMM_MODBUS_MIN_REQ_LENGTH 12
#define COMM_MODBUS_MAX_MESSAGE_LENGTH 260

namespace COMM
{
	class CCommModbus : public ICommModbus
	{
	private:
		std::string lastError_;
		modbus_t* ctx_;
		int tableBitsSize_;
		int tableInputBitsSize_;
		int tableInputRegistersSize_;
		int tableRegistersSize_;
		int byteTimeoutSec_;
		int byteTimeoutUsec_;
		int responseTimeoutSec_;
		int responseTimeoutUsec_;
		DRIVER::IDriverCallback *logCb_;

	private:
		bool getSpaceSize();

	public:
		CCommModbus();
		virtual ~CCommModbus();

		virtual std::string getLastError() override;
		virtual void setup(const std::string& ip, int port) override;
		virtual void setup(const std::string& port, int baudrate, int dataBits, char parity, int stopBits) override;
		virtual void setByteTimeout(int sec, int usec) override;
		virtual void setResponseTimeout(int sec, int usec) override;
		virtual bool connect() override;
		virtual void disconnect() override;
		virtual void setSlaveId(int slaveId) override;
		virtual int readBits(int addr, int nb, std::vector<uint8_t>& data) override;
		virtual int readInputBits(int addr, int nb, std::vector<uint8_t>& data) override;
		virtual int readRegisters(int addr, int nb, std::vector<uint16_t>& data) override;
		virtual int readInputRegisters(int addr, int nb, std::vector<uint16_t>& data) override;
		virtual int writeBit(int addr, bool status) override;
		virtual int writeRegister(int addr, const uint16_t value) override;
		virtual int writeBits(int addr, const std::vector<uint8_t>& data) override;
		virtual int writeRegisters(int addr, const std::vector<uint16_t>& data) override;
		virtual void setLogCallBack(DRIVER::IDriverCallback* cb) override;
		virtual void onLog(const std::vector<uint8_t>& data) override;
		virtual void onLog(const std::string& func, const std::string& type, const std::string& addr, const std::vector<uint8_t>& data) override;
		virtual int readBits(int addr, int nb, std::vector<uint8_t>& data, std::vector<uint8_t>& packet_send, std::vector<uint8_t>& packet_recv) override;
		virtual int readInputBits(int addr, int nb, std::vector<uint8_t>& data, std::vector<uint8_t>& packet_send, std::vector<uint8_t>& packet_recv) override;
		virtual int readRegisters(int addr, int nb, std::vector<uint16_t>& data, std::vector<uint8_t>& packet_send, std::vector<uint8_t>& packet_recv) override;
		virtual int readInputRegisters(int addr, int nb, std::vector<uint16_t>& data, std::vector<uint8_t>& packet_send, std::vector<uint8_t>& packet_recv) override;
	};
	REGISTER_COMM(CCommModbus, "CommModbus")
} //namespace COMM end


