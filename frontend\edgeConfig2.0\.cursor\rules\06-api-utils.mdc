---
description: 
globs: 
alwaysApply: false
---
# API和工具函数

## API配置
- [src/api/index.ts](mdc:src/api/index.ts) - API请求的主配置，包含拦截器和通用处理
- **src/api/modules/** - 按模块分类的API请求函数
- **src/api/interface/** - API接口的TypeScript类型定义
- **src/api/config/** - API相关配置
- **src/api/helper/** - API辅助函数

## 工具函数
- [src/utils/util.ts](mdc:src/utils/util.ts) - 通用工具函数集
- [src/utils/errorHandler.ts](mdc:src/utils/errorHandler.ts) - 全局错误处理
- [src/utils/getEnv.ts](mdc:src/utils/getEnv.ts) - 环境变量处理
- [src/utils/eleValidate.ts](mdc:src/utils/eleValidate.ts) - ElementPlus表单验证
- [src/utils/serviceDict.ts](mdc:src/utils/serviceDict.ts) - 服务字典
- [src/utils/svg.ts](mdc:src/utils/svg.ts) - SVG工具函数
- **src/utils/is/** - 类型判断函数
- **src/utils/theme/** - 主题相关工具

## 使用示例
```ts
// API使用
import { getUserInfo } from '@/api/modules/user';

// 工具函数使用
import { deepClone } from '@/utils/util';
```

