#pragma once

#include <string>
#include <vector>
#include <fstream>
#include <mutex>
#include <memory>
#include <chrono>

namespace COMM
{
    class ModbusPacketLogger
    {
    private:
        static std::mutex logMutex_;
        static std::unique_ptr<std::ofstream> logFile_;
        static bool enabled_;
        static std::string logFilePath_;

    public:
        static void initialize(const std::string& logPath);
        static void enable(bool enable);
        static void logPacket(bool isSend, const std::vector<uint8_t>& packet, 
                             const std::string& context = "");
        static void logPacket(bool isSend, const uint8_t* data, size_t length, 
                             const std::string& context = "");
        static void flush();
        static void close();

    private:
        static std::string getCurrentTimestamp();
        static std::string formatPacket(const uint8_t* data, size_t length);
    };
}