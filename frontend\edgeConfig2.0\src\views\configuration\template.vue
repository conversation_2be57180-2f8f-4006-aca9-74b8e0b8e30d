<template>
  <div class="container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span></span>
          <div class="header-buttons">
            <el-button 
              type="primary" 
              @click="saveTemplate"
              :class="{ 'has-changes': isTemplateModified || checkUnsavedChanges() }"
            >
              保存模板
              <el-badge 
                v-if="isTemplateModified || checkUnsavedChanges()" 
                :value="getModifiedCount()" 
                class="changes-badge" 
              />
            </el-button>
            <el-button type="primary" @click="handleImportTemplate">导入设备模板</el-button>
            <el-button type="success" @click="handleExportTemplate">导出设备模板</el-button>
            <el-button type="info" @click="refreshTemplateList" :loading="refreshLoading">
              <el-icon><Refresh /></el-icon> 刷新
            </el-button>
          </div>
        </div>
      </template>
      <div class="card-content">
        <el-container style="height: 100%">
          <!-- 左侧设备原型列表 -->
          <el-aside width="220px" class="device-template-list">
            <div class="list-header">
              <span>设备原型列表</span>
              <el-button type="primary" size="small" @click="handleAddTemplate">新建</el-button>
            </div>
            <el-tree
              ref="deviceTreeRef"
              :data="templateList"
              node-key="id"
              :props="defaultProps"
              @node-click="handleNodeClick"
              @node-contextmenu="handleContextMenu"
              highlight-current
              class="template-tree"
            >
              <template #default="{ node, data }">
                <div class="custom-tree-node">
                  <span>{{ node.label }}</span>
                </div>
              </template>
            </el-tree>
          </el-aside>
          
          <!-- 右侧点位信息 -->
          <el-main class="template-content">
            <template v-if="currentTemplate">
              <div class="template-header">
                <span>{{ currentTemplate.name }}</span>
              </div>
              <div class="template-detail">
                <el-descriptions :column="2" border size="small">
                  <el-descriptions-item label="模板名称">
                    <span :class="{ 'modified-field': isFieldModified('name') }">{{ currentTemplate.name }}</span>
                  </el-descriptions-item>
                  <el-descriptions-item label="模板ID">{{ currentTemplate.id }}</el-descriptions-item>
                  <el-descriptions-item label="创建时间">{{ currentTemplate.createTime }}</el-descriptions-item>
                  <el-descriptions-item label="更新时间">{{ currentTemplate.updateTime }}</el-descriptions-item>
                  <el-descriptions-item label="描述">
                    <span :class="{ 'modified-field': isFieldModified('description') }">{{ currentTemplate.description }}</span>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
              
              <!-- 点位信息表格 -->
              <div class="point-info">
                <div class="point-info-header">
                  <span>位号信息</span>
                  <div class="point-buttons">
                    <el-button type="primary" size="small" @click="handleAddPoint">添加位号</el-button>
                    <el-button type="success" size="small" @click="handleBatchAddPoints">批量添加位号</el-button>
                  </div>
                </div>
                <el-table
                  ref="pointTableRef"
                  :data="pointList"
                  style="width: 100%"
                  stripe
                  border
                  :row-class-name="getRowClass"
                  @row-contextmenu="handlePointContextMenu"
                >
                  <el-table-column prop="code" label="位号编码" min-width="20%">
                    <template #default="scope">
                      <el-input
                        v-model="scope.row.code"
                        size="small"
                        placeholder="请输入位号编码"
                        @input="handlePointFieldChange(scope.row, 'code')"
                        @blur="validatePointCode(scope.row)"
                        :class="{ 'modified-field': isPointModified(scope.row) }"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="name" label="位号描述" min-width="40%">
                    <template #default="scope">
                      <el-input
                        v-model="scope.row.name"
                        size="small"
                        placeholder="请输入位号描述"
                        @input="handlePointFieldChange(scope.row, 'name')"
                        :class="{ 'modified-field': isPointModified(scope.row) }"
                      />
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="address" label="寄存器地址" min-width="20%">
                    <template #default="scope">
                      <el-input
                        v-model="scope.row.address"
                        size="small"
                        placeholder="请输入寄存器地址"
                        @input="handlePointFieldChange(scope.row, 'address')"
                        :class="{ 'modified-field': isPointModified(scope.row) }"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="protocolType" label="协议类型" min-width="15%">
                    <template #default="scope">
                      <el-select
                        v-model="scope.row.protocolType"
                        size="small"
                        placeholder="请选择协议类型"
                        @change="handleProtocolTypeChange(scope.row)"
                        :class="{ 'modified-field': isPointModified(scope.row) }"
                        style="width: 100%"
                      >
                        <el-option 
                          v-for="protocol in protocolTemplates" 
                          :key="protocol.name" 
                          :label="protocol.name" 
                          :value="protocol.name" 
                        />
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column prop="type" label="数据类型" min-width="15%">
                    <template #default="scope">
                      <el-select
                        v-model="scope.row.type"
                        size="small"
                        placeholder="请选择数据类型"
                        @change="handlePointFieldChange(scope.row, 'type')"
                        :class="{ 'modified-field': isPointModified(scope.row) }"
                        style="width: 100%"
                        :disabled="!scope.row.protocolType"
                      >
                        <el-option 
                          v-for="typeOption in getDataTypeOptions(scope.row.protocolType)" 
                          :key="typeOption.value" 
                          :label="typeOption.text" 
                          :value="typeOption.value" 
                        />
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column fixed="right" label="操作" width="150">
                    <template #default="scope">
                      <el-button 
                        v-if="isNewPoint(scope.row)" 
                        link 
                        type="success" 
                        size="small" 
                        @click="confirmNewPoint(scope.row)"
                        :disabled="!canConfirmPoint(scope.row)"
                      >
                        确认
                      </el-button>
                      <el-button link type="danger" size="small" @click="handleDeletePoint(scope.row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </template>
            <template v-else>
              <el-empty description="请选择或创建一个设备模板" />
            </template>
          </el-main>
        </el-container>
      </div>
    </el-card>

    <!-- 右键菜单 - 设备模板 -->
    <div v-show="templateContextMenu.visible" class="context-menu" :style="{ left: templateContextMenu.x + 'px', top: templateContextMenu.y + 'px' }">
      <div class="context-menu-item" @click="handleAddTemplate">新建设备模板</div>
      <div class="context-menu-item" v-if="templateContextMenu.data" @click="handleEditTemplate">编辑设备模板</div>
      <div class="context-menu-item" v-if="templateContextMenu.data" @click="handleDeleteTemplate">删除设备模板</div>
    </div>

    <!-- 右键菜单 - 位号 -->
    <div v-show="pointContextMenu.visible" class="context-menu" :style="{ left: pointContextMenu.x + 'px', top: pointContextMenu.y + 'px' }">
      <div class="context-menu-item" @click="handleAddPoint">添加位号</div>
      <div class="context-menu-item" @click="handleDeletePoint(pointContextMenu.data)">删除位号</div>
    </div>

    <!-- 新建设备模板弹窗 -->
    <el-dialog
      v-model="templateDialogVisible"
      :title="templateForm.id ? '编辑设备模板' : '新建设备模板'"
      width="500px"
      destroy-on-close
    >
      <el-form :model="templateForm" label-width="100px" :rules="templateRules" ref="templateFormRef">
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="templateForm.name" placeholder="请输入设备模板名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="templateForm.description" type="textarea" placeholder="请输入设备模板描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="templateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitTemplateForm">确认</el-button>
        </span>
      </template>
    </el-dialog>



    <!-- 批量添加位号弹窗 -->
    <el-dialog
      v-model="batchPointDialogVisible"
      title="批量添加位号"
      width="40%"
      destroy-on-close
    >
      <el-form :model="batchPointForm" :rules="batchPointRules" ref="batchPointFormRef" label-width="120px">
        <el-form-item label="开始地址" prop="startAddress">
          <el-input v-model="batchPointForm.startAddress" placeholder="请输入开始地址" />
        </el-form-item>
        <el-form-item label="位号个数" prop="registerCount">
          <el-input-number 
            v-model="batchPointForm.registerCount" 
            :min="1" 
            placeholder="请输入位号个数"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="协议类型" prop="protocolType">
          <el-select 
            v-model="batchPointForm.protocolType" 
            placeholder="请选择协议类型"
            style="width: 100%"
            @change="handleBatchProtocolTypeChange"
          >
            <el-option 
              v-for="protocol in protocolTemplates" 
              :key="protocol.name" 
              :label="protocol.name" 
              :value="protocol.name" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数据类型" prop="registerType">
          <el-select 
            v-model="batchPointForm.registerType" 
            placeholder="请选择数据类型"
            style="width: 100%"
            :disabled="!batchPointForm.protocolType"
          >
            <el-option 
              v-for="typeOption in getDataTypeOptions(batchPointForm.protocolType)" 
              :key="typeOption.value" 
              :label="typeOption.text" 
              :value="typeOption.value" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="位号前缀" prop="namePrefix">
          <el-input v-model="batchPointForm.namePrefix" placeholder="请输入位号前缀" />
        </el-form-item>
                  <div class="dialog-tip">
            <p>说明：</p>
            <ul>
              <li>位号个数用于控制生成的位号数量</li>
              <li>位号编码格式：前缀 + 序号（如：AI001, AI002...）</li>
              <li>位号描述格式：前缀 + 序号（如：AI001, AI002...）</li>
              <li>地址将从开始地址开始按数据类型自动递增生成</li>
            </ul>
          </div>
      </el-form>
      
      <!-- 预览表格 -->
      <div v-if="previewPoints.length > 0" class="preview-section">
        <div class="preview-header">
          <span class="preview-title">预览将要创建的位号（共 {{ previewPoints.length }} 个）</span>
        </div>
        <el-table
          :data="previewPoints"
          style="width: 100%;"
          :height="240"
          border
          stripe
          size="small"
        >
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="code" label="位号编码" min-width="40%" align="center" />
          <el-table-column prop="address" label="寄存器地址" min-width="40%" align="center" />
        </el-table>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelBatchAdd">取消</el-button>
          <el-button type="primary" @click="previewBatchPoints" v-if="previewPoints.length === 0">预览</el-button>
          <el-button type="primary" @click="previewBatchPoints" v-if="previewPoints.length > 0">重新预览</el-button>
          <el-button type="success" @click="confirmBatchAddPoints" v-if="previewPoints.length > 0">确认添加</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入设备模板弹窗 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入设备模板"
      width="500px"
      destroy-on-close
    >
      <el-upload
        class="upload-demo"
        drag
        action=""
        :auto-upload="false"
        :on-change="handleFileChange"
        :limit="1"
        accept=".json"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将JSON文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            请上传JSON格式的设备模板文件
          </div>
        </template>
      </el-upload>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="importTemplate">确认导入</el-button>
        </span>
      </template>
    </el-dialog>

    <input ref="fileInputRef" type="file" accept=".json" style="display: none" @change="handleImportFile" />
  </div>
</template>

<script setup lang="ts" name="ConfigTemplate">
import { ref, reactive, onMounted, nextTick } from 'vue';
// @ts-ignore
import { ElMessage, ElMessageBox } from 'element-plus';
// @ts-ignore
import type { FormInstance } from 'element-plus';
import { UploadFilled, Refresh } from '@element-plus/icons-vue';
import { onBeforeRouteLeave } from 'vue-router';
import { 
  saveDeviceTemplate, 
  deleteDeviceTemplate, 
  getDeviceTemplateList,
  getConfigLinkTemplate
} from "@/api/modules/configuration";

// 定义位号类型接口
interface PointItem {
  id: string;
  code: string;
  name: string;
  description: string;
  address: string;
  type: string;
  protocolType?: string; // 添加协议类型字段
}

// 定义模板类型接口
interface TemplateItem {
  id: string;
  name: string;
  description: string;
  createTime: string;
  updateTime: string;
  createTimeStamp?: number | string; // 添加原始时间戳字段
  updateTimeStamp?: number | string; // 添加原始时间戳字段
  points?: PointItem[];
  [x: string]: any;
}

// 设备模板树配置
const defaultProps = {
  children: 'children',
  label: 'name'
};

// 设备原型列表
const templateList = ref<TemplateItem[]>([]);
const currentTemplate = ref<TemplateItem | null>(null);
const deviceTreeRef = ref();

// 协议模板列表
const protocolTemplates = ref<any[]>([]);

// 时间戳转换为指定格式的字符串
const formatTimestamp = (timestamp: number | string): string => {
  if (!timestamp) return '';
  
  const date = new Date(Number(timestamp));
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  const milliseconds = String(date.getMilliseconds()).padStart(3, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
};

// 获取协议模板列表
const fetchConfigLinkTemplate = async () => {
  try {
    const res = await getConfigLinkTemplate();
    console.log('协议模板数据:', res);
    
    if (res.result && res.result.resultCode === "0") {
      // 检查返回的数据结构
      if (res.data && Array.isArray(res.data)) {
        // 保存完整的协议模板数据
        protocolTemplates.value = res.data;
        console.log('解析后的协议模板:', protocolTemplates.value);
      } else {
        console.error('获取协议模板失败: 数据格式不正确');
      }
    } else {
      console.error('获取协议模板失败:', res.result?.resultError || '未知错误');
    }
  } catch (error) {
    console.error('获取协议模板失败:', error);
  }
};

// 获取设备模板列表
const fetchTemplateList = async () => {
  try {
    const res = await getDeviceTemplateList();
    if (res.result && res.result.resultCode === "0") {
      // 检查返回的数据结构
      if (res.data && res.data.template && Array.isArray(res.data.template)) {
        // 解析返回的模板数据
        templateList.value = res.data.template.map((template: any) => {
          return {
            id: template.id,
            name: template.name,
            description: template.description || '',
            createTime: formatTimestamp(template.createTime), // 转换为指定格式
            updateTime: formatTimestamp(template.modifyTime), // 转换为指定格式
            // 保存原始时间戳，以便在保存时使用
            createTimeStamp: template.createTime,
            updateTimeStamp: template.modifyTime,
            // 保存原始点位数据，以便在选择模板时使用
            points: template.points || []
          };
        });
        console.log('解析后的模板列表:', templateList.value);
      } else {
        templateList.value = [];
        console.warn('返回的数据结构不符合预期:', res.data);
      }
    } else {
      ElMessage.error(`获取模板列表失败: ${res.result?.resultError || '未知错误'}`);
    }
  } catch (error) {
    console.error('获取模板列表失败:', error);
    ElMessage.error('获取设备模板列表失败');
  }
};

// 位号列表
const pointList = ref<PointItem[]>([]);
const pointTableRef = ref();

// 刷新按钮加载状态
const refreshLoading = ref(false);

// 刷新模板列表
const refreshTemplateList = async () => {
  // 检查是否有未保存的修改
  if (isTemplateModified.value || checkUnsavedChanges()) {
    try {
      await ElMessageBox.confirm(
        '刷新将丢失当前未保存的修改，是否继续？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      );
      // 用户确认刷新，继续执行
    } catch (error) {
      // 用户取消刷新，直接返回
      return;
    }
  }
  
  refreshLoading.value = true;
  try {
    // 保存当前选中的模板ID
    const currentTemplateId = currentTemplate.value?.id;
    
    // 调用API获取最新的模板列表
    await fetchTemplateList();
    
    // 如果之前有选中的模板，尝试重新选中
    if (currentTemplateId) {
      const template = templateList.value.find(t => t.id === currentTemplateId);
      if (template) {
        // 重新选中之前的模板
        handleSelectTemplate(template);
        
        // 更新树的选中状态
        nextTick(() => {
          if (deviceTreeRef.value) {
            deviceTreeRef.value.setCurrentKey(currentTemplateId);
          }
        });
      } else {
        // 如果找不到之前选中的模板，清空当前选中
        currentTemplate.value = null;
        pointList.value = [];
        originalTemplate.value = null;
        originalPointList.value = [];
        isTemplateModified.value = false;
      }
    }
    
    ElMessage.success('刷新成功');
  } catch (error) {
    console.error('刷新失败:', error);
    ElMessage.error('刷新失败');
  } finally {
    refreshLoading.value = false;
  }
};

// 处理选择设备模板
const handleSelectTemplate = (data: TemplateItem) => {
  // 检查是否有未保存的修改
  if (isTemplateModified.value || checkUnsavedChanges()) {
    ElMessageBox.confirm(
      '当前有未保存的修改，切换模板将丢失这些修改，是否继续？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
      .then(() => {
        // 用户确认切换，加载选中的模板
        loadSelectedTemplate(data);
      })
      .catch(() => {
        // 用户取消切换，恢复树的选中状态
        nextTick(() => {
          if (deviceTreeRef.value && currentTemplate.value) {
            deviceTreeRef.value.setCurrentKey(currentTemplate.value.id);
          }
        });
      });
  } else {
    // 没有未保存的修改，直接加载选中的模板
    loadSelectedTemplate(data);
  }
};

// 加载选中的模板
const loadSelectedTemplate = (data: TemplateItem) => {
  // 设置当前选中的模板
  currentTemplate.value = data;
  
  // 从模板数据中获取位号信息
  const template = templateList.value.find(t => t.id === data.id);
  if (template && template.points) {
    // 解析位号数据
    pointList.value = template.points.map((point: any) => {
      return {
        id: point.id || generateRandomId(),
        code: point.code || '',
        name: point.name || '',
        description: point.description || '',
        address: point.address || '',
        type: point.type || '',
        protocolType: point.protocolType || '' // 添加协议类型字段
      };
    });
    
    // 更新原始位号列表，用于检测修改
    originalPointList.value = JSON.parse(JSON.stringify(pointList.value));
  } else {
    // 如果没有位号数据，设置为空数组
    pointList.value = [];
    originalPointList.value = [];
  }
  
  // 清空未确认位号列表
  unconfirmedPoints.value.clear();
  
  // 更新原始模板数据，用于检测修改
  originalTemplate.value = JSON.parse(JSON.stringify(currentTemplate.value));
  isTemplateModified.value = false;
  
  // 关闭右键菜单
  templateContextMenu.visible = false;
};

// 组件挂载时获取模板列表
onMounted(() => {
  // 获取协议模板列表
  fetchConfigLinkTemplate();
  // 获取设备模板列表
  fetchTemplateList();
});

// 右键菜单 - 设备模板
const templateContextMenu = reactive({
  visible: false,
  x: 0,
  y: 0,
  data: null as any
});

// 右键菜单 - 位号
const pointContextMenu = reactive({
  visible: false,
  x: 0,
  y: 0,
  data: null as any
});

// 设备模板表单
const templateDialogVisible = ref(false);
const templateFormRef = ref<FormInstance>();
const templateForm = reactive({
  id: '',
  name: '',
  description: ''
});
const templateRules = reactive({
  name: [
    { required: true, message: '请输入设备模板名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ]
});



// 添加修改状态跟踪
const isTemplateModified = ref(false);
const originalTemplate = ref<any>(null);
const originalPointList = ref<any[]>([]);

// 保存设备模板
const saveTemplate = async () => {
  if (!currentTemplate.value) {
    ElMessage.warning('请先选择一个设备模板');
    return;
  }
  
  try {
    // 获取当前时间戳
    const currentTimeStamp = Date.now();
    
    // 更新模板的更新时间
    currentTemplate.value.updateTimeStamp = currentTimeStamp;
    currentTemplate.value.updateTime = formatTimestamp(currentTimeStamp);
    
    // 准备要发送到后端的数据
    const templateData = {
      id: currentTemplate.value.id,
      name: currentTemplate.value.name,
      description: currentTemplate.value.description,
      createTime: currentTemplate.value.createTimeStamp || Date.now(), // 使用原始时间戳
      modifyTime: currentTemplate.value.updateTimeStamp, // 使用原始时间戳
      points: pointList.value.map(point => ({
        id: point.id,
        code: point.code,
        name: point.name,
        description: point.description,
        address: point.address,
        type: point.type,
        protocolType: point.protocolType || '' // 添加协议类型字段
      }))
    };
    
    // 调用API保存数据
    const res = await saveDeviceTemplate(templateData);
    
    // 检查API响应
    if (res.result && res.result.resultCode === "0") {
      ElMessage.success('设备模板保存成功');
      
      // 保存成功后重新加载设备模板列表以更新缓存数据
      await fetchTemplateList();
      
      // 重新选中当前模板以获取最新数据
      if (currentTemplate.value) {
        const updatedTemplate = templateList.value.find(t => t.id === currentTemplate.value!.id);
        if (updatedTemplate) {
          loadSelectedTemplate(updatedTemplate);
        }
      }
      
      // 更新原始数据，重置修改状态
      originalTemplate.value = JSON.parse(JSON.stringify(currentTemplate.value));
      originalPointList.value = JSON.parse(JSON.stringify(pointList.value));
      isTemplateModified.value = false;
    } else {
      // 如果code不是"0"，则表示失败
      ElMessage.error(`保存失败: ${res.result?.resultError || '未知错误'}`);
    }
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('设备模板保存失败');
  }
};

// 检查是否有未保存的修改
const checkUnsavedChanges = () => {
  if (!originalTemplate.value || !currentTemplate.value) return false;
  
  // 检查模板基本信息是否有变化
  if (originalTemplate.value.name !== currentTemplate.value.name || 
      originalTemplate.value.description !== currentTemplate.value.description) {
    return true;
  }
  
  // 检查位号列表是否有变化（简单比较长度和内容）
  if (originalPointList.value.length !== pointList.value.length) {
    return true;
  }
  
  // 深度比较位号内容是否有变化
  const pointsChanged = JSON.stringify(originalPointList.value) !== JSON.stringify(pointList.value);
  
  return pointsChanged;
};

// 检查特定字段是否被修改
const isFieldModified = (fieldName: string) => {
  if (!originalTemplate.value || !currentTemplate.value) return false;
  return originalTemplate.value[fieldName] !== currentTemplate.value[fieldName];
};

// 检查特定位号是否被修改
const isPointModified = (point: any) => {
  if (!originalPointList.value) return true; // 新增的位号
  
  const originalPoint = originalPointList.value.find(p => p.id === point.id);
  if (!originalPoint) return true; // 新增的位号
  
  // 比较位号的各个字段
  return JSON.stringify(originalPoint) !== JSON.stringify(point);
};

// 跟踪新增但未确认的位号
const unconfirmedPoints = ref<Set<string>>(new Set());

// 检查位号是否是新增的
const isNewPoint = (point: any) => {
  if (!originalPointList.value) return true;
  return !originalPointList.value.some(p => p.id === point.id);
};

// 检查位号是否可以确认（必填字段已填写）
const canConfirmPoint = (point: PointItem) => {
  return point.code && point.code.trim() !== '' && point.name && point.name.trim() !== '';
};

// 确认新增位号
const confirmNewPoint = (point: PointItem) => {
  // 验证必填字段
  if (!canConfirmPoint(point)) {
    ElMessage.warning('请填写位号编码和位号描述');
    return;
  }
  
  // 验证位号编码格式
  const codePattern = /^[A-Za-z0-9_]+$/;
  if (!codePattern.test(point.code)) {
    ElMessage.warning('位号编码只能包含字母、数字和下划线');
    return;
  }
  
  // 检查编码是否重复
  const duplicatePoint = pointList.value.find(p => p.id !== point.id && p.code === point.code);
  if (duplicatePoint) {
    ElMessage.warning('位号编码不能重复');
    return;
  }
  
  // 从未确认列表中移除
  unconfirmedPoints.value.delete(point.id);
  
  // 将位号添加到原始列表中，表示已确认
  originalPointList.value.push(JSON.parse(JSON.stringify(point)));
  
  ElMessage.success(`位号 "${point.name}" 确认成功`);
};

// 在表格行上添加类
const getRowClass = ({ row }: { row: any }) => {
  if (isNewPoint(row)) {
    return unconfirmedPoints.value.has(row.id) ? 'new-point unconfirmed-point' : 'new-point confirmed-point';
  }
  return '';
};

// 点击树节点
const handleNodeClick = (data: TemplateItem) => {
  // 使用handleSelectTemplate函数处理模板选择
  handleSelectTemplate(data);
};

// 右键菜单 - 设备模板
const handleContextMenu = (event: MouseEvent, data: any) => {
  event.preventDefault();
  event.stopPropagation();
  
  // 隐藏点位右键菜单
  pointContextMenu.visible = false;
  
  // 显示设备模板右键菜单
  templateContextMenu.x = event.clientX;
  templateContextMenu.y = event.clientY;
  templateContextMenu.data = data;
  templateContextMenu.visible = true;
  
  // 点击空白处关闭右键菜单
  const closeMenu = () => {
    templateContextMenu.visible = false;
    document.removeEventListener('click', closeMenu);
  };
  
  nextTick(() => {
    document.addEventListener('click', closeMenu);
  });
};

// 右键菜单 - 位号
const handlePointContextMenu = (row: any, column: any, event: MouseEvent) => {
  event.preventDefault();
  event.stopPropagation();
  
  // 隐藏设备模板右键菜单
  templateContextMenu.visible = false;
  
  // 显示位号右键菜单
  pointContextMenu.x = event.clientX;
  pointContextMenu.y = event.clientY;
  pointContextMenu.data = row;
  pointContextMenu.visible = true;
  
  // 点击空白处关闭右键菜单
  const closeMenu = () => {
    pointContextMenu.visible = false;
    document.removeEventListener('click', closeMenu);
  };
  
  nextTick(() => {
    document.addEventListener('click', closeMenu);
  });
};

// 新建设备模板
const handleAddTemplate = () => {
  templateContextMenu.visible = false;
  templateForm.id = '';
  templateForm.name = '';
  templateForm.description = '';
  templateDialogVisible.value = true;
};

// 编辑设备模板
const handleEditTemplate = () => {
  templateContextMenu.visible = false;
  
  if (!templateContextMenu.data) {
    ElMessage.warning('请先选择一个设备模板');
    return;
  }
  
  templateForm.id = templateContextMenu.data.id;
  templateForm.name = templateContextMenu.data.name;
  templateForm.description = templateContextMenu.data.description;
  templateDialogVisible.value = true;
};

// 删除设备模板
const handleDeleteTemplate = () => {
  templateContextMenu.visible = false;
  const data = templateContextMenu.data;
  if (data) {
    ElMessageBox.confirm(`确定要删除设备模板 "${data.name}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          // 查找完整的模板数据
          const fullTemplate = templateList.value.find(item => item.id === data.id);
          
          if (!fullTemplate) {
            ElMessage.error('找不到完整的模板数据');
            return;
          }
          
          // 准备完整的模板数据
          const templateData: any = {
            id: fullTemplate.id,
            name: fullTemplate.name,
            description: fullTemplate.description || '',
            createTime: fullTemplate.createTimeStamp || Date.now(),
            modifyTime: fullTemplate.updateTimeStamp || Date.now(),
            points: []
          };
          
          // 如果有点位数据，添加到模板中
          if (fullTemplate.points && Array.isArray(fullTemplate.points)) {
            templateData.points = fullTemplate.points.map((point: any) => ({
              id: point.id,
              code: point.code,
              name: point.name,
              description: point.description || '',
              address: point.address || '',
              type: point.type || '',
              pointType: point.pointType || ''
            }));
          }
          
          // 调用API删除模板，发送完整的模板数据
          const res = await deleteDeviceTemplate(templateData);
          
          // 检查API响应
          if (res.result && res.result.resultCode === "0") {
            ElMessage.success('删除成功');
            
            // 删除成功后重新加载设备模板列表以更新缓存数据
            await fetchTemplateList();
            
            // 如果当前选中的是被删除的模板，清空当前选中
            if (currentTemplate.value && currentTemplate.value.id === data.id) {
              currentTemplate.value = null;
              pointList.value = [];
              originalTemplate.value = null;
              originalPointList.value = [];
              isTemplateModified.value = false;
            }
          } else {
            // 如果code不是"0"，则表示失败
            ElMessage.error(`删除失败: ${res.result?.resultError || '未知错误'}`);
          }
        } catch (error) {
          console.error('删除失败:', error);
          ElMessage.error('删除失败');
        }
      })
      .catch(() => {
        // 取消删除
      });
  }
};

// 生成8位随机ID
const generateRandomId = () => {
  // 生成8位随机字母数字组合
  return Math.random().toString(36).substring(2, 10);
};

// 提交设备模板表单
const submitTemplateForm = async () => {
  if (!templateFormRef.value) return;
  
  await templateFormRef.value.validate(async (valid: boolean, fields?: any) => {
    if (valid) {
      if (templateForm.id) {
        // 编辑模式 - 只更新当前模板的基本信息，不调用API
        // 实际保存会在用户点击"保存模板"按钮时进行
        const index = templateList.value.findIndex(item => item.id === templateForm.id);
        if (index !== -1) {
          templateList.value[index] = {
            ...templateList.value[index],
            name: templateForm.name,
            description: templateForm.description,
            updateTime: formatTimestamp(Date.now())
          };
          
          if (currentTemplate.value && currentTemplate.value.id === templateForm.id) {
            currentTemplate.value.name = templateForm.name;
            currentTemplate.value.description = templateForm.description;
            currentTemplate.value.updateTime = formatTimestamp(Date.now());
            currentTemplate.value.updateTimeStamp = Date.now();
            // 标记为已修改
            isTemplateModified.value = true;
          }
        }
        ElMessage.success('设备模板信息更新成功，请点击保存模板按钮保存更改');
      } else {
        // 新建模式 - 创建空模板，不调用API
        // 实际保存会在用户添加点位并点击"保存模板"按钮时进行
        const newTemplate: TemplateItem = {
          id: generateRandomId(),
          name: templateForm.name,
          description: templateForm.description,
          createTime: formatTimestamp(Date.now()),
          updateTime: formatTimestamp(Date.now()),
          createTimeStamp: Date.now(),
          updateTimeStamp: Date.now(),
          points: []
        };
        
        templateList.value.push(newTemplate);
        
        // 自动选中新创建的模板
        currentTemplate.value = newTemplate;
        pointList.value = [];
        originalTemplate.value = JSON.parse(JSON.stringify(newTemplate));
        originalPointList.value = [];
        isTemplateModified.value = true; // 标记为已修改，提示用户需要保存
        
        ElMessage.success('设备模板创建成功，请添加点位并保存模板');
      }
      templateDialogVisible.value = false;
    }
  });
};

// 添加位号
const handleAddPoint = () => {
  pointContextMenu.visible = false;
  
  if (!currentTemplate.value) {
    ElMessage.warning('请先选择一个设备模板');
    return;
  }
  
  // 直接在表格中添加一行空白数据
  const newPoint: PointItem = {
    id: generateRandomId(),
    code: '',
    name: '',
    description: '',
    address: '',
    type: '',
    protocolType: '' // 添加协议类型字段
  };
  
  pointList.value.push(newPoint);
  
  // 将新位号ID添加到未确认列表
  unconfirmedPoints.value.add(newPoint.id);
  
  // 标记为已修改
  isTemplateModified.value = true;
  
  // 更新模板的更新时间
  if (currentTemplate.value) {
    const currentTimeStamp = Date.now();
    currentTemplate.value.updateTimeStamp = currentTimeStamp;
    currentTemplate.value.updateTime = formatTimestamp(currentTimeStamp);
  }
  
  ElMessage.success('已添加新位号，请填写必要信息后点击确认');
};

// 处理批量添加位号
const handleBatchAddPoints = () => {
  if (!currentTemplate.value) {
    ElMessage.warning('请先选择一个设备模板');
    return;
  }
  
  // 重置表单
  batchPointForm.startAddress = '';
  batchPointForm.registerCount = 1;
  batchPointForm.protocolType = ''; // 重置协议类型
  batchPointForm.registerType = '';
  batchPointForm.namePrefix = '';
  
  // 清除预览
  previewPoints.value = [];
  
  // 显示批量添加对话框
  batchPointDialogVisible.value = true;
};



// 预览批量添加位号
const previewBatchPoints = () => {
  if (!batchPointFormRef.value) return;
  
  batchPointFormRef.value.validate((valid: boolean, fields?: any) => {
    if (valid) {
      // 生成预览位号列表
      generatePreviewPoints();
    } else {
      console.log('表单验证失败:', fields);
    }
  });
};

// 生成预览位号列表
const generatePreviewPoints = () => {
  const points: any[] = [];
  const count = batchPointForm.registerCount;
  const prefix = batchPointForm.namePrefix;
  
  // 1. 先获取选择的数据类型对应的value值，统一转为字符串再转为全小写
  const dataTypeValue = String(batchPointForm.registerType || '').toLowerCase();
  
  // 2. 判断转换后的字符是否为特定的数据类型字符串
  const specificDataTypes = [
    'bool', 'boolean', 'bit', 'byte', 'sbyte', 'uint8', 'int8', 'uint16', 'int16', 
    'uint32', 'int32', 'uint64', 'int64', 'float', 'double'
  ];
  const isSpecificDataType = specificDataTypes.includes(dataTypeValue);
  
  // 解析开始地址（支持数字和字符串）
  let startAddr: number;
  if (typeof batchPointForm.startAddress === 'string' && /^\d+$/.test(batchPointForm.startAddress)) {
    startAddr = parseInt(batchPointForm.startAddress);
  } else {
    // 如果不是纯数字，直接使用字符串
    startAddr = 0; // 默认从0开始，后续可以改进支持更复杂的地址格式
  }
  
  for (let i = 0; i < count; i++) {
    // 生成位号编码，格式：前缀+序号（3位补零）
    const code = `${prefix}${String(i + 1).padStart(3, '0')}`;
    
    // 计算地址
    let address: string;
    
    if (!isSpecificDataType) {
      // 3. 如果不是特定数据类型，则按照填写的开始地址依次+1计算位号寄存器地址
      if (typeof batchPointForm.startAddress === 'string' && /^\d+$/.test(batchPointForm.startAddress)) {
        address = String(startAddr + i);
      } else {
        // 如果开始地址不是数字，直接使用原始格式加偏移
        address = `${batchPointForm.startAddress}+${i}`;
      }
    } else {
      // 如果是特定数据类型，根据数据类型占据的寄存器大小递增
      if (typeof batchPointForm.startAddress === 'string' && /^\d+$/.test(batchPointForm.startAddress)) {
        // 根据数据类型计算地址递增规则
        if (dataTypeValue === 'bool' || dataTypeValue === 'boolean' || dataTypeValue === 'bit') {
          // bool/boolean/bit类型：按位递增，每个寄存器16位
          const registerIndex = Math.floor(i / 16); // 寄存器索引
          const bitIndex = i % 16; // 位索引
          address = `${startAddr + registerIndex}.${bitIndex}`;
        } else {
          // 其他类型：根据位宽递增
          let registerSize: number; // 占用的寄存器数量
          
          switch (dataTypeValue) {
            case 'byte':
            case 'sbyte':
            case 'int8':
            case 'uint8':
              // 8位类型，按照16位(1个寄存器)的位宽算
              registerSize = 1;
              break;
            case 'int16':
            case 'uint16':
              // 16位类型，占1个寄存器
              registerSize = 1;
              break;
            case 'int32':
            case 'uint32':
            case 'float':
              // 32位类型，占2个寄存器
              registerSize = 2;
              break;
            case 'int64':
            case 'uint64':
            case 'double':
              // 64位类型，占4个寄存器
              registerSize = 4;
              break;
            default:
              // 默认按1个寄存器计算
              registerSize = 1;
          }
          
          address = String(startAddr + i * registerSize);
        }
      } else {
        // 如果开始地址不是数字，直接使用原始格式加偏移
        address = `${batchPointForm.startAddress}+${i}`;
      }
    }
    
    points.push({
      code: code,
      address: address
    });
  }
  
  previewPoints.value = points;
};



// 取消批量添加
const cancelBatchAdd = () => {
  // 清除预览数据
  previewPoints.value = [];
  // 关闭对话框
  batchPointDialogVisible.value = false;
};

// 确认批量添加位号
const confirmBatchAddPoints = () => {
  // 检查是否有预览数据
  if (previewPoints.value.length === 0) {
    ElMessage.warning('请先预览位号');
    return;
  }
  
  // 检查是否选中了模板
  if (!currentTemplate.value) {
    ElMessage.warning('请先选择一个设备模板');
    return;
  }
  
  try {
    // 检查是否有重复的位号编码
    const existingCodes = pointList.value.map((p: any) => p.code);
    const duplicateCodes = previewPoints.value.filter(point => 
      existingCodes.includes(point.code)
    );
    
    if (duplicateCodes.length > 0) {
      const duplicateCodesStr = duplicateCodes.map(p => p.code).join(', ');
      ElMessage.warning(`以下位号编码已存在，请修改后重试：${duplicateCodesStr}`);
      return;
    }
    
    // 创建新的位号对象
    const newPoints = previewPoints.value.map(previewPoint => ({
      id: generateRandomId(),
      code: previewPoint.code,
      name: previewPoint.code, // 位号描述与编码相同
      description: previewPoint.code, // 描述也与编码相同
      address: previewPoint.address,
      type: batchPointForm.registerType || '', // 使用表单中选择的数据类型
      protocolType: batchPointForm.protocolType || '' // 使用表单中选择的协议类型
    }));
    
    // 添加到位号列表
    pointList.value.push(...newPoints);
    
    // 批量添加的位号直接添加到原始列表中，表示已确认
    originalPointList.value.push(...newPoints.map(point => JSON.parse(JSON.stringify(point))));
    
    // 标记为已修改
    isTemplateModified.value = true;
    
    // 更新模板的更新时间
    if (currentTemplate.value) {
      const currentTimeStamp = Date.now();
      currentTemplate.value.updateTimeStamp = currentTimeStamp;
      currentTemplate.value.updateTime = formatTimestamp(currentTimeStamp);
    }
    
    // 显示成功消息
    ElMessage.success(`成功添加 ${newPoints.length} 个位号，可直接保存模板`);
    
    // 关闭对话框
    batchPointDialogVisible.value = false;
    
    // 清除预览数据
    previewPoints.value = [];
    
    console.log('批量添加的位号:', newPoints);
    
  } catch (error) {
    console.error('批量添加位号失败:', error);
    ElMessage.error('批量添加位号失败');
  }
};

// 删除位号
const handleDeletePoint = (row: any) => {
  pointContextMenu.visible = false;
  
  const pointName = row.name || row.code || '未命名位号';
  ElMessageBox.confirm(`确定要删除位号 "${pointName}" 吗？`, '删除确认', {
    confirmButtonText: '确定删除',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // 从位号列表中移除
      pointList.value = pointList.value.filter(item => item.id !== row.id);
      
      // 从未确认列表中移除（如果存在）
      unconfirmedPoints.value.delete(row.id);
      
      // 标记为已修改
      isTemplateModified.value = true;
      
      // 更新模板的更新时间
      if (currentTemplate.value) {
        const currentTimeStamp = Date.now();
        currentTemplate.value.updateTimeStamp = currentTimeStamp;
        currentTemplate.value.updateTime = formatTimestamp(currentTimeStamp);
      }
      
      ElMessage.success(`位号 "${pointName}" 删除成功`);
    })
    .catch(() => {
      // 取消删除
    });
};



// 获取指定协议类型的数据类型选项
const getDataTypeOptions = (protocolType: string) => {
  if (!protocolType) return [];
  
  // 查找对应的协议模板
  const template = protocolTemplates.value.find(t => t.name === protocolType);
  if (!template || !template.point) {
    return [];
  }
  
  // 查找数据类型字段的配置（level为4的字段）
  const typeConfig = template.point.find((p: any) => p.level === 4);
  if (typeConfig && typeConfig.inputType === 'select' && typeConfig.items) {
    return typeConfig.items;
  }
  
  // 如果没有找到配置，返回默认的数据类型选项
  return [
    { text: 'Boolean', value: 'Boolean' },
    { text: 'Int16', value: 'Int16' },
    { text: 'UInt16', value: 'UInt16' },
    { text: 'Int32', value: 'Int32' },
    { text: 'UInt32', value: 'UInt32' },
    { text: 'Float', value: 'Float' },
    { text: 'Double', value: 'Double' },
    { text: 'String', value: 'String' }
  ];
};

// 处理协议类型变更
const handleProtocolTypeChange = (row: PointItem) => {
  // 当协议类型变更时，清空数据类型选择
  row.type = '';
  
  // 标记为已修改
  handlePointFieldChange(row, 'protocolType');
};

// 处理批量添加中的协议类型变更
const handleBatchProtocolTypeChange = () => {
  // 当协议类型变更时，清空数据类型选择
  batchPointForm.registerType = '';
};

// 处理位号字段变更（表格内编辑）
const handlePointFieldChange = (row: PointItem, field: string) => {
  // 如果是编码字段且之前为空，自动填充描述字段
  if (field === 'code' && row.code && !row.name) {
    row.name = row.code; // 使用编码作为默认描述
  }
  
  // 标记为已修改
  isTemplateModified.value = true;
  
  // 更新模板的更新时间
  if (currentTemplate.value) {
    const currentTimeStamp = Date.now();
    currentTemplate.value.updateTimeStamp = currentTimeStamp;
    currentTemplate.value.updateTime = formatTimestamp(currentTimeStamp);
  }
};

// 验证位号编码
const validatePointCode = (row: PointItem) => {
  if (!row.code) {
    ElMessage.warning('位号编码不能为空');
    return;
  }
  
  // 检查编码格式（只能包含字母、数字和下划线）
  const codePattern = /^[A-Za-z0-9_]+$/;
  if (!codePattern.test(row.code)) {
    ElMessage.warning('位号编码只能包含字母、数字和下划线');
    return;
  }
  
  // 检查编码是否重复
  const duplicatePoint = pointList.value.find(point => point.id !== row.id && point.code === row.code);
  if (duplicatePoint) {
    ElMessage.warning('位号编码不能重复');
    return;
  }
};

// 页面加载时获取设备模板列表已在上面的onMounted中处理，此处删除重复调用

// 点击页面空白处关闭右键菜单
window.addEventListener('click', () => {
  templateContextMenu.visible = false;
  pointContextMenu.visible = false;
});

// 批量添加位号
const batchPointDialogVisible = ref(false);
const batchPointFormRef = ref<FormInstance>();
const batchPointForm = reactive({
  startAddress: '',
  registerCount: 1,
  protocolType: '', // 添加协议类型字段
  registerType: '',
  namePrefix: ''
});

// 批量添加位号表单验证规则
const batchPointRules = reactive({
  startAddress: [
    { required: true, message: '请输入开始地址', trigger: 'blur' }
  ],
  registerCount: [
    { required: true, message: '请输入位号个数', trigger: 'change' },
    { type: 'number', min: 1, message: '位号个数必须大于0', trigger: 'change' }
  ],
  protocolType: [
    { required: true, message: '请选择协议类型', trigger: 'change' }
  ],
  registerType: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ],
  namePrefix: [
    { required: true, message: '请输入位号前缀', trigger: 'blur' },
    { pattern: /^[A-Za-z0-9_]+$/, message: '位号前缀只能包含字母、数字和下划线', trigger: 'blur' }
  ]
});

// 预览位号列表
const previewPoints = ref<any[]>([]);

// 导入设备模板
const importDialogVisible = ref(false);
const importFile = ref<File | null>(null);
const fileInputRef = ref<HTMLInputElement | null>(null);

// 处理导入按钮点击
const handleImportTemplate = () => {
  importDialogVisible.value = true;
  importFile.value = null;
};

// 处理文件选择变更
const handleFileChange = (file: any) => {
  if (file && file.raw) {
    console.log('选择的文件:', file.name);
    importFile.value = file.raw;
  } else {
    console.warn('文件选择无效');
    importFile.value = null;
  }
};

// 执行导入功能
const importTemplate = () => {
  if (!importFile.value) {
    ElMessage.warning('请先选择要导入的JSON文件');
    return;
  }

  const reader = new FileReader();
  reader.onload = (event) => {
    try {
      // 获取文件内容
      const content = event.target?.result as string;
      console.log('原始文件内容:', content);
      
      // 解析JSON
      const importData = JSON.parse(content);
      console.log('解析后的数据:', importData);
      
      // 基本验证
      if (!importData.name) {
        ElMessage.error('导入失败: 模板名称缺失');
        return;
      }
      
      // 创建新模板对象
      const newTemplate: TemplateItem = {
        id: generateRandomId(), // 生成新ID确保唯一性
        name: importData.name,
        description: importData.description || '',
        createTime: formatTimestamp(Date.now()),
        updateTime: formatTimestamp(Date.now()),
        createTimeStamp: Date.now(),
        updateTimeStamp: Date.now()
      };
      
      // 处理点位数据
      let newPoints: PointItem[] = [];
      
      // 检查points数组是否存在且有效
      if (importData.points && Array.isArray(importData.points) && importData.points.length > 0) {
        console.log(`发现${importData.points.length}个位号数据`);
        
        // 处理每个位号
        newPoints = importData.points.map((point: any) => {
          console.log('处理位号:', point);
          
          // 创建新位号对象
          return {
            id: generateRandomId(),
            code: point.code || '',
            name: point.name || '',
            description: point.description || point.name || '', // 如果没有description，使用name作为默认值
            address: point.address || '',
            type: point.type || '',
            protocolType: point.protocolType || '' // 添加协议类型字段
          };
        });
      } else {
        console.warn('导入的模板没有位号数据或格式不正确');
      }
      
      console.log('处理后的位号列表:', newPoints);
      
      // 先添加模板到列表
      templateList.value.push(newTemplate);
      
      // 设置当前选中的模板
      currentTemplate.value = newTemplate;
      
      // 确保位号列表是响应式的
      pointList.value = [...newPoints];
      
      console.log('设置后的位号列表:', pointList.value);
      
      // 更新原始数据，设置修改状态为已修改，这样用户需要点击保存按钮才会保存
      originalTemplate.value = JSON.parse(JSON.stringify(newTemplate));
      originalPointList.value = JSON.parse(JSON.stringify(newPoints));
      isTemplateModified.value = true; // 设置为已修改状态，提示用户需要保存
      
      // 关闭对话框并显示成功消息
      importDialogVisible.value = false;
      ElMessage.success(`成功导入模板"${newTemplate.name}"，包含${newPoints.length}个位号，请点击保存按钮保存模板`);
      
      // 使用nextTick确保DOM更新后再选中模板
      nextTick(() => {
        // 强制刷新视图
        if (deviceTreeRef.value) {
          deviceTreeRef.value.setCurrentKey(newTemplate.id);
        }
        
        // 强制刷新位号表格
        if (pointTableRef.value) {
          pointTableRef.value.doLayout();
        }
      });
      
    } catch (error) {
      console.error('导入错误:', error);
      ElMessage.error('导入失败: JSON格式不正确');
    }
  };
  
  // 开始读取文件
  reader.readAsText(importFile.value);
};

// 处理直接通过input导入文件
const handleImportFile = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    importFile.value = input.files[0];
    importTemplate();
  }
};

// 导出设备模板
const handleExportTemplate = () => {
  if (!currentTemplate.value) {
    ElMessage.warning('请先选择一个设备模板');
    return;
  }
  
  // 准备导出数据 - 与保存格式保持一致
  const exportData = {
    id: currentTemplate.value.id,
    name: currentTemplate.value.name,
    description: currentTemplate.value.description,
    createTime: currentTemplate.value.createTimeStamp || Date.now(), // 使用原始时间戳
    modifyTime: currentTemplate.value.updateTimeStamp || Date.now(), // 使用原始时间戳
    points: pointList.value.map(point => ({
      id: point.id, // 包含id字段，与保存格式一致
      code: point.code,
      name: point.name,
      description: point.description,
      address: point.address,
      type: point.type,
      protocolType: point.protocolType || '' // 添加协议类型字段
    }))
  };
  
  // 创建Blob对象并下载
  const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `${currentTemplate.value.name}_template.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
  
  ElMessage.success('设备模板导出成功');
};

// 页面离开前检查未保存的修改
onBeforeRouteLeave((to, from, next) => {
  if (isTemplateModified.value || checkUnsavedChanges()) {
    ElMessageBox.confirm(
      '当前有未保存的修改，离开页面将丢失这些修改，是否继续？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
      .then(() => {
        // 放弃修改，恢复原始更新时间
        if (currentTemplate.value && originalTemplate.value) {
          currentTemplate.value.updateTime = originalTemplate.value.updateTime;
        }
        next();
      })
      .catch(() => {
        next(false);
      });
  } else {
    next();
  }
});

// 获取修改的数量
const getModifiedCount = () => {
  let count = 0;
  
  // 如果是新导入的模板，显示为"新"而不是数字
  if (isTemplateModified.value && pointList.value.length > 0 && 
      (!originalPointList.value || originalPointList.value.length === 0)) {
    return '新';
  }
  
  // 检查模板基本信息
  if (originalTemplate.value && currentTemplate.value) {
    if (originalTemplate.value.name !== currentTemplate.value.name) count++;
    if (originalTemplate.value.description !== currentTemplate.value.description) count++;
  }
  
  // 检查位号列表
  if (originalPointList.value && pointList.value) {
    // 检查是否有新增的位号
    const newPoints = pointList.value.filter(point => 
      !originalPointList.value.some(p => p.id === point.id)
    );
    count += newPoints.length;
    
    // 检查是否有修改的位号
    const modifiedPoints = pointList.value.filter(point => {
      const originalPoint = originalPointList.value.find(p => p.id === point.id);
      if (!originalPoint) return false; // 新增的位号已经在上面计算过了
      
      return JSON.stringify(originalPoint) !== JSON.stringify(point);
    });
    count += modifiedPoints.length;
    
    // 检查是否有删除的位号
    const deletedPoints = originalPointList.value.filter(point => 
      !pointList.value.some(p => p.id === point.id)
    );
    count += deletedPoints.length;
  }
  
  // 如果是导入的新模板但计数为0，至少显示为1
  if (count === 0 && isTemplateModified.value) {
    count = 1;
  }
  
  return count;
};
</script>

<style scoped lang="scss">
.container {
  width: 100%;
  height: 100%;
  padding: 0;
  box-sizing: border-box;
  
  .main-card {
    height: 100%;
    
    :deep(.el-card__header) {
      padding: 12px 15px;
    }
    
    :deep(.el-card__body) {
      padding: 10px 15px;
      height: calc(100% - 60px);
    }
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: none;
    padding-bottom: 0;
  }
  
  .header-buttons {
    display: flex;
    gap: 8px;
  }
  
  .card-content {
    height: 100%;
    
    .device-template-list {
      border-right: 1px solid #ebeef5;
      padding: 5px;
      background-color: #f9f9f9;
      border-radius: 4px;
      margin-right: 8px;
      
      .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        font-weight: bold;
        font-size: 14px;
        padding: 0 5px;
      }
      
      .template-tree {
        height: calc(100% - 40px);
        background-color: transparent;
        
        :deep(.el-tree-node__content) {
          height: 32px;
          border-radius: 4px;
          margin-bottom: 2px;
          
          &:hover {
            background-color: #f0f2f5;
          }
        }
      }
      
      .custom-tree-node {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        font-size: 14px;
      }
    }
    
    .template-content {
      padding: 5px 0 5px 10px;
      
      .template-header {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 10px;
        padding-left: 5px;
        border-left: 3px solid var(--el-color-primary);
      }
      
      .template-detail {
        margin-bottom: 15px;
        
        :deep(.el-descriptions) {
          .el-descriptions__header {
            margin-bottom: 10px;
          }
          
          .el-descriptions__label {
            font-weight: bold;
            color: #606266;
          }
        }
      }
      
      .point-info {
        .point-info-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
          font-weight: bold;
          font-size: 14px;
          padding-left: 5px;
          border-left: 3px solid var(--el-color-primary);
          
          .point-buttons {
            display: flex;
            gap: 8px;
          }
        }
        
        :deep(.el-table) {
          border-radius: 4px;
          overflow: hidden;
          
          .el-table__header th {
            font-size: 14px;
            background-color: #f5f7fa;
            height: 45px;
            font-weight: 600;
          }
          
          .el-table__row {
            height: 50px; // 增加行高以适应输入框
          }
          
          .el-table--striped .el-table__body tr.el-table__row--striped td {
            background-color: #f9fafc;
          }
          
          // 表格内输入框样式
          .el-input {
            .el-input__wrapper {
              border: none;
              box-shadow: none;
              background-color: transparent;
              
              &:hover {
                box-shadow: 0 0 0 1px var(--el-color-primary) inset;
              }
              
              &.is-focus {
                box-shadow: 0 0 0 1px var(--el-color-primary) inset;
              }
            }
          }
          
          // 表格内选择器样式
          .el-select {
            .el-select__wrapper {
              border: none;
              box-shadow: none;
              background-color: transparent;
              
              &:hover {
                box-shadow: 0 0 0 1px var(--el-color-primary) inset;
              }
              
              &.is-focus {
                box-shadow: 0 0 0 1px var(--el-color-primary) inset;
              }
            }
          }
          
          // 修改字段的特殊样式
          .modified-field {
            .el-input__wrapper {
              background-color: rgba(64, 158, 255, 0.1);
            }
            
            .el-select__wrapper {
              background-color: rgba(64, 158, 255, 0.1);
            }
          }
        }
      }
    }
  }

  /* 右键菜单样式 */
  .context-menu {
    position: fixed;
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 4px 0;
    z-index: 2000;
    
    .context-menu-item {
      padding: 6px 12px;
      cursor: pointer;
      font-size: 14px;
      
      &:hover {
        background-color: #f5f7fa;
        color: var(--el-color-primary);
      }
    }
  }
}

/* 添加到现有样式中 */
.modified-field {
  font-weight: bold;
  color: #409EFF; /* 使用Element Plus的主色调 */
  position: relative;
}

/* 可选：添加一个小标记 */
.modified-field::after {
  content: '*';
  position: absolute;
  top: -5px;
  right: -8px;
  color: #F56C6C; /* 使用Element Plus的危险色 */
  font-size: 14px;
}

/* 可选：为新增的位号添加不同的样式 */
.new-point {
  background-color: rgba(64, 158, 255, 0.05);
  
  td {
    border-left: 3px solid var(--el-color-primary);
  }
}

/* 未确认的新增位号样式 */
.unconfirmed-point {
  background-color: rgba(255, 193, 7, 0.1);
  
  td {
    border-left: 3px solid #ffc107;
    position: relative;
  }
  
  td::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 1px dashed #ffc107;
    pointer-events: none;
  }
}

/* 已确认的新增位号样式 */
.confirmed-point {
  background-color: rgba(40, 167, 69, 0.15);
  
  td {
    border-left: 4px solid #28a745;
    box-shadow: inset 0 0 0 1px rgba(40, 167, 69, 0.2);
  }
}

.has-changes {
  position: relative;
}

.changes-badge {
  margin-left: 8px;
}

/* 为新导入的模板添加特殊样式 */
.el-badge__content.is-fixed.is-dot {
  right: 5px;
}

/* 自定义徽章样式，支持显示"新"字 */
:deep(.el-badge__content:not(.is-dot)) {
  min-width: 20px;
  height: 20px;
  line-height: 20px;
  padding: 0 6px;
  border-radius: 10px;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

/* 批量添加位号弹窗样式 */
.dialog-tip {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
  margin-top: 16px;
  
  p {
    margin: 0 0 8px 0;
    font-weight: bold;
    color: #495057;
  }
  
  ul {
    margin: 0;
    padding-left: 16px;
    
    li {
      margin-bottom: 4px;
      color: #6c757d;
      font-size: 13px;
      line-height: 1.4;
    }
  }
}

.preview-section {
  margin-top: 20px;
  border-top: 1px solid #e9ecef;
  padding-top: 16px;
  
  .preview-header {
    margin-bottom: 12px;
    
    .preview-title {
      font-weight: bold;
      color: #495057;
      font-size: 14px;
    }
  }
}
</style> 
