@echo off
setlocal enabledelayedexpansion

if "%1"=="" (
    call :start_processes
    goto :eof
)

if /i "%1"=="start" (
    call :start_processes
    goto :eof
)

if /i "%1"=="stop" (
    call :stop_processes
    goto :eof
)

if /i "%1"=="status" (
    call :status_processes
    goto :eof
)

echo Usage: %0 {start^|stop^|status}
exit /b 1

:stop_processes
echo Stopping processes...

REM Stop DriverX.exe directly
tasklist | findstr /i "DriverX.exe" >nul 2>&1
if !errorlevel! equ 0 (
    echo Stopping DriverX.exe...
    taskkill /im DriverX.exe /f >nul 2>&1
)



REM Stop cmd processes running rungateway
wmic process where "name='cmd.exe' and commandline like '%%rungateway%%'" delete >nul 2>&1

echo All processes stopped.
goto :eof

:start_processes
call :stop_processes

echo Starting rungateway.bat...
set "exe_dir=%~dp0"
cd /d "%exe_dir%"

REM Create VBS script to run rungateway.bat completely hidden
echo Set objShell = CreateObject("WScript.Shell") > "%temp%\runhidden.vbs"
echo objShell.Run "cmd /c cd /d ""%exe_dir%"" && rungateway.bat", 0, False >> "%temp%\runhidden.vbs"

REM Run the VBS script to start rungateway.bat hidden
cscript //nologo "%temp%\runhidden.vbs"

REM Clean up temp VBS file
del "%temp%\runhidden.vbs" >nul 2>&1

echo rungateway.bat started in background (no window).
goto :eof

:status_processes
echo Checking process status...

REM Check if rungateway.bat is running by looking for cmd processes with rungateway in command line
wmic process where "name='cmd.exe' and commandline like '%%rungateway%%'" get processid /format:value 2>nul | findstr "ProcessId" >nul 2>&1
if !errorlevel! equ 0 (
    echo rungateway.bat is running.
) else (
    echo rungateway.bat is not running.
)



tasklist | findstr /i "DriverX.exe" >nul 2>&1
if !errorlevel! equ 0 (
    echo DriverX.exe is running.
) else (
    echo DriverX.exe is not running.
)
goto :eof 