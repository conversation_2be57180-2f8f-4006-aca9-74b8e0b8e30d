<template>
  <el-drawer v-model="drawerVisible" :destroy-on-close="true" size="520px" title="控制设置">
    <el-form ref="ruleFormRef" label-width="120px" label-suffix=" :" :model="drawerProps">
      <el-form-item label="链路名称" prop="linkName">
        {{ drawerProps.linkName }}
      </el-form-item>
      <el-form-item label="设备名称" prop="deviceName">
        {{ drawerProps.deviceName }}
      </el-form-item>
      <el-form-item label="位号名" prop="code">
        {{ drawerProps.code }}
      </el-form-item>
      <el-form-item label="位号描述" prop="name">
        {{ drawerProps.name }}
      </el-form-item>
      <el-form-item label="数据类型" prop="dataType">
        {{ drawerProps.dataType }}
      </el-form-item>
      <el-form-item label="寄存器地址" prop="address">
        {{ drawerProps.address }}
      </el-form-item>
      <el-form-item label="当前值" prop="currentValue">
        {{ drawerProps.currentValue }}
      </el-form-item>
      
      <!-- 控制设置区域 -->
      <el-divider content-position="left">控制设置</el-divider>
      <el-form-item label="控制值" prop="controlValue">
        <el-col :span="16">
          <el-select 
            v-if="isBooleanType" 
            v-model="drawerProps.controlValue" 
            placeholder="请选择控制值"
            style="width: 100%"
          >
            <el-option label="True" :value="true" />
            <el-option label="False" :value="false" />
          </el-select>
          <el-input-number 
            v-else
            v-model="drawerProps.controlValue" 
            placeholder="请输入控制值"
            style="width: 100%"
            :precision="undefined"
            :step="1"
            :controls="false"
          />
        </el-col>
        <el-col :span="2"></el-col>
        <el-col :span="6">
          <el-button type="primary" @click="handleControl">下发</el-button>
        </el-col>
      </el-form-item>
    </el-form>
  </el-drawer>
</template>

<script setup lang="ts" name="ControlDrawer">
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { setConfigPointValue } from '@/api/modules/configuration';

interface RowData {
  linkId?: string;
  linkName?: string;
  deviceId?: string;
  deviceName?: string;
  pointId?: string;
  code?: string;
  name?: string;
  dataType?: string;
  address?: string;
  currentValue?: any;
  controlValue?: any;
}

// drawer框状态
const drawerVisible = ref(false);
const drawerProps = ref<RowData>({});

// 判断是否为布尔类型
const isBooleanType = computed(() => {
  const dataType = drawerProps.value.dataType?.toLowerCase();
  return dataType === 'bool' || dataType === 'boolean' || dataType === 'bit';
});

// 接收父组件传过来的参数
const acceptParams = (params: RowData): void => {
  drawerProps.value = { 
    ...params, 
    controlValue: params.currentValue ?? "" 
  };
  drawerVisible.value = true;
};

// 处理控制命令下发
const handleControl = async () => {
  console.log('下发控制命令:', {
    deviceId: drawerProps.value.deviceId,
    code: drawerProps.value.code,
    controlValue: drawerProps.value.controlValue
  });
  
  try {
    // 构造控制下发的参数结构体
    const controlParams = {
      linkId: drawerProps.value.linkId || '',
      linkName: drawerProps.value.linkName || '',
      deviceId: drawerProps.value.deviceId || '',
      deviceName: drawerProps.value.deviceName || '',
      pointId: drawerProps.value.pointId || '',     // 位号ID
      pointCode: drawerProps.value.code || '',      // 位号Code
      pointName: drawerProps.value.name || '',      // 位号描述
      value: drawerProps.value.controlValue
    };
    
    console.log('发送控制参数:', controlParams);
    
    // 调用后端API进行控制下发
    const res = await setConfigPointValue(controlParams);
    
    if (res.result && res.result.resultCode === "0") {
      ElMessage.success('控制命令下发成功');
      drawerVisible.value = false;
    } else {
      ElMessage.error('控制命令下发失败: ' + (res.result?.resultError || '未知错误'));
    }
  } catch (error) {
    console.error('控制命令下发失败:', error);
    ElMessage.error('控制命令下发失败');
  }
};

defineExpose({
  acceptParams
});
</script>

<style scoped>
.el-divider {
  margin: 20px 0;
}
</style> 