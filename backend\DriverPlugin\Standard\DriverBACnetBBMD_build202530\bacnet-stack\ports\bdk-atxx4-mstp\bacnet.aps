<AVRStudio><MANAGEMENT><ProjectName>bacnet</ProjectName><Created>29-Apr-2009 08:16:53</Created><LastEdit>07-Oct-2010 10:30:19</LastEdit><ICON>241</ICON><ProjectType>0</ProjectType><Created>29-Apr-2009 08:16:53</Created><Version>4</Version><Build>4, 15, 0, 623</Build><ProjectTypeName>AVR GCC</ProjectTypeName></MANAGEMENT><CODE_CREATION><ObjectFile>bacnet.elf</ObjectFile><EntryFile></EntryFile><SaveFolder>C:\code\bacnet-stack\ports\bdk-atxx4-mstp\</SaveFolder></CODE_CREATION><DEBUG_TARGET><CURRENT_TARGET>JTAGICE mkII</CURRENT_TARGET><CURRENT_PART>ATmega644P.xml</CURRENT_PART><BREAKPOINTS></BREAKPOINTS><IO_EXPAND><HIDE>false</HIDE></IO_EXPAND><REGISTERNAMES><Register>R00</Register><Register>R01</Register><Register>R02</Register><Register>R03</Register><Register>R04</Register><Register>R05</Register><Register>R06</Register><Register>R07</Register><Register>R08</Register><Register>R09</Register><Register>R10</Register><Register>R11</Register><Register>R12</Register><Register>R13</Register><Register>R14</Register><Register>R15</Register><Register>R16</Register><Register>R17</Register><Register>R18</Register><Register>R19</Register><Register>R20</Register><Register>R21</Register><Register>R22</Register><Register>R23</Register><Register>R24</Register><Register>R25</Register><Register>R26</Register><Register>R27</Register><Register>R28</Register><Register>R29</Register><Register>R30</Register><Register>R31</Register></REGISTERNAMES><COM>Auto</COM><COMType>0</COMType><WATCHNUM>0</WATCHNUM><WATCHNAMES><Pane0><Variables>char_string</Variables><Variables>apdu</Variables><Variables>pkt</Variables></Pane0><Pane1></Pane1><Pane2></Pane2><Pane3></Pane3></WATCHNAMES><BreakOnTrcaeFull>0</BreakOnTrcaeFull></DEBUG_TARGET><Debugger><modules><module><map private="c:\avrdev\gcc\build-avr\gcc\" public="C:\code\bacnet-stack\ports\bdk-atxx4-mstp\"/></module></modules><Triggers></Triggers></Debugger><AVRGCCPLUGIN><FILES><SOURCEFILE>main.c</SOURCEFILE><SOURCEFILE>timer2.c</SOURCEFILE><SOURCEFILE>eeprom.c</SOURCEFILE><SOURCEFILE>init.c</SOURCEFILE><SOURCEFILE>input.c</SOURCEFILE><SOURCEFILE>led.c</SOURCEFILE><SOURCEFILE>rs485.c</SOURCEFILE><SOURCEFILE>seeprom.c</SOURCEFILE><SOURCEFILE>serial.c</SOURCEFILE><SOURCEFILE>stack.c</SOURCEFILE><SOURCEFILE>dlmstp.c</SOURCEFILE><SOURCEFILE>bo.c</SOURCEFILE><SOURCEFILE>bi.c</SOURCEFILE><SOURCEFILE>ai.c</SOURCEFILE><SOURCEFILE>device.c</SOURCEFILE><SOURCEFILE>watchdog.c</SOURCEFILE><SOURCEFILE>adc.c</SOURCEFILE><SOURCEFILE>bacnet.c</SOURCEFILE><SOURCEFILE>fuses.c</SOURCEFILE><SOURCEFILE>test.c</SOURCEFILE><SOURCEFILE>timer.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\demo\handler\noserv.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\demo\handler\s_iam.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\demo\handler\s_ihave.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\demo\handler\txbuf.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\demo\handler\h_dcc.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\demo\handler\h_npdu.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\demo\handler\h_rd.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\demo\handler\h_rp.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\demo\handler\h_rpm.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\demo\handler\h_whohas.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\demo\handler\h_whois.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\demo\handler\h_wp.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\reject.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\ringbuf.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\rp.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\rpm.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\whohas.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\whois.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\wp.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\abort.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\apdu.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\bacaddr.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\bacapp.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\bacdcode.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\bacerror.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\bacint.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\bacreal.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\bacstr.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\crc.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\dcc.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\fifo.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\iam.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\ihave.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\npdu.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\rd.c</SOURCEFILE><SOURCEFILE>C:\code\bacnet-stack\src\memcopy.c</SOURCEFILE><SOURCEFILE>av.c</SOURCEFILE><HEADERFILE>timer.h</HEADERFILE><HEADERFILE>eeprom.h</HEADERFILE><HEADERFILE>hardware.h</HEADERFILE><HEADERFILE>iar2gcc.h</HEADERFILE><HEADERFILE>init.h</HEADERFILE><HEADERFILE>input.h</HEADERFILE><HEADERFILE>led.h</HEADERFILE><HEADERFILE>nvdata.h</HEADERFILE><HEADERFILE>rs485.h</HEADERFILE><HEADERFILE>seeprom.h</HEADERFILE><HEADERFILE>serial.h</HEADERFILE><HEADERFILE>watchdog.h</HEADERFILE><HEADERFILE>adc.h</HEADERFILE><HEADERFILE>bacnet.h</HEADERFILE><HEADERFILE>stack.h</HEADERFILE><HEADERFILE>test.h</HEADERFILE><OTHERFILE>default\bacnet.lss</OTHERFILE><OTHERFILE>default\bacnet.map</OTHERFILE></FILES><CONFIGS><CONFIG><NAME>default</NAME><USESEXTERNALMAKEFILE>YES</USESEXTERNALMAKEFILE><EXTERNALMAKEFILE>Makefile</EXTERNALMAKEFILE><PART>atmega644p</PART><HEX>1</HEX><LIST>1</LIST><MAP>1</MAP><OUTPUTFILENAME>bacnet.elf</OUTPUTFILENAME><OUTPUTDIR>default\</OUTPUTDIR><ISDIRTY>0</ISDIRTY><OPTIONS><OPTION><FILE>C:\code\bacnet-stack\demo\handler\h_dcc.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\demo\handler\h_npdu.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\demo\handler\h_rd.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\demo\handler\h_rp.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\demo\handler\h_rpm.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\demo\handler\h_whohas.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\demo\handler\h_whois.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\demo\handler\h_wp.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\demo\handler\noserv.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\demo\handler\s_iam.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\demo\handler\s_ihave.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\demo\handler\txbuf.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\abort.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\apdu.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\bacaddr.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\bacapp.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\bacdcode.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\bacerror.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\bacint.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\bacreal.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\bacstr.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\crc.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\dcc.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\fifo.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\iam.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\ihave.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\memcopy.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\npdu.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\rd.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\reject.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\ringbuf.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\rp.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\rpm.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\whohas.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\whois.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>C:\code\bacnet-stack\src\wp.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>adc.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>ai.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>av.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>bacnet.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>bi.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>bo.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>device.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>dlmstp.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>eeprom.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>fuses.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>init.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>input.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>led.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>main.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>rs485.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>seeprom.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>serial.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>stack.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>test.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>timer.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>timer2.c</FILE><OPTIONLIST></OPTIONLIST></OPTION><OPTION><FILE>watchdog.c</FILE><OPTIONLIST></OPTIONLIST></OPTION></OPTIONS><INCDIRS><INCLUDE>.\</INCLUDE><INCLUDE>..\..\include\</INCLUDE></INCDIRS><LIBDIRS/><LIBS/><LINKOBJECTS/><OPTIONSFORALL>-Wall -gdwarf-2 -std=gnu99 -mcall-prologues -finline-functions-called-once -ffunction-sections -fdata-sections -Wstrict-prototypes -Wmissing-prototypes -DBACDL_MSTP -DMAX_APDU=128 -DBIG_ENDIAN=0 -DMAX_TSM_TRANSACTIONS=0 -DBACAPP_BOOLEAN -DBACAPP_REAL -DBACAPP_OBJECT_ID -DBACAPP_UNSIGNED -DBACAPP_ENUMERATED -DBACAPP_CHARACTER_STRING -DWRITE_PROPERTY -g                -DF_CPU=18432000UL  -funsigned-char -funsigned-bitfields -fpack-struct -fshort-enums</OPTIONSFORALL><LINKEROPTIONS>-Wl,--gc-sections,-static</LINKEROPTIONS><SEGMENTS/></CONFIG></CONFIGS><LASTCONFIG>default</LASTCONFIG><USES_WINAVR>1</USES_WINAVR><GCC_LOC>C:\WinAVR-20090313\bin\avr-gcc.exe</GCC_LOC><MAKE_LOC>C:\WinAVR-20090313\utils\bin\make.exe</MAKE_LOC></AVRGCCPLUGIN><IOView><usergroups/><sort sorted="0" column="0" ordername="1" orderaddress="1" ordergroup="1"/></IOView><Files><File00000><FileId>00000</FileId><FileName>main.c</FileName><Status>259</Status></File00000><File00001><FileId>00001</FileId><FileName>rs485.c</FileName><Status>258</Status></File00001><File00002><FileId>00002</FileId><FileName>bacnet.c</FileName><Status>258</Status></File00002><File00003><FileId>00003</FileId><FileName>device.c</FileName><Status>258</Status></File00003><File00004><FileId>00004</FileId><FileName>C:\code\bacnet-stack\src\fifo.c</FileName><Status>258</Status></File00004><File00005><FileId>00005</FileId><FileName>timer.c</FileName><Status>258</Status></File00005><File00006><FileId>00006</FileId><FileName>timer2.c</FileName><Status>258</Status></File00006><File00007><FileId>00007</FileId><FileName>hardware.h</FileName><Status>1</Status></File00007><File00008><FileId>00008</FileId><FileName>adc.c</FileName><Status>259</Status></File00008></Files><Events><Bookmarks></Bookmarks></Events><Trace><Filters></Filters></Trace></AVRStudio>
