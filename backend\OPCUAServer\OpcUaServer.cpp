#include "OpcUaServer.h"

#include <iostream>
#include <cstdint>
#include <thread>
#include <vector>
#include <functional>
#include <algorithm>

COpcUaServer::COpcUaServer()
    : m_server(nullptr)
    , m_running(false)
    , m_defaultPublishingInterval(100.0)
    ,m_endpoint("opc.tcp://0.0.0.0:4840")
{
    // 在构造函数中初始化服务器实例，但不配置网络
    initServerInstance();
}

COpcUaServer::~COpcUaServer()
{
    uninit();
}

bool COpcUaServer::init(const std::string& endpoint)
{
    // 确保服务器实例已经创建
    if (!initServerInstance()) {
        return false;
    }
    
    m_endpoint = endpoint;
    UA_ServerConfig* config = UA_Server_getConfig(m_server);

    size_t portPos = endpoint.rfind(':');
    if (portPos == std::string::npos) 
    {
        std::cerr << "Invalid endpoint format" << std::endl;
        return false;
    }
    uint16_t port = std::stoi(endpoint.substr(portPos + 1));

    // 重新配置网络层
    UA_StatusCode status = UA_ServerConfig_setMinimal(config, port, nullptr);
    if (status != UA_STATUSCODE_GOOD) {
        std::cerr << "Failed to configure OPC UA server network" << std::endl;
        return false;
    }

    if (config->networkLayersSize > 0) {
        UA_ServerNetworkLayer* nl = &config->networkLayers[0];
        nl->discoveryUrl = UA_String_fromChars(endpoint.c_str());
    }
    
    config->applicationDescription.applicationUri = UA_STRING_ALLOC(endpoint.c_str());

    UA_NodeId_init(&adminNodeId_);
    adminNodeId_.identifierType = UA_NODEIDTYPE_GUID;
    adminNodeId_.identifier.guid.data1 = 1;

    std::cout << "OPC UA Server network configured successfully on " << endpoint << std::endl;
    return true;
}

void COpcUaServer::uninit()
{
    // 确保服务器已停止
    if (m_running) {
        stop();
    }
    
    // 清理服务器实例
    if (m_server) {
        UA_Server_delete(m_server);
        m_server = nullptr;
    }

    m_nodePathToId.clear();
    m_nodeIdToPath.clear();
    
    // 重置其他状态
    m_running = false;
    m_defaultPublishingInterval = 100.0;
    
    std::cout << "OPC UA Server uninitialized" << std::endl;
}

bool COpcUaServer::initServerInstance()
{
    // 如果已经初始化，直接返回
    if (m_server) {
        return true;
    }
    
    m_server = UA_Server_new();
    if (!m_server) {
        std::cerr << "Failed to create OPC UA server instance" << std::endl;
        return false;
    }

    // 使用默认配置（不配置网络端口）
    UA_ServerConfig* config = UA_Server_getConfig(m_server);
    UA_ServerConfig_setDefault(config);
    
    config->publishingIntervalLimits.min = 100.0;
    config->publishingIntervalLimits.max = 3600000.0;
    
    config->applicationDescription.applicationName = UA_LOCALIZEDTEXT_ALLOC("en-US", "EMSBOX OPC UA Server");
    
    UA_UInt16 namespaceIndex = UA_Server_addNamespace(m_server, "http://opcuaserver/namespace2");
    if (namespaceIndex != 2) {
        std::cerr << "Failed to register namespace 2, got index: " << namespaceIndex << std::endl;
        UA_Server_delete(m_server);
        m_server = nullptr;
        return false;
    }
    
    // 创建顶层父节点 emsbox
    UA_NodeId parentNodeId = UA_NODEID_NUMERIC(0, UA_NS0ID_OBJECTSFOLDER);
    const std::string& part = "emsbox";
    UA_NodeId_init(&m_emsboxNodeId);
    m_emsboxNodeId.namespaceIndex = 2;
    UA_QualifiedName browseName = UA_QUALIFIEDNAME_ALLOC(2, part.c_str());
    UA_LocalizedText displayName = UA_LOCALIZEDTEXT_ALLOC("en-US", part.c_str());

    UA_ObjectAttributes objAttr = UA_ObjectAttributes_default;
    objAttr.displayName = displayName;

    UA_StatusCode code = UA_Server_addObjectNode(m_server,
        UA_NODEID_NULL,  // auto-generate nodeId
        parentNodeId,
        UA_NODEID_NUMERIC(0, UA_NS0ID_ORGANIZES),
        browseName,
        UA_NODEID_NUMERIC(0, UA_NS0ID_BASEOBJECTTYPE),
        objAttr,
        NULL,
        &m_emsboxNodeId);

    UA_QualifiedName_clear(&browseName);
    UA_LocalizedText_clear(&displayName);

    if (code != UA_STATUSCODE_GOOD) {
        std::cerr << "Failed to create emsbox root node" << std::endl;
        UA_Server_delete(m_server);
        m_server = nullptr;
        return false;
    }
    
    std::cout << "OPC UA Server instance initialized (without network config)" << std::endl;
    return true;
}

bool COpcUaServer::start()
{
    if (!m_server) {
        std::cerr << "Server not initialized" << std::endl;
        return false;
    }
    
    if (m_running) {
        std::cout << "OPC UA Server is already running" << std::endl;
        return true;
    }

    UA_StatusCode retval = UA_Server_run_startup(m_server);
    if (retval != UA_STATUSCODE_GOOD) {
        std::cerr << "Failed to start OPC UA server" << std::endl;
        return false;
    }

    m_running = true;
    m_serverThread = std::thread(&COpcUaServer::run, this);

    std::cout << "OPC UA Server started successfully" << std::endl;
    return true;
}

void COpcUaServer::stop()
{
    if (m_running) {
        m_running = false;
        if (m_serverThread.joinable()) {
            m_serverThread.join();
        }
        
        if (m_server) {
            UA_Server_run_shutdown(m_server);
        }
        
        std::cout << "OPC UA Server stopped" << std::endl;
    }
}

bool COpcUaServer::addVariable(const std::unordered_map<std::string, std::unordered_map<std::string, std::set<std::string>>>& allNodeName)
{
    // 确保服务器实例已经创建
    if (!initServerInstance()) {
        return false;
    }
    
    // 先清除所有现有节点
    if (!clearAllNodes()) {
        std::cerr << "Failed to clear existing nodes" << std::endl;
        return false;
    }
    for (auto& link : allNodeName)
    {
        std::string linkId = link.first;
        UA_NodeId linkNodeId = UA_NODEID_NULL;  // 使用栈变量而不是指针
        UA_QualifiedName browseName = UA_QUALIFIEDNAME_ALLOC(2, linkId.c_str());
        UA_LocalizedText displayName = UA_LOCALIZEDTEXT_ALLOC("en-US", linkId.c_str());
        
        UA_ObjectAttributes objAttr = UA_ObjectAttributes_default;
        objAttr.displayName = displayName;

        UA_StatusCode code = UA_Server_addObjectNode(m_server,
            UA_NODEID_NULL,  // auto-generate nodeId
            m_emsboxNodeId,
            UA_NODEID_NUMERIC(0, UA_NS0ID_ORGANIZES),
            browseName,
            UA_NODEID_NUMERIC(0, UA_NS0ID_BASEOBJECTTYPE),
            objAttr,
            NULL,
            &linkNodeId);
        UA_QualifiedName_clear(&browseName);
        UA_LocalizedText_clear(&displayName);

        if (code != UA_STATUSCODE_GOOD) {
            std::cerr << "add link object failed." << std::endl;
            return false;
        }
        
        // 保存link节点映射
        std::string linkPath = linkId;
        m_nodePathToId[linkPath] = linkNodeId;

        for (auto& device : link.second)
        {
            std::string deviceId = device.first;
            UA_NodeId deviceNodeId = UA_NODEID_NULL;  // 使用栈变量而不是指针
            UA_QualifiedName deviceBrowseName = UA_QUALIFIEDNAME_ALLOC(2, deviceId.c_str());
            UA_LocalizedText deviceDisplayName = UA_LOCALIZEDTEXT_ALLOC("en-US", deviceId.c_str());
            UA_ObjectAttributes deviceObjAttr = UA_ObjectAttributes_default;
            deviceObjAttr.displayName = deviceDisplayName;
            UA_StatusCode code = UA_Server_addObjectNode(m_server,
                UA_NODEID_NULL,  // auto-generate nodeId
                linkNodeId,
                UA_NODEID_NUMERIC(0, UA_NS0ID_ORGANIZES),
                deviceBrowseName,
                UA_NODEID_NUMERIC(0, UA_NS0ID_BASEOBJECTTYPE),
                deviceObjAttr,
                NULL,
                &deviceNodeId);
            UA_QualifiedName_clear(&deviceBrowseName);
            UA_LocalizedText_clear(&deviceDisplayName);
            if (code != UA_STATUSCODE_GOOD) {
                std::cerr << "add device object failed 1." << std::endl;
                return false;
            }
            if (UA_NodeId_isNull(&deviceNodeId)) {
                std::cerr << "add device object failed 2." << std::endl;
                return false;
            }
            
            // 保存device节点映射
            std::string devicePath = linkId + "." + deviceId;
            m_nodePathToId[devicePath] = deviceNodeId;

            for (auto& point : device.second)
            {
                std::string pointId = point;
                UA_NodeId pointNodeId = UA_NODEID_NULL;  // 使用栈变量而不是指针
                UA_VariableAttributes attr;
                UA_VariableAttributes_init(&attr);
                UA_VariableAttributes_copy(&UA_VariableAttributes_default, &attr);
                attr.displayName = UA_LOCALIZEDTEXT_ALLOC("en-US", pointId.c_str());
                attr.accessLevel = UA_ACCESSLEVELMASK_READ | UA_ACCESSLEVELMASK_WRITE;

                UA_QualifiedName codebrowseName = UA_QUALIFIEDNAME_ALLOC(2, pointId.c_str());
                UA_StatusCode codestatus = UA_Server_addVariableNode(m_server,
                    UA_NODEID_NULL,
                    deviceNodeId,
                    UA_NODEID_NUMERIC(0, UA_NS0ID_ORGANIZES),
                    codebrowseName,
                    UA_NODEID_NUMERIC(0, UA_NS0ID_BASEDATAVARIABLETYPE),
                    attr,
                    NULL,
                    &pointNodeId);
                UA_QualifiedName_clear(&codebrowseName);

                if (codestatus != UA_STATUSCODE_GOOD) {
                    std::cerr << "add point object failed 1." << std::endl;
                    return false;
                }
                if (UA_NodeId_isNull(&pointNodeId)) {
                    std::cerr << "add point object failed 2." << std::endl;
                    return false;
                }

                // 保存节点路径映射
                std::string nodePath = linkId + "." + deviceId + "." + pointId;
                m_nodePathToId[nodePath] = pointNodeId;
                //m_nodeIdToPath[pointNodeId] = nodePath;
                m_nodeIdToPath[pointNodeId] = std::make_tuple(linkId, deviceId, pointId);

                // 为变量节点设置write回调，将this指针作为nodeContext传递
                UA_ValueCallback callback;
                callback.onRead = nullptr;
                callback.onWrite = &COpcUaServer::writeCallback;
                UA_Server_setVariableNode_valueCallback(m_server, pointNodeId, callback);
                
                // 设置节点上下文为当前实例指针
                UA_Server_setNodeContext(m_server, pointNodeId, this);

                UA_String nodeId_uastr = UA_STRING_NULL;
                UA_NodeId_toString(&pointNodeId, &nodeId_uastr);
                std::string nodeId_str = std::string(reinterpret_cast<const char*>(nodeId_uastr.data), nodeId_uastr.length);
                UA_String_deleteMembers(&nodeId_uastr);
                
                std::cout << "Created node: " << nodePath << " -> " << nodeId_str << std::endl;
            }
        }

    }

    return true;
}

bool COpcUaServer::clearAllNodes()
{
    if (!m_server) {
        std::cerr << "Server not initialized." << std::endl;
        return false;
    }

    // 首先从映射表中获取所有需要删除的节点
    std::vector<std::pair<std::string, UA_NodeId>> pathNodePairs;
    
    // 方法1：从映射表中收集所有节点ID和路径
    for (const auto& pair : m_nodePathToId) {
        pathNodePairs.push_back({pair.first, pair.second});
        std::cout << "Found node to delete from mapping: " << pair.first << std::endl;
    }
    
    // 按路径深度排序，深度越深的越先删除（point -> device -> link）
    std::sort(pathNodePairs.begin(), pathNodePairs.end(), 
        [](const std::pair<std::string, UA_NodeId>& a, const std::pair<std::string, UA_NodeId>& b) {
            // 计算路径深度（点的个数）
            int depthA = std::count(a.first.begin(), a.first.end(), '.');
            int depthB = std::count(b.first.begin(), b.first.end(), '.');
            return depthA > depthB; // 深度大的排在前面（先删除）
        });
    
    std::vector<UA_NodeId> nodesToDelete;
    for (const auto& pair : pathNodePairs) {
        nodesToDelete.push_back(pair.second);
    }
    
    // 方法2：如果映射表为空，则通过浏览获取节点
    if (nodesToDelete.empty()) {
        // 递归收集所有子节点
        std::function<void(const UA_NodeId&)> collectChildNodes = [&](const UA_NodeId& parentId) {
            UA_BrowseDescription browseDescription;
            UA_BrowseDescription_init(&browseDescription);
            browseDescription.nodeId = parentId;
            browseDescription.resultMask = UA_BROWSERESULTMASK_ALL;
            browseDescription.browseDirection = UA_BROWSEDIRECTION_FORWARD;
            browseDescription.referenceTypeId = UA_NODEID_NUMERIC(0, UA_NS0ID_ORGANIZES);
            browseDescription.includeSubtypes = true;

            UA_BrowseResult browseResult = UA_Server_browse(m_server, 0, &browseDescription);
            
            if (browseResult.statusCode == UA_STATUSCODE_GOOD) {
                for (size_t i = 0; i < browseResult.referencesSize; i++) {
                    UA_ReferenceDescription* ref = &browseResult.references[i];
                    if (ref->nodeId.nodeId.namespaceIndex == 2) { // 只处理我们创建的节点
                        // 先递归收集子节点
                        collectChildNodes(ref->nodeId.nodeId);
                        // 然后添加当前节点到删除列表
                        nodesToDelete.push_back(ref->nodeId.nodeId);
                    }
                }
            }
            
            UA_BrowseResult_clear(&browseResult);
        };
        
        // 从emsbox根节点开始收集
        collectChildNodes(m_emsboxNodeId);
    }
    
    // 删除收集到的所有节点（已按正确顺序排序：point -> device -> link）
    for (const auto& nodeId : nodesToDelete) {
        UA_StatusCode deleteStatus = UA_Server_deleteNode(m_server, nodeId, true);
        if (deleteStatus != UA_STATUSCODE_GOOD) {
            std::cerr << "Failed to delete node with status: " << deleteStatus << std::endl;
        } else {
            std::cout << "Successfully deleted node" << std::endl;
        }
    }
    
    // 清空映射表
    m_nodePathToId.clear();
    m_nodeIdToPath.clear();
    
    std::cout << "All custom nodes cleared successfully, total deleted: " << nodesToDelete.size() << std::endl;
    return true;
}

bool COpcUaServer::updateVariable(const std::string& nodeName, const SubData& value)
{
    if (!m_server || !m_running) {
        return false;
    }
    auto pointIter = m_nodePathToId.find(nodeName);
    if (pointIter == m_nodePathToId.end()) {
        return false;
    }
    auto pointNodeId = pointIter->second;

    return ua_server_write(&pointNodeId, value);
}

void COpcUaServer::run()
{
    while (m_running) {
        UA_Server_run_iterate(m_server, true);
    }
}

bool COpcUaServer::ua_server_write(UA_NodeId* nodeId, const SubData& value)
{
    UA_Variant variant;
    UA_Variant_init(&variant);
    if (std::holds_alternative<std::string>(value.data)) 
    {
        std::string _value_str = *std::get_if<std::string>(&value.data);
        UA_String uaString = UA_STRING_ALLOC(_value_str.c_str());
        UA_Variant_setScalar(&variant, &uaString, getCurrentUADataType(value));
        UA_String_clear(&uaString);
    }
    else 
    {
        UA_Variant_setScalar(&variant, (void*)&value.data, getCurrentUADataType(value));
    }
    UA_DataValue dataValue;
    UA_DataValue_init(&dataValue);
    UA_Variant_copy(&variant, &(dataValue.value));
    dataValue.hasValue = true;

    dataValue.sourceTimestamp = (value.timestamp * 10000) + UA_DATETIME_UNIX_EPOCH;
    dataValue.hasSourceTimestamp = true;

    dataValue.serverTimestamp = UA_DateTime_now();
    dataValue.hasServerTimestamp = true;
    switch (value.quality) {
    case 1:
        dataValue.status = 0x00000000;
        break;
    case 2:
        dataValue.status = 0x80000000;
        break;
    case 3:
        dataValue.status = 0x40000000;
        break;
    default:
        dataValue.status = 0x00000000;
        break;
    }
    dataValue.hasStatus = true;

    UA_WriteValue writeValue;
    UA_WriteValue_init(&writeValue);
    writeValue.nodeId = *nodeId;
    writeValue.attributeId = UA_ATTRIBUTEID_VALUE;
    writeValue.indexRange = UA_STRING_NULL;
    UA_DataValue_copy(&dataValue, &writeValue.value);
    UA_StatusCode status = UA_Server_write(m_server, &writeValue);
    UA_WriteValue_clear(&writeValue);
    UA_DataValue_clear(&dataValue);
    
    return status == UA_STATUSCODE_GOOD;
}

SubData COpcUaServer::convertVariantToSubData(const UA_Variant* variant)
{
    SubData subData;
    subData.timestamp = UA_DateTime_toUnixTime(UA_DateTime_now()) / 1000; // 转换为秒
    subData.quality = 1; // 默认为好质量
    
    if (!variant || !variant->data || !variant->type) {
        subData.data = std::monostate{};
        return subData;
    }
    
    // 根据数据类型转换
    if (variant->type == &UA_TYPES[UA_TYPES_BOOLEAN]) {
        subData.data = *(UA_Boolean*)variant->data;
    }
    else if (variant->type == &UA_TYPES[UA_TYPES_SBYTE]) {
        subData.data = *(UA_SByte*)variant->data;
    }
    else if (variant->type == &UA_TYPES[UA_TYPES_BYTE]) {
        subData.data = *(UA_Byte*)variant->data;
    }
    else if (variant->type == &UA_TYPES[UA_TYPES_INT16]) {
        subData.data = *(UA_Int16*)variant->data;
    }
    else if (variant->type == &UA_TYPES[UA_TYPES_UINT16]) {
        subData.data = *(UA_UInt16*)variant->data;
    }
    else if (variant->type == &UA_TYPES[UA_TYPES_INT32]) {
        subData.data = *(UA_Int32*)variant->data;
    }
    else if (variant->type == &UA_TYPES[UA_TYPES_UINT32]) {
        subData.data = *(UA_UInt32*)variant->data;
    }
    else if (variant->type == &UA_TYPES[UA_TYPES_INT64]) {
        subData.data = *(UA_Int64*)variant->data;
    }
    else if (variant->type == &UA_TYPES[UA_TYPES_UINT64]) {
        subData.data = *(UA_UInt64*)variant->data;
    }
    else if (variant->type == &UA_TYPES[UA_TYPES_FLOAT]) {
        subData.data = *(UA_Float*)variant->data;
    }
    else if (variant->type == &UA_TYPES[UA_TYPES_DOUBLE]) {
        subData.data = *(UA_Double*)variant->data;
    }
    else if (variant->type == &UA_TYPES[UA_TYPES_STRING]) {
        UA_String* uaStr = (UA_String*)variant->data;
        if (uaStr->data && uaStr->length > 0) {
            subData.data = std::string((char*)uaStr->data, uaStr->length);
        } else {
            subData.data = std::string("");
        }
    }
    else {
        // 不支持的类型，设为空状态
        subData.data = std::monostate{};
        std::cerr << "Unsupported variant type in convertVariantToSubData" << std::endl;
    }
    
    return subData;
}

const UA_DataType* COpcUaServer::getCurrentUADataType(const SubData& data)
{
    if (std::holds_alternative<std::string>(data.data)) {
        return &UA_TYPES[UA_TYPES_STRING];
    }
    else if (std::holds_alternative<bool>(data.data)) {
        return  &UA_TYPES[UA_TYPES_BOOLEAN];
    }
    else if (std::holds_alternative<double>(data.data)) {
        return  &UA_TYPES[UA_TYPES_DOUBLE];
    }
    else if (std::holds_alternative<float>(data.data)) {
        return  &UA_TYPES[UA_TYPES_FLOAT];
    }
    else if (std::holds_alternative<int64_t>(data.data)) {
        return  &UA_TYPES[UA_TYPES_INT64];
    }
    else if (std::holds_alternative<uint64_t>(data.data)) {
        return  &UA_TYPES[UA_TYPES_UINT64];
    }
    else if (std::holds_alternative<uint32_t>(data.data)) {
        return  &UA_TYPES[UA_TYPES_UINT32];
    }
    else if (std::holds_alternative<int32_t>(data.data)) {
        return  &UA_TYPES[UA_TYPES_INT32];
    }
    else if (std::holds_alternative<uint16_t>(data.data)) {
        return  &UA_TYPES[UA_TYPES_UINT16];
    }
    else if (std::holds_alternative<int16_t>(data.data)) {
        return &UA_TYPES[UA_TYPES_INT16];
    }
    else if (std::holds_alternative<uint8_t>(data.data)) {
        return  &UA_TYPES[UA_TYPES_BYTE];
    }
    else if (std::holds_alternative<int8_t>(data.data)) {
        return  &UA_TYPES[UA_TYPES_SBYTE];
    }
    return nullptr;
}

bool COpcUaServer::setPublishingInterval(double interval)
{
    if (!m_server) return false;
    
    m_defaultPublishingInterval = interval;
    UA_ServerConfig* config = UA_Server_getConfig(m_server);
    config->publishingIntervalLimits.min = interval;
    return true;
}

bool COpcUaServer::setVariableSamplingInterval(const std::string& nodeName, double interval)
{
    if (!m_server) return false;

    char* nodeIdCopy = strdup(nodeName.c_str());
    UA_NodeId nodeId = UA_NODEID_STRING(2, nodeIdCopy);
    free(nodeIdCopy);

    UA_Double samplingInterval = interval;
    UA_Variant variant;
    UA_Variant_init(&variant);
    UA_Variant_setScalar(&variant, &samplingInterval, &UA_TYPES[UA_TYPES_DOUBLE]);

    UA_QualifiedName qn;
    qn.namespaceIndex = 0;
    qn.name = UA_STRING_ALLOC(nodeName.c_str());
    
    UA_StatusCode status = UA_Server_writeObjectProperty(m_server, nodeId, qn, variant);

    if (status != UA_STATUSCODE_GOOD) {
        std::cerr << "Failed to set sampling interval for node: " << nodeName << std::endl;
        return false;
    }

    return true;
}

bool COpcUaServer::setEndpoint(const std::string& endpoint)
{
    // 如果服务器正在运行，不允许修改端点
    if (m_running) {
        std::cerr << "Cannot change endpoint while server is running" << std::endl;
        return false;
    }

    // 验证端点格式
    size_t portPos = endpoint.rfind(':');
    if (portPos == std::string::npos) {
        std::cerr << "Invalid endpoint format: " << endpoint << std::endl;
        return false;
    }

    try {
        uint16_t port = std::stoi(endpoint.substr(portPos + 1));
        if (port == 0) {
            std::cerr << "Invalid port number in endpoint: " << endpoint << std::endl;
            return false;
        }
    } catch (const std::exception& e) {
        std::cerr << "Invalid port number in endpoint: " << endpoint << std::endl;
        return false;
    }

    // 更新端点地址
    m_endpoint = endpoint;
    std::cout << "Endpoint updated to: " << endpoint << std::endl;
    return true;
}

void COpcUaServer::setWriteCallback(OpcUaServerWriteCallback callback)
{
    m_writeCallback = callback;
}

void COpcUaServer::writeCallback(UA_Server *server,
                               const UA_NodeId *sessionId,
                               void *sessionContext,
                               const UA_NodeId *nodeId,
                               void *nodeContext,
                               const UA_NumericRange *range,
                               const UA_DataValue *data)
{
    // 只处理来自客户端的写入请求
    // 服务器内部写入(updateVariable)的sessionId为空，跳过回调
    if (!sessionId || UA_NodeId_isNull(sessionId)) 
    {
        //std::cout << "Internal server write detected, skipping callback" << std::endl;
        return;
    }

    // 从nodeContext获取实例指针
    COpcUaServer* instance = static_cast<COpcUaServer*>(nodeContext);
    if (!instance) 
    {
        //std::cerr << "COpcUaServer instance not available in write callback" << std::endl;
        return;
    }

    if (UA_NodeId_equal(sessionId, &(instance->adminNodeId_)))
    {
        //std::cout << "Internal server write detected, skipping callback" << std::endl;
        return;
    }

    // 检查是否设置了回调函数
    if (!instance->m_writeCallback) 
    {
        //std::cout << "No write callback set, write operation ignored" << std::endl;
        return;
    }

    // 查找节点路径
    auto it = instance->m_nodeIdToPath.find(*nodeId);
    std::tuple<std::string, std::string, std::string> pointInfo;
    if (it != instance->m_nodeIdToPath.end()) 
    {
        pointInfo = it->second;
    } 
    else 
    {
        // 如果找不到路径，使用节点ID字符串表示
        //UA_String nodeIdStr = UA_STRING_NULL;
        //UA_NodeId_toString(nodeId, &nodeIdStr);
        //nodePath = std::string(reinterpret_cast<const char*>(nodeIdStr.data), nodeIdStr.length);
        //UA_String_deleteMembers(&nodeIdStr);
    }

    //std::cout << "Client write callback triggered for node: " << nodePath << std::endl;

    // 将UA_Variant转换为SubData
    SubData subData = instance->convertVariantToSubData(&data->value);
    
    // 如果有时间戳信息，使用原始时间戳
    if (data->hasSourceTimestamp)
    {
        subData.timestamp = UA_DateTime_toUnixTime(data->sourceTimestamp) / 1000; // 转换为秒
    }
    
    // 如果有状态信息，转换质量
    if (data->hasStatus) 
    {
        if (data->status == UA_STATUSCODE_GOOD) 
        {
            subData.quality = 1; // 好质量
        } 
        else if ((data->status & 0x80000000) != 0) 
        {
            subData.quality = 2; // 坏质量
        }
        else
        {
            subData.quality = 3; // 不确定质量
        }
    }

    // 调用用户设置的回调函数
    try 
    {
        instance->m_writeCallback(pointInfo, subData, nodeId);
    }
    catch (const std::exception& e) 
    {
        std::cerr << "Exception in write callback: " << e.what() << std::endl;
        return;
    }
}
