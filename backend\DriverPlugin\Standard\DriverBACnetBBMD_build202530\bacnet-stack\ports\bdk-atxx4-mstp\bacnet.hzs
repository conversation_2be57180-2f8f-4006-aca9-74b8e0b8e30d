<!DOCTYPE CrossStudio_Session_File>
<session>
 <Bookmarks/>
 <Breakpoints groups="Breakpoints" active_group="Breakpoints"/>
 <ExecutionProfileWindow/>
 <FrameBufferWindow>
  <FrameBufferWindow bufferHeight="-1" addressSpace="" addressText="" bufferWidth="-1"/>
 </FrameBufferWindow>
 <Memory1/>
 <Memory2/>
 <Memory3/>
 <Memory4/>
 <Project>
  <ProjectSessionItem path="bacnet"/>
  <ProjectSessionItem path="bacnet;bacnet"/>
 </Project>
 <Register1/>
 <Register2/>
 <Register3/>
 <Register4/>
 <TargetWindow programLoadAddress="" programSize="" uploadStartAddress="" programMemoryInterface="" programFileName="" uploadMemoryInterface="" programFileType="" uploadFileName="" uploadFileType="" programAction="" uploadSize=""/>
 <Threads>
  <ThreadsWindow showLists=""/>
 </Threads>
 <TraceWindow>
  <Trace enabled="Yes"/>
 </TraceWindow>
 <Watch1>
  <Watches active="1" update="Never"/>
 </Watch1>
 <Watch2>
  <Watches active="0" update="Never"/>
 </Watch2>
 <Watch3>
  <Watches active="0" update="Never"/>
 </Watch3>
 <Watch4>
  <Watches active="0" update="Never"/>
 </Watch4>
 <Files/>
 <ARMCrossStudioWindow activeProject="bacnet" fileDialogDefaultFilter="*.c" autoConnectTarget="" buildConfiguration="Common" sessionSettings="" debugSearchFileMap="" fileDialogInitialDirectory="" debugSearchPath="" autoConnectCapabilities="0"/>
</session>
