#!/bin/sh

# 配置文件路径
conf_file="${1:-/user_part/etc/systemd/timesyncd.conf}"

# 提取NTP地址（仅[Time]段落内未注释的配置）
ntp_value=$(awk -F= '
    /^\[Time\]/ { in_section=1; next }
    /^\[/ { in_section=0 }
    in_section && !/^[[:space:]]*#/ && tolower($1) ~ /^[[:space:]]*ntp[[:space:]]*$/ {
        gsub(/^[ \t]+|[ \t]+$/, "", $2); 
        print $2; exit }
' "$conf_file")

# 提取auto值（全局搜索注释行，返回 true/false 纯文本）
auto_value=$(grep -iE '^[[:space:]]*#[[:space:]]*auto[[:space:]]*=[[:space:]]*(true|false)' "$conf_file" | \
             awk -F= '{gsub(/^[ \t]+|[ \t]+$/, "", $2); print tolower($2)}' | head -n1)

# 变量拼接JSON（关键点：switch值不加引号）
output=$(printf '{"address":"%s", "switch":%s}' \
         "${ntp_value:-127.0.0.1}" \
         "${auto_value:-false}")

echo "$output"