<template>
  <div class="container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>组态查看</span>
          <div class="header-buttons">
            <!-- 隐藏导出组态和导入组态按钮 -->
            <!-- <el-button type="primary" @click="exportConfig">导出组态</el-button> -->
            <!-- <el-button type="primary" @click="importConfig">导入组态</el-button> -->
            <!-- <el-button type="info" @click="viewMessages">报文查看</el-button> -->
            <el-button type="success" @click="checkLog">查看日志</el-button>
            <el-dialog
              v-model="logDialog.visible"
              title="查看日志"
              custom-class="bottom-dialog"
              width="80%"
              style="height:80%;"
              @close="closeLogDialog"
              class="auto-height-dialog"
            >
              <!-- <div class="dialog-content"> -->
                <el-input
                  v-model="logInfos"
                  type="textarea"
                  placeholder="日志内容"
                  resize="none"
                  class="auto-height-input"
                />
              <!-- </div> -->
            </el-dialog>
            <el-button 
              type="success" 
              @click="startCollection" 
              :disabled="isRunning"
            >开始采集</el-button>
            <el-button 
              type="danger" 
              @click="stopCollection" 
              :disabled="!isRunning"
            >停止采集</el-button>
          </div>
        </div>
      </template>
      <div class="card-content">
        <div class="content-layout">
          <div class="left-panel">
            <!-- 左侧协议原型树 -->
            <el-tree
              ref="protocolTreeRef"
              :data="protocolList"
              node-key="id"
              :props="defaultProps"
              @node-click="handleNodeClick"
              highlight-current
              default-expand-all
              :expand-on-click-node="false"
              class="protocol-tree"
            >
              <template #default="{ node, data }">
                <span class="custom-tree-node" :class="{ 'node-selected': currentProtocol && currentProtocol.id === data.id }">
                  <span>{{ node.label }}</span>
                </span>
              </template>
            </el-tree>
          </div>
          <div class="right-panel">
            <!-- 右侧点表 -->
            <el-table
              :data="pointsList"
              style="width: 100%"
              border
              stripe
              height="calc(100vh - 200px)"
            >
            <!-- :max-height="600" -->
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column prop="code" label="位号名" min-width="120" align="center" />
              <el-table-column prop="name" label="位号描述" width="120" align="center" />
              <el-table-column label="寄存器地址" width="120" align="center">
                <template #default="scope">
                  {{ getPointProtocolValue(scope.row, 'Address') }}
                </template>
              </el-table-column>
              <el-table-column label="数据类型" width="100" align="center">
                <template #default="scope">
                  {{ getPointProtocolValue(scope.row, 'ValueType') }}
                </template>
              </el-table-column>
              <el-table-column prop="value" label="当前值" width="100" align="center" />
              <el-table-column prop="quality" label="质量码" width="80" align="center">
                <template #default="scope">
                  <el-tag :type="scope.row.quality?.length === 0 ? 'success' : 'danger'" size="small">
                    {{ scope.row.quality?.length === 0 ? 'GOOD' : scope.row.quality?.join('/') }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="timestamp" label="时间戳" width="180" align="center">
                <template #default="scope">
                  {{ formatTime(scope.row.timestamp) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" align="center" fixed="right">
                <template #default="scope">
                  <el-button 
                    type="primary" 
                    size="small"
                    :disabled="!isRunning"
                    @click="handleControl(scope.row)"
                  >
                    控制
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </el-card>
    
    <!-- 控制设置抽屉 -->
    <ControlDrawer ref="controlDrawerRef" />
  </div>
</template>

<script setup lang="ts" name="ConfigView">
import { ref, reactive, onMounted, onBeforeUnmount, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getConfiguration, exportConfiguration, importConfiguration, getConfigurationMessages, getConfigPoints, startLocalConfigurationWork, stopLocalConfigurationWork, getConfigRunningState,getLogInfos } from "@/api/modules/configuration";
import ControlDrawer from './controlDrawer.vue';

// 定义数据结构
const protocolList = ref<any[]>([]);
const currentProtocol = ref<any>(null);
const pointsList = ref<any[]>([]);
const defaultProps = {
  children: 'children',
  label: 'name'
};
const protocolTreeRef = ref();
const controlDrawerRef = ref();
const pollingTimer = ref<NodeJS.Timer | null>(null);
const runningStateTimer = ref<NodeJS.Timer | null>(null);
const getLogInfoTimer = ref<NodeJS.Timer | null>(null);
const isRunning = ref(false);
// 日志对话框
const logDialog = reactive({
  visible: false
});
//日志内容
const logInfos:any = ref('')

// 查看日志
const checkLog = async()=>{
  if (!currentProtocol.value || currentProtocol.value.type !== 'device') {
    ElMessage.warning('请先选择一个设备');
    return;
  }
  console.log(currentProtocol.value,'currentProtocol.value')
  startGetLogInfo()
  logDialog.visible = true
}

const getLogInfo = async()=>{
  const res:any = await getLogInfos({
    linkId:currentProtocol.value.parentInstance,
    deviceId:currentProtocol.value.id
  })
  let newLoginfo = ''
  if(res.result && res.result.resultCode === "0" && res.data && res.data.log){
    for(let i =res.data.log.length-1;i>-1;i--){
      newLoginfo += `${res.data.log[i]}\n`
    }
  }
  logInfos.value = newLoginfo
}
const startGetLogInfo = ()=>{
  getLogInfo()
  getLogInfoTimer.value = setInterval(getLogInfo, 1000);
}

const closeLogDialog = ()=>{
  if (getLogInfoTimer.value) {
    clearInterval(getLogInfoTimer.value);
    getLogInfoTimer.value = null;
  }
}

// 获取组态配置
const fetchConfiguration = async () => {
  try {
    const res = await getConfiguration();
    console.log('组态配置数据:', res);
    
    if (res.result && res.result.resultCode === "0") {
      // 解析返回的数据结构，构建树形结构
      const treeData: any[] = [];
      
      // 检查是否有data和link属性
      if (res.data && res.data.link) {
        // 遍历link下的每个协议原型
        Object.entries(res.data.link).forEach(([protoKey, protoValue]: [string, any]) => {
          // 创建协议原型节点
          const protoNode:any = {
            id: `proto_${protoKey}`,
            name: protoKey,
            type: 'prototype',
            children: []
          };
          
          // 遍历该原型下的所有协议实例
          if (protoValue.instances && Array.isArray(protoValue.instances)) {
            protoValue.instances.forEach((instance: any) => {
              // 创建协议实例节点
              const instanceNode:any = {
                id: instance.id,
                name: instance.name,
                type: 'instance',
                parentProto: protoKey,
                children: []
              };
              
              // 如果有设备，添加设备节点
              if (instance.devices && Array.isArray(instance.devices)) {
                instance.devices.forEach((device: any) => {
                  const deviceNode:any = {
                    id: device.id,
                    name: device.name,
                    type: 'device',
                    parentInstance: instance.id,
                    points: device.points || [],
                    children: []
                  };
                  
                  instanceNode.children.push(deviceNode);
                });
              }
              
              protoNode.children.push(instanceNode);
            });
          }
          
          treeData.push(protoNode);
        });
      }
      
      console.log('构建的树形数据:', treeData);
      protocolList.value = treeData;
    } else {
      ElMessage.error('获取组态配置失败: ' + (res.result?.resultError || '未知错误'));
    }
  } catch (error) {
    console.error('获取组态配置失败:', error);
    ElMessage.error('获取组态配置失败');
  }
};

// 停止轮询
const stopPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
    pollingTimer.value = null;
  }
};

// 开始轮询设备点位数据
const startPolling = (deviceId: string) => {
  // 先停止之前的轮询
  stopPolling();
  
  // 开始新的轮询
  const fetchPoints = async () => {
    try {
      // 获取设备所属的链路ID
      let linkId = '';
      
      if (currentProtocol.value && currentProtocol.value.type === 'device') {
        // 从当前选中的设备节点获取父级实例ID作为linkId
        linkId = currentProtocol.value.parentInstance || '';
      }
      
      // 修改请求参数格式，传入 link 和 device 参数
      const params = { 
        link: linkId, 
        device: deviceId 
      };
      console.log('发送请求参数:', JSON.stringify(params));
      
      const res = await getConfigPoints(params);
      if (res.result && res.result.resultCode === "0") {
        // 使用接口返回的点位数据更新现有点位列表
        if (res.data && Array.isArray(res.data.points)) {
          // 创建一个Map用于快速查找返回的点位数据
          const pointsMap = new Map(res.data.points.map((point: any) => [point.code, point]));
          
          // 更新现有点位列表中的值
          pointsList.value = pointsList.value.map(point => {
            const updatedPoint:any = pointsMap.get(point.code);
            if (updatedPoint) {
              // 如果接口返回了这个点位的数据，就更新value、quality和timestamp
              // 但保留原始的protocol信息
              return {
                ...point,
                value: updatedPoint.value,
                quality: updatedPoint.quality || [],
                timestamp: updatedPoint.timestamp,
                // 保持原始协议信息不变
                protocol: point.protocol
              };
            }
            // 如果接口没有返回这个点位的数据，保持原样
            return point;
          });
          
          console.log('更新点位数据:', pointsList.value);
        }
      } else {
        console.error('获取点位数据失败:', res.result?.resultError || '未知错误');
      }
    } catch (error) {
      console.error('获取点位数据失败:', error);
    }
  };
  
  // 立即执行一次
  fetchPoints();
  
  // 设置定时器，每秒执行一次
  pollingTimer.value = setInterval(fetchPoints, 1000);
};

// 获取点位协议参数值
const getPointProtocolValue = (point: any, paramName: 'Address' | 'ValueType') => {
  if (!point.protocol || !Array.isArray(point.protocol)) return '-';
  
  // 根据参数名确定要查找的level值
  const levelMap = {
    'Address': 3,  // 寄存器地址对应level 3
    'ValueType': 4 // 数据类型对应level 4
  } as const;
  
  const level = levelMap[paramName];
  if (!level) return '-';
  
  // 查找对应level的协议参数
  const param = point.protocol.find((p: { level: number; value: string }) => p.level === level);
  if (!param) return '-';
  
  // 如果是select类型且有items，则查找对应的text显示
  if (param.inputType === 'select' && param.items && Array.isArray(param.items)) {
    const selectedItem = param.items.find((item: any) => String(item.value) === String(param.value));
    return selectedItem ? selectedItem.text : param.value;
  }
  
  // 否则直接返回value
  return param.value || '-';
};

// 处理节点点击
const handleNodeClick = (data: any) => {
  console.log('点击节点:', data);
  currentProtocol.value = data;
  
  // 如果点击的是设备节点
  if (data.type === 'device') {
    // 使用设备节点中的原始points数据，并初始化质量码为 Bad
    pointsList.value = (data.points || []).map((point: any) => ({
      ...point,
      quality: ['BAD'],
      value: null,
      timestamp: null,
      // 保持原始协议信息
      protocol: point.protocol || {}
    }));
    
    // 只有在运行状态下才开始轮询设备点位数据
    if (isRunning.value) {
      startPolling(data.id);
    }
  } else {
    // 非设备节点，清空点位列表并停止轮询
    pointsList.value = [];
    stopPolling();
  }
};

// 格式化时间戳
const formatTime = (timestamp: number | string) => {
  if (!timestamp) return '';
  const date = new Date(Number(timestamp));
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  const milliseconds = String(date.getMilliseconds()).padStart(3, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
};

// 导出组态
const exportConfig = async () => {
  if (!currentProtocol.value || !currentProtocol.value.id) {
    ElMessage.warning('请先选择要导出的组态');
    return;
  }
  
  try {
    const res = await exportConfiguration({ configId: currentProtocol.value.id });
    // 处理文件下载
    const blob = new Blob([res], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `组态配置_${currentProtocol.value.name}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    ElMessage.success('组态导出成功');
  } catch (error) {
    console.error('导出组态失败:', error);
    ElMessage.error('导出组态失败');
  }
};

// 导入组态
const importConfig = () => {
  // 创建文件选择器
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';
  input.onchange = async (event: any) => {
    const file = event.target.files[0];
    if (!file) return;
    
    const formData = new FormData();
    formData.append('file', file);
    
    try {
      const res = await importConfiguration(formData);
      if (res.result && res.result.resultCode === "0") {
        ElMessage.success('组态导入成功');
        fetchConfiguration(); // 重新加载组态数据
      } else {
        ElMessage.error('导入组态失败: ' + (res.result?.resultError || '未知错误'));
      }
    } catch (error) {
      console.error('导入组态失败:', error);
      ElMessage.error('导入组态失败');
    }
  };
  input.click();
};

// 报文查看
const viewMessages = async () => {
  if (!currentProtocol.value || !currentProtocol.value.id) {
    ElMessage.warning('请先选择要查看的组态');
    return;
  }
  
  try {
    const res = await getConfigurationMessages({ configId: currentProtocol.value.id });
    if (res.result && res.result.resultCode === "0") {
      ElMessageBox.alert(JSON.stringify(res.data, null, 2), '报文信息', {
        confirmButtonText: '确定',
        closeOnClickModal: true
      });
    } else {
      ElMessage.error('获取报文信息失败: ' + (res.result?.resultError || '未知错误'));
    }
  } catch (error) {
    console.error('获取报文信息失败:', error);
    ElMessage.error('获取报文信息失败');
  }
};

// 开始采集
const startCollection = async () => {
  await startLocalConfigurationWork({ configId: '' });
  // 如果当前选中的是设备节点，立即开始轮询数据
  if (currentProtocol.value && currentProtocol.value.type === 'device') {
    startPolling(currentProtocol.value.id);
  }
};

// 停止采集
const stopCollection = async () => {
  await stopLocalConfigurationWork({ configId: '' });
  // 停止所有轮询
  stopPolling();
};

// 获取组态运行状态
const fetchRunningState = async () => {
  try {
    const res = await getConfigRunningState();
    console.log('组态运行状态:', res);
    if (res.data && typeof res.data.state === 'boolean') {
      isRunning.value = res.data.state;
    }
  } catch (error) {
    console.error('获取组态运行状态失败:', error);
  }
};

// 开始运行状态轮询
const startRunningStatePolling = () => {
  // 先停止之前的轮询（如果有）
  stopRunningStatePolling();
  
  // 立即执行一次
  fetchRunningState();
  
  // 设置定时器，每1秒执行一次
  runningStateTimer.value = setInterval(fetchRunningState, 1000);
};

// 停止运行状态轮询
const stopRunningStatePolling = () => {
  if (runningStateTimer.value) {
    clearInterval(runningStateTimer.value);
    runningStateTimer.value = null;
  }
};

// 处理控制按钮点击
const handleControl = (point: any) => {
  console.log('点击控制按钮，点位信息:', point);
  
  // 获取链路信息（设备的父级实例）
  let linkId = '';
  let linkName = '';
  
  if (currentProtocol.value && currentProtocol.value.type === 'device') {
    // 从树形结构中找到设备所属的协议实例
    linkId = currentProtocol.value.parentInstance || '';
    
    // 查找协议实例名称
    for (const proto of protocolList.value) {
      if (proto.children) {
        for (const instance of proto.children) {
          if (instance.id === linkId) {
            linkName = instance.name || '';
            break;
          }
        }
      }
    }
  }
  
  // 构造传递给抽屉组件的参数
  const params = {
    linkId,
    linkName,
    deviceId: currentProtocol.value?.id || '',
    deviceName: currentProtocol.value?.name || '',
    pointId: point.id || '',   // 位号ID
    code: point.code || '',    // 位号Code
    name: point.name || '',    // 位号描述
    dataType: getPointProtocolValue(point, 'ValueType'),
    address: getPointProtocolValue(point, 'Address'),
    currentValue: point.value || '',
    controlValue: ''
  };
  
  // 打开控制设置抽屉
  controlDrawerRef.value.acceptParams(params);
};

// 组件挂载时
onMounted(() => {
  // 加载组态配置数据
  fetchConfiguration();
  // 开始运行状态轮询
  startRunningStatePolling();
});

// 组件卸载前
onBeforeUnmount(() => {
  // 停止点位轮询
  stopPolling();
  // 停止运行状态轮询
  stopRunningStatePolling();
});
</script>

<style scoped>
.container {
  height: 100%;
}

.main-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 32px;
}

.header-buttons {
  display: flex;
  gap: 8px;
}

.card-content {
  flex: 1;
  overflow: hidden;
}

.content-layout {
  display: flex;
  height: 100%;
}

.left-panel {
  width: 240px;
  border-right: 1px solid #ebeef5;
  overflow: auto;
  padding-right: 10px;
  flex-shrink: 0;
}

.right-panel {
  flex: 1;
  padding-left: 10px;
  overflow: hidden;
  min-width: 0; /* 防止flex子项溢出 */
}

.protocol-tree {
  width: 100%;
}

/* 调整表格在容器中的布局 */
.el-table {
  overflow: visible !important;
}

/* 确保表格内容区域可以滚动 */
:deep(.el-table__body-wrapper) {
  overflow: auto !important;
  /* max-height: calc(100vh - 300px); */
}

/* 可选：美化滚动条 */
:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: #ddd;
  border-radius: 3px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f6f6f6;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.node-selected {
  font-weight: bold;
  color: #409eff;
}

</style>
<style>
.auto-height-dialog .el-dialog__body {
  height: 86%; /* 根据需要调整 */
  padding: 20px;
}
.el-dialog__body .auto-height-input{
  height: 100% !important;
}

.auto-height-input .el-textarea__inner {
  height: 100%;
  min-height: 200px !important ;/* 最小高度 */
}
</style>
