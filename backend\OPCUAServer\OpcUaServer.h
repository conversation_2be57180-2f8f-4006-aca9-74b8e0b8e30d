#pragma once

#include "open62541.h"
#include <string>
#include <thread>
#include <atomic>
#include <map>
#include <type_traits>
#include <unordered_map>
#include <set>
#include <variant>
#include <functional>

struct SubData {
    std::variant<
        std::monostate,  // 表示空状态
        uint8_t, int8_t,
        uint16_t, int16_t,
        uint32_t, int32_t,
        uint64_t, int64_t,
        float, double,
        bool,
        std::string
    > data;

    uint64_t timestamp;
    int64_t quality;
    bool isEmpty() const {
        return std::holds_alternative<std::monostate>(data);
    }
};

// UA_NodeId的自定义比较器
struct NodeIdComparator {
    bool operator()(const UA_NodeId& lhs, const UA_NodeId& rhs) const {
        if (lhs.namespaceIndex != rhs.namespaceIndex) {
            return lhs.namespaceIndex < rhs.namespaceIndex;
        }
        if (lhs.identifierType != rhs.identifierType) {
            return lhs.identifierType < rhs.identifierType;
        }
        
        switch (lhs.identifierType) {
            case UA_NODEIDTYPE_NUMERIC:
                return lhs.identifier.numeric < rhs.identifier.numeric;
            case UA_NODEIDTYPE_STRING: {
                if (lhs.identifier.string.length != rhs.identifier.string.length) {
                    return lhs.identifier.string.length < rhs.identifier.string.length;
                }
                if (lhs.identifier.string.length == 0) {
                    return false;
                }
                return memcmp(lhs.identifier.string.data, rhs.identifier.string.data, lhs.identifier.string.length) < 0;
            }
            case UA_NODEIDTYPE_GUID:
                return memcmp(&lhs.identifier.guid, &rhs.identifier.guid, sizeof(UA_Guid)) < 0;
            case UA_NODEIDTYPE_BYTESTRING: {
                if (lhs.identifier.byteString.length != rhs.identifier.byteString.length) {
                    return lhs.identifier.byteString.length < rhs.identifier.byteString.length;
                }
                if (lhs.identifier.byteString.length == 0) {
                    return false;
                }
                return memcmp(lhs.identifier.byteString.data, rhs.identifier.byteString.data, lhs.identifier.byteString.length) < 0;
            }
            default:
                return false;
        }
    }
};

// Write回调函数类型定义
using OpcUaServerWriteCallback = std::function<void(const std::tuple<std::string, std::string, std::string>& pointInfo, const SubData& value, const UA_NodeId* nodeId)>;

class COpcUaServer
{
public:
    COpcUaServer();
    virtual ~COpcUaServer();

    bool init(const std::string& endpoint = "opc.tcp://0.0.0.0:4840");
    void uninit();
    bool start();
    void stop();

    bool addVariable(const std::unordered_map<std::string,std::unordered_map<std::string,std::set<std::string>>> &allNodeName);
    
    // 清除所有自定义节点（保留emsbox根节点）
    bool clearAllNodes();
    
    bool updateVariable(const std::string& nodeName, const SubData& value);

    bool isRunning() const { return m_running; }
    std::string endpoint() const { return m_endpoint; }

    // 设置端点地址，只有在服务器未运行时才能修改
    bool setEndpoint(const std::string& endpoint);

    bool setPublishingInterval(double interval);
    bool setVariableSamplingInterval(const std::string& nodeName, double interval);
    
    // 设置write回调函数
    void setWriteCallback(OpcUaServerWriteCallback callback);

private:
    void run();
    bool ua_server_write(UA_NodeId* nodeId, const SubData& value);
    const UA_DataType* getCurrentUADataType(const SubData& data);
    
    // 内部初始化服务器实例（不包含网络配置）
    bool initServerInstance();
    
    // 将UA_Variant转换为SubData
    SubData convertVariantToSubData(const UA_Variant* variant);
    
    // 静态回调函数，用于OPC UA服务器
    static void writeCallback(UA_Server *server,
                            const UA_NodeId *sessionId,
                            void *sessionContext,
                            const UA_NodeId *nodeId,
                            void *nodeContext,
                            const UA_NumericRange *range,
                            const UA_DataValue *data);

public :
    UA_NodeId adminNodeId_;

private:
    UA_Server* m_server;
    std::thread m_serverThread;
    std::atomic<bool> m_running;
    double m_defaultPublishingInterval;  // 默认发布间隔（毫秒）
    std::string m_endpoint;
  
    UA_NodeId m_emsboxNodeId;
    // 存储节点路径到节点ID的映射
    std::map<std::string, UA_NodeId> m_nodePathToId;
    // 存储节点ID到路径的反向映射，使用自定义比较器
    std::map<UA_NodeId, std::tuple<std::string, std::string, std::string>, NodeIdComparator> m_nodeIdToPath;
    
    // Write回调函数
    OpcUaServerWriteCallback m_writeCallback;
};


