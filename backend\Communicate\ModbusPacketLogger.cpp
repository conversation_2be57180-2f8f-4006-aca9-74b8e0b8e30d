#include "ModbusPacketLogger.h"
#include "Utils/Utils.hpp"
#include <iomanip>

namespace COMM
{
    std::mutex ModbusPacketLogger::logMutex_;
    std::unique_ptr<std::ofstream> ModbusPacketLogger::logFile_;
    bool ModbusPacketLogger::enabled_ = false;
    std::string ModbusPacketLogger::logFilePath_ = "";

    void ModbusPacketLogger::initialize(const std::string& logPath)
    {
        std::lock_guard<std::mutex> lock(logMutex_);
        logFilePath_ = logPath;
        logFile_ = std::make_unique<std::ofstream>(logPath, std::ios::app);
        enabled_ = logFile_->is_open();
    }

    void ModbusPacketLogger::enable(bool enable)
    {
        std::lock_guard<std::mutex> lock(logMutex_);
        enabled_ = enable;
    }

    void ModbusPacketLogger::logPacket(bool isSend, const std::vector<uint8_t>& packet, const std::string& context)
    {
        logPacket(isSend, packet.data(), packet.size(), context);
    }

    void ModbusPacketLogger::logPacket(bool isSend, const uint8_t* data, size_t length, const std::string& context)
    {
        if (!enabled_ || !logFile_ || !logFile_->is_open()) {
            return;
        }

        std::lock_guard<std::mutex> lock(logMutex_);
        
        *logFile_ << "[" << getCurrentTimestamp() << "] ";
        *logFile_ << (isSend ? "TX -> " : "RX <- ");
        
        if (!context.empty()) {
            *logFile_ << "[" << context << "] ";
        }
        
        *logFile_ << formatPacket(data, length) << std::endl;
        
        // 立即刷新到文件
        logFile_->flush();
    }

    void ModbusPacketLogger::flush()
    {
        std::lock_guard<std::mutex> lock(logMutex_);
        if (logFile_ && logFile_->is_open()) {
            logFile_->flush();
        }
    }

    void ModbusPacketLogger::close()
    {
        std::lock_guard<std::mutex> lock(logMutex_);
        if (logFile_) {
            logFile_->close();
            logFile_.reset();
        }
    }

    std::string ModbusPacketLogger::getCurrentTimestamp()
    {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()) % 1000;

        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        ss << '.' << std::setfill('0') << std::setw(3) << ms.count();
        return ss.str();
    }

    std::string ModbusPacketLogger::formatPacket(const uint8_t* data, size_t length)
    {
        std::stringstream ss;
        ss << std::hex << std::setfill('0') << std::uppercase;
        
        for (size_t i = 0; i < length; ++i) {
            ss << std::setw(2) << static_cast<int>(data[i]);
            if (i < length - 1) {
                ss << " ";
            }
        }
        
        // 添加ASCII显示（可选）
        if (length > 0) {
            ss << "  | ";
            for (size_t i = 0; i < length; ++i) {
                char c = static_cast<char>(data[i]);
                ss << (std::isprint(c) ? c : '.');
            }
        }
        
        return ss.str();
    }
}