[Project]
FileName=AVROSP.dev
Name=AVROSP
UnitCount=12
Type=1
Ver=1
ObjFiles=
Includes=
Libs=
PrivateResource=
ResourceIncludes=
MakeIncludes=
Compiler=
CppCompiler=
Linker=
IsCpp=1
Icon=
ExeOutput=
ObjectOutput=
OverrideOutput=0
OverrideOutputName=AVROSP.exe
HostApplication=
Folders=
CommandLine= -dATmega16 -if\temp\rnd8KB.hex -pf
IncludeVersionInfo=0
SupportXPThemes=0
CompilerSet=0
CompilerSettings=000000000000000100
UseCustomMakefile=0
CustomMakefile=

[Unit1]
FileName=main.cpp
CompileCpp=1
Folder=
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[VersionInfo]
Major=0
Minor=1
Release=1
Build=1
LanguageID=1033
CharsetID=1252
CompanyName=
FileVersion=
FileDescription=Developed using the Dev-C++ IDE
InternalName=
LegalCopyright=
LegalTrademarks=
OriginalFilename=
ProductName=
ProductVersion=
AutoIncBuildNr=0

[Unit2]
FileName=CommChannel.cpp
CompileCpp=1
Folder=Serialtest
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[Unit3]
FileName=ErrorMsg.cpp
CompileCpp=1
Folder=Serialtest
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[Unit4]
FileName=JobInfo.cpp
CompileCpp=1
Folder=AVROSP
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[Unit5]
FileName=AVRDevice.cpp
CompileCpp=1
Folder=AVROSP
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[Unit6]
FileName=HEXParser.cpp
CompileCpp=1
Folder=AVROSP
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[Unit7]
FileName=XMLParser.cpp
CompileCpp=1
Folder=AVROSP
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[Unit8]
FileName=AVRBootloader.cpp
CompileCpp=1
Folder=AVROSP
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[Unit9]
FileName=AVRProgrammer.cpp
CompileCpp=1
Folder=AVROSP
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[Unit10]
FileName=Utility.cpp
CompileCpp=1
Folder=
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[Unit11]
FileName=SerialPort.cpp
CompileCpp=1
Folder=AVROSP
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[Unit12]
FileName=AVRInSystemProg.cpp
CompileCpp=1
Folder=AVROSP
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

