{"name": "ModbusRTU(Build-202316)", "type": "empty", "category": "Modbus协议", "categoryNumber": 5, "systemClass": 1, "scriptType": "c++", "information": "Modbus是一种串行通信协议，是Modicon公司（现在的施耐德电气Schneider Electric）于1979年为使用可编程逻辑控制器（PLC）通信而发表。Modbus已经成为工业领域通信协议的业界标准（De facto），并且现在是工业电子设备之间常用的连接方式。", "2DModel": [], "3DModel": [], "version": 1, "iemsProtocolClass": "modbus_rtu", "iemsProtocolInterfaceType": "rs485", "argument": [{"name": "IsContiguousAddress", "label": "组包模式", "description": "组包模式", "required": true, "type": "string", "visible": true, "defaultValue": "true", "inputType": "select", "items": [{"text": "模式一", "value": "true"}, {"text": "模式二", "value": "false"}], "_level": 2}, {"name": "QuantityPerRequest", "label": "单次请求个数", "description": "一次请求寄存器的个数", "required": true, "type": "number", "inputType": "numberInput", "visible": true, "min": 1, "max": 120, "defaultValue": 120, "_level": 2}, {"name": "RequestInterval", "label": "采集间隔", "description": "请求时间间隔,单位：毫秒", "required": true, "type": "number", "inputType": "numberInput", "visible": true, "min": 0, "max": 60000, "defaultValue": 0, "_level": 2}, {"name": "BaseAddress", "label": "起始地址", "description": "请求的起始地址,0或者1", "required": true, "type": "number", "visible": true, "defaultValue": 0, "inputType": "select", "items": [{"text": "0", "value": 0}, {"text": "1", "value": 1}], "_level": 2}, {"name": "SlaveId", "label": "从站号", "description": "从站地址,从0开始，如无填255", "required": true, "type": "number", "inputType": "numberInput", "min": 0, "max": 255, "precision": 0, "defaultValue": 255, "_level": 2}, {"name": "Address", "label": "寄存器地址", "description": "寄存器地址", "required": true, "type": "string", "inputType": "input", "defaultValue": 0, "_level": 3}, {"name": "ByteOrder", "label": "大小端交换", "description": "大小端交换模式，不填写默认为ABCD模式", "required": true, "type": "string", "defaultValue": "ABCD", "inputType": "select", "items": [{"text": "AB模式", "value": "AB"}, {"text": "BA模式", "value": "BA"}, {"text": "ABCD模式", "value": "ABCD"}, {"text": "CDAB模式", "value": "CDAB"}, {"text": "BADC模式", "value": "BADC"}, {"text": "DCBA模式", "value": "DCBA"}], "_level": 2}, {"name": "ValueType", "label": "值类型", "description": "数值类型", "required": true, "type": "string", "defaultValue": "Int16", "inputType": "select", "items": [{"text": "Boolean", "value": "Boolean"}, {"text": "Int16", "value": "Int16"}, {"text": "UInt16", "value": "UInt16"}, {"text": "Int32", "value": "Int32"}, {"text": "UInt32", "value": "UInt32"}, {"text": "Float", "value": "Float"}], "_level": 4}], "attribute": [{"code": "_online", "name": "在线状态", "type": "DI", "value": {"ON": "在线", "OFF": "离线"}, "history": {"change": false}, "pointType": "internalState", "description": "设备在线状态"}], "classType": "link", "connection": [{"name": "DeviceName", "label": "串口", "description": "串口地址", "required": true, "inputType": "select", "type": "string", "defaultValue": "/dev/ttymxc6", "visible": true, "_level": 1, "useIEMSCom": true, "items": [{"text": "COM1", "value": "/dev/ttymxc6"}, {"text": "COM2", "value": "/dev/ttymxc7"}, {"text": "COM3", "value": "/dev/ttymxc1"}, {"text": "COM4", "value": "/dev/ttymxc3"}]}, {"name": "BaudRate", "label": "波特率", "description": "波特率", "required": true, "type": "number", "visible": true, "min": 110, "max": 921600, "defaultValue": 9600, "inputType": "select", "items": [{"text": "110", "value": 110}, {"text": "300", "value": 300}, {"text": "600", "value": 600}, {"text": "1200", "value": 1200}, {"text": "2400", "value": 2400}, {"text": "4800", "value": 4800}, {"text": "9600", "value": 9600}, {"text": "14400", "value": 14400}, {"text": "19200", "value": 19200}, {"text": "38400", "value": 38400}, {"text": "57600", "value": 57600}, {"text": "115200", "value": 115200}, {"text": "230400", "value": 230400}, {"text": "380400", "value": 380400}, {"text": "460800", "value": 460800}, {"text": "921600", "value": 921600}], "_level": 1}, {"name": "Parity", "label": "校验位", "description": "奇偶校验位", "required": true, "type": "string", "visible": true, "defaultValue": "N", "inputType": "select", "items": [{"text": "None", "value": "N"}, {"text": "Odd", "value": "O"}, {"text": "Even", "value": "E"}, {"text": "<PERSON>", "value": "M"}, {"text": "Space", "value": "S"}], "_level": 1}, {"name": "DataBit", "label": "数据位", "description": "数据位", "required": true, "type": "number", "visible": true, "min": 5, "max": 8, "defaultValue": 8, "inputType": "select", "items": [{"text": "5", "value": 5}, {"text": "6", "value": 6}, {"text": "7", "value": 7}, {"text": "8", "value": 8}], "_level": 1}, {"name": "StopBit", "label": "停止位", "description": "停止位", "required": true, "type": "number", "visible": true, "min": 1, "max": 2, "defaultValue": 1, "inputType": "select", "items": [{"text": "1", "value": 1}, {"text": "1.5", "value": 1.5}, {"text": "2", "value": 2}], "_level": 1}, {"name": "TimeoutMs", "label": "超时时间", "description": "连接超时时间", "required": true, "type": "number", "inputType": "numberInput", "visible": true, "min": 0, "max": 60000, "defaultValue": 0, "_level": 1}], "driverFile": {"Linux x86_64": "DriverEMSBoxModbusRtu_build202528"}, "description": "ModbusRTU标准驱动", "creationTime": 1682045469078, "systemVersion": "0.0.1", "modificationTime": 1682045469078, "argumentAscription": "point"}