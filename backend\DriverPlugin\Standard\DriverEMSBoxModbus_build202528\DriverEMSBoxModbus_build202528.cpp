#include "DriverEMSBoxModbus_build202528.h"

#include <iostream>
#include <functional>
#include <unordered_map>
#include <algorithm>
#include <chrono>
#include <cmath>
#include <thread>
#include <iomanip>
#include <sstream>

#include "Utils/String.hpp"
#include "Utils/Utils.hpp"

template<typename T> T bcd2dec(uint8_t* bcd, bool isSigned)
{
	T v = 0;
	bool highestBit = UTILS::getBit(*(bcd + sizeof(T) - 1), 7);
	/*if (isSigned)
	{
		*(T*)bcd = std::abs(*(T*)bcd);
	}*/

	for (int i = 0; i < sizeof(T); i++)
	{
		uint8_t temp = *(bcd + i);
		temp = temp - (temp >> 4) * 6;
		v += temp * std::pow(10, i);
	}

	if (isSigned && highestBit)
	{
		v = -v;
	}
	
	return v;
}

namespace DRIVER
{
	static void modbusLogCallBack(int type, unsigned char* data, int len, void* user)
	{
		if (user == NULL)
			return;

		std::stringstream ss;
		for (int i = 0; i < len; i++) {
			ss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>((uint8_t)data[i]) << " ";
		}
		//std::cout << ss.str() << std::endl;

		CDriverEMSBoxModbus_build202528* p = static_cast<CDriverEMSBoxModbus_build202528*>(user);
		if (type == 0)
			p->modbusLog("[send] " + ss.str());
		else if (type == 1)
			p->modbusLog("[recv] " + ss.str());

		//std::cout << ss.str() << std::endl;
	}

	static std::unordered_map<std::string, std::function<void(SValue&, const uint16_t*, char)>> Array2SValueTable =
	{
		{ "Int16", [](SValue& v, const uint16_t* arr, char swapMode) {v = UTILS::arr2var<int16_t, uint16_t>((const uint8_t*)arr, swapMode); }},
		{ "UInt16", [](SValue& v, const uint16_t* arr, char swapMode) {v = UTILS::arr2var<uint16_t, uint16_t>((const uint8_t*)arr, swapMode); } },
		{ "Int32", [](SValue& v, const uint16_t* arr, char swapMode) {v = UTILS::arr2var<int32_t, uint16_t>((const uint8_t*)arr, swapMode); } },
		{ "UInt32", [](SValue& v, const uint16_t* arr, char swapMode) {v = UTILS::arr2var<uint32_t, uint16_t>((const uint8_t*)arr, swapMode); } },
		{ "Float", [](SValue& v, const uint16_t* arr, char swapMode) {v = UTILS::arr2var<float, uint16_t>((const uint8_t*)arr, swapMode); } },
		{ "BCD8421_Int16", [](SValue& v, const uint16_t* arr, char swapMode) {
			int16_t var = UTILS::arr2var<int16_t, uint16_t>((const uint8_t*)arr, swapMode);
			v = bcd2dec<int16_t>((uint8_t*) & var, true); }},
		{ "BCD8421_UInt16", [](SValue& v, const uint16_t* arr, char swapMode) {
			uint16_t var = UTILS::arr2var<uint16_t, uint16_t>((const uint8_t*)arr, swapMode); 
			v = bcd2dec<int16_t>((uint8_t*)&var, true); } },
		{ "BCD8421_Int32", [](SValue& v, const uint16_t* arr, char swapMode) {
			int32_t var = UTILS::arr2var<int32_t, uint16_t>((const uint8_t*)arr, swapMode);
			v = bcd2dec<int32_t>((uint8_t*)&var, true); } },
		{ "BCD8421_UInt32", [](SValue& v, const uint16_t* arr, char swapMode) {
			uint32_t var = UTILS::arr2var<uint32_t, uint16_t>((const uint8_t*)arr, swapMode);
			v = bcd2dec<uint32_t>((uint8_t*)&var, true); } }
	};

	static std::unordered_map<std::string, std::function<void(const SValue&, std::vector<uint16_t>&, char)>> SValue2ArrayTable =
	{
		{ "Int16", [](const SValue& v, std::vector<uint16_t>& arr, char swapMode) {
			uint8_t writeBuff[2] = { 0 };
			UTILS::var2arr<int16_t, uint16_t>(v.toInt<int16_t>(), writeBuff, swapMode);
			arr.emplace_back(*(int16_t*)writeBuff); }},
		{ "UInt16", [](const SValue& v, std::vector<uint16_t>& arr, char swapMode) {
			uint8_t writeBuff[2] = { 0 };
			UTILS::var2arr<uint16_t, uint16_t>(v.toInt<uint16_t>(), writeBuff, swapMode);
			arr.emplace_back(*(uint16_t*)writeBuff); } },
		{ "Int32", [](const SValue& v, std::vector<uint16_t>& arr, char swapMode) {
			uint8_t writeBuff[4] = { 0 };
			UTILS::var2arr<int32_t, uint16_t>(v.toInt<int32_t>(), writeBuff, swapMode);
			arr.emplace_back(*(int16_t*)writeBuff);
			arr.emplace_back(*((int16_t*)writeBuff + 1)); } },
		{ "UInt32", [](const SValue& v, std::vector<uint16_t>& arr, char swapMode) {
			uint8_t writeBuff[4] = { 0 };
			UTILS::var2arr<uint32_t, uint16_t>(v.toInt<uint32_t>(), writeBuff, swapMode);
			arr.emplace_back(*(uint16_t*)writeBuff);
			arr.emplace_back(*((uint16_t*)writeBuff + 1)); } },
		{ "Float", [](const SValue& v, std::vector<uint16_t>& arr, char swapMode) {
			uint8_t writeBuff[4] = { 0 };
			if (v.getType() == SValue::eValueInt)
				UTILS::var2arr<float, uint16_t>(v.toInt<int32_t>(), writeBuff, swapMode);
			else 
				UTILS::var2arr<float, uint16_t>(v.toFloat(), writeBuff, swapMode);
			arr.emplace_back(*(int16_t*)writeBuff);
			arr.emplace_back(*((int16_t*)writeBuff + 1)); } },
		{ "BCD8421_Int16", [](const SValue& v, std::vector<uint16_t>& arr, char swapMode) {
			int16_t bcd = v.toInt<int16_t>();
			bcd = bcd + bcd / 10 * 6;
			uint8_t writeBuff[2] = { 0 };
			UTILS::var2arr<int16_t, uint16_t>(bcd, writeBuff, swapMode); } },
		{ "BCD8421_UInt16", [](const SValue& v, std::vector<uint16_t>& arr, char swapMode) {
			uint16_t bcd = v.toInt<uint16_t>();
			bcd = bcd + bcd / 10 * 6;
			uint8_t writeBuff[2] = { 0 };
			UTILS::var2arr<uint16_t, uint16_t>(bcd, writeBuff, swapMode); } },
		{ "BCD8421_Int32", [](const SValue& v, std::vector<uint16_t>& arr, char swapMode) {
			int32_t bcd = v.toInt<int32_t>();
			bcd = bcd + bcd / 10 * 6;
			uint8_t writeBuff[4] = { 0 };
			UTILS::var2arr<int32_t, uint16_t>(bcd, writeBuff, swapMode); } },
		{ "BCD8421_UInt32", [](const SValue& v, std::vector<uint16_t>& arr, char swapMode) {
			uint32_t bcd = v.toInt<uint32_t>();
			bcd = bcd + bcd / 10 * 6;
			uint8_t writeBuff[4] = { 0 };
			UTILS::var2arr<uint32_t, uint16_t>(bcd, writeBuff, swapMode); } }
	};

	static std::unordered_map<std::string, char> ByteOrder_SwapMode_Mappings =
	{
			{ "AB", 'n'},
			{ "BA", 'b'},
			{ "ABCD", 'n'},
			{ "DCBA", 'a'},
			{ "CDAB", 'w'},
			{ "BADC", 'b'}
	};

	static std::unordered_map<std::string, SValue::EValueType> dataTypeToSValueData = {
		{ "Boolean",    SValue::EValueType::eValueBoolean },
		{ "Int16",              SValue::EValueType::eValueInt},
		{ "UInt16",             SValue::EValueType::eValueUInt},
		{ "Int32",              SValue::EValueType::eValueInt},
		{ "UInt32",             SValue::EValueType::eValueUInt},
		{ "Float",              SValue::EValueType::eValueReal},
	};

	static void getSpaceSize(COMM::ICommModbus* modbusClient, int tableBitsSize, int tableInputBitsSize, int tableInputRegistersSize, int tableRegistersSize)
	{
		/*uint8_t* bitsBuf = new uint8_t[MODBUS_MAX_READ_BITS];
		int nb = MODBUS_MAX_READ_BITS;
		int max = MODBUS_MAX_READ_BITS;
		while (true)
		{
			int rc = modbus_read_bits(ctx_, 0, max, bitsBuf);
			if (rc > 0)
			{

			}
		}*/



	}

	SValue CDriverEMSBoxModbus_build202528::data2svalue(const uint16_t* data, const std::string& valueType, char swapMode, int offset)
	{
		SValue sv;
		if (valueType == "Boolean")
		{
			if (offset >= 0 && offset < 16)
			{
				sv = UTILS::getBit(UTILS::arr2var<int16_t, uint16_t>((const uint8_t*)data, swapMode), offset);
			}
		}
		else
		{
			auto convertFuncIter = Array2SValueTable.find(valueType);
			if (convertFuncIter != Array2SValueTable.end())
			{
				convertFuncIter->second(sv, data, swapMode);
				if (dataTypeToSValueData.find(valueType) != dataTypeToSValueData.end())
				{
					sv.setType(dataTypeToSValueData[valueType]);
				}
			}
		}

		return sv;
	}

	EStatusCode CDriverEMSBoxModbus_build202528::requestContiguousData(std::string deviceId)
	{
		EStatusCode rsc = EStatusCode::eStatusSuccess;
		auto devConfig = deviceIsContiguous_[deviceId];
		auto& contiguousDataTables = deviceContiguousDataTables_[deviceId];
		for (auto& slaveTable : contiguousDataTables)
		{
			//cb_->onLog(eLogInfo, "slave id = " + std::to_string(slaveTable.first));
			//cb_->onLog(eLogInfo, UTILS::STRING::SFormat("poll param: minBitAddr = {0}, minInputBitAddr={1}, minInputRegisterAddr={2}, minRegisterAddr={3}, maxBitAddr = {4}, maxInputBitAddr={5}, maxInputRegisterAddr={6}, maxRegisterAddr={7}",
			//	slaveTable.second.scaddr->minBitAddr, slaveTable.second.scaddr->minInputBitAddr, slaveTable.second.scaddr->minInputRegisterAddr, slaveTable.second.scaddr->minRegisterAddr,
			//	slaveTable.second.scaddr->maxBitAddr, slaveTable.second.scaddr->maxInputBitAddr, slaveTable.second.scaddr->maxInputRegisterAddr, slaveTable.second.scaddr->maxRegisterAddr));

			modbusClient_->setSlaveId(slaveTable.first);

			if (slaveTable.second.scaddr->maxBitAddr >= 0)
			{
				int rc = -1;
				int addr = slaveTable.second.scaddr->minBitAddr;
				int quantityPerRequest = devConfig.quantityPerRequest;
				if (quantityPerRequest > slaveTable.second.scaddr->maxBitAddr - slaveTable.second.scaddr->minBitAddr)
					quantityPerRequest = slaveTable.second.scaddr->maxBitAddr - slaveTable.second.scaddr->minBitAddr + 1;
				std::vector<uint8_t> buf(quantityPerRequest);
				
				std::vector<uint8_t> sendVec, recvVec;
				while (quantityPerRequest > 0)
				{
					if (flag_ == EFlag::eFlagInterrupt) return eStatusSuccess;
					//rc = modbusClient_->readBits(addr, quantityPerRequest, buf);
					rc = modbusClient_->readBits(addr, quantityPerRequest, buf, sendVec, recvVec);
					logIEMSPacket(deviceId, sendVec, recvVec);
					if (rc > 0)
					{
						errCount_ = 0;
						memcpy(slaveTable.second.tableBits.data() + addr, buf.data(), rc);
					}
					else
					{
						cb_->onLog(eLogError, "while read bits error : " + modbusClient_->getLastError() + ", addr=" + std::to_string(addr));
						errCount_++;
						if (rc == COMM::ICommModbus::ERR_LINK)
							return EStatusCode::eStatusNeedReopen;
						//else
						//	return EStatusCode::eStatusFail;
					}
					addr += quantityPerRequest;
					int remainBits = slaveTable.second.scaddr->maxBitAddr - addr + 1;
					quantityPerRequest = remainBits / quantityPerRequest > 0 ? quantityPerRequest : remainBits;
					if (devConfig.requestInterval > 0)
					{
						//std::this_thread::sleep_for(std::chrono::milliseconds(cp->requestInterval));
						uint64_t total = 0;
						while (true)
						{
							if (flag_ == EFlag::eFlagInterrupt) return eStatusSuccess;
							std::this_thread::sleep_for(std::chrono::milliseconds(50));
							total += 50;
							if (total >= devConfig.requestInterval)
								break;
						}
					}
				}
			}

			if (slaveTable.second.scaddr->maxInputBitAddr >= 0)
			{
				int rc = -1;
				int quantityPerRequest = devConfig.quantityPerRequest;
				int addr = slaveTable.second.scaddr->minInputBitAddr;
				if (quantityPerRequest > slaveTable.second.scaddr->maxInputBitAddr - slaveTable.second.scaddr->minInputBitAddr)
					quantityPerRequest = slaveTable.second.scaddr->maxInputBitAddr - slaveTable.second.scaddr->minInputBitAddr + 1;
				std::vector<uint8_t> buf(quantityPerRequest);
				std::vector<uint8_t> sendVec, recvVec;
				while (quantityPerRequest > 0)
				{
					if (flag_ == EFlag::eFlagInterrupt) return eStatusSuccess;
					//rc = modbusClient_->readInputBits(addr, quantityPerRequest, buf);
					rc = modbusClient_->readInputBits(addr, quantityPerRequest, buf, sendVec, recvVec);
					logIEMSPacket(deviceId, sendVec, recvVec);
					if (rc > 0)
					{
						errCount_ = 0;
						memcpy(slaveTable.second.tableInputBits.data() + addr, buf.data(), rc);
					}
					else
					{
						cb_->onLog(eLogError, "while read input bits error : " + modbusClient_->getLastError() + ", addr=" + std::to_string(addr));
						errCount_++;
						if (rc == COMM::ICommModbus::ERR_LINK)
							return EStatusCode::eStatusNeedReopen;
						//else
						//	return EStatusCode::eStatusFail;
					}
					addr += quantityPerRequest;
					int remainBits = slaveTable.second.scaddr->maxInputBitAddr - addr + 1;
					quantityPerRequest = remainBits / quantityPerRequest > 0 ? quantityPerRequest : remainBits;
					if (devConfig.requestInterval > 0)
					{
						uint64_t total = 0;
						while (true)
						{
							if (flag_ == EFlag::eFlagInterrupt) return eStatusSuccess;
							std::this_thread::sleep_for(std::chrono::milliseconds(50));
							total += 50;
							if (total >= devConfig.requestInterval)
								break;
						}
					}
				}
			}

			if (slaveTable.second.scaddr->maxRegisterAddr >= 0)
			{
				int rc = -1;
				int quantityPerRequest = devConfig.quantityPerRequest;
				int addr = slaveTable.second.scaddr->minRegisterAddr;
				if (quantityPerRequest > slaveTable.second.scaddr->maxRegisterAddr - slaveTable.second.scaddr->minRegisterAddr)
					quantityPerRequest = slaveTable.second.scaddr->maxRegisterAddr - slaveTable.second.scaddr->minRegisterAddr + 1;
				std::vector<uint16_t> buf(quantityPerRequest);
				std::vector<uint8_t> sendVec, recvVec;
				while (quantityPerRequest > 0)
				{
					if (flag_ == EFlag::eFlagInterrupt) return eStatusSuccess;
					cb_->onLog(eLogInfo, UTILS::STRING::SFormat("read reg addr:{0},quant:{1}", addr, quantityPerRequest));
					//rc = modbusClient_->readRegisters(addr, quantityPerRequest, buf);
					rc = modbusClient_->readRegisters(addr, quantityPerRequest, buf, sendVec, recvVec);
					logIEMSPacket(deviceId, sendVec, recvVec);
					if (rc > 0)
					{
						errCount_ = 0;
						memcpy(slaveTable.second.tableRegisters.data() + addr, buf.data(), rc * 2);
					}
					else
					{
						cb_->onLog(eLogError, "while read registers error : " + modbusClient_->getLastError() + ", addr=" + std::to_string(addr));
						errCount_++;
						if (rc == COMM::ICommModbus::ERR_LINK)
							return EStatusCode::eStatusNeedReopen;
						//else
						//	return EStatusCode::eStatusFail;
					}
					addr += quantityPerRequest;
					int remainRegisters = slaveTable.second.scaddr->maxRegisterAddr - addr + 1;
					quantityPerRequest = remainRegisters / quantityPerRequest > 0 ? quantityPerRequest : remainRegisters;
					if (devConfig.requestInterval > 0)
					{
						uint64_t total = 0;
						while (true)
						{
							if (flag_ == EFlag::eFlagInterrupt) return eStatusSuccess;
							std::this_thread::sleep_for(std::chrono::milliseconds(50));
							total += 50;
							if (total >= devConfig.requestInterval)
								break;
						}
					}
				}
				cb_->onLog(eLogInfo, UTILS::STRING::SFormat("read reg addr:{0},quant:{1}", addr, quantityPerRequest));
			}

			if (slaveTable.second.scaddr->maxInputRegisterAddr >= 0)
			{
				int rc = -1;
				int quantityPerRequest = devConfig.quantityPerRequest;
				int addr = slaveTable.second.scaddr->minInputRegisterAddr;
				if (quantityPerRequest > slaveTable.second.scaddr->maxInputRegisterAddr - slaveTable.second.scaddr->minInputRegisterAddr)
					quantityPerRequest = slaveTable.second.scaddr->maxInputRegisterAddr - slaveTable.second.scaddr->minInputRegisterAddr + 1;
				std::vector<uint16_t> buf(quantityPerRequest);
				std::vector<uint8_t> sendVec, recvVec;
				while (quantityPerRequest > 0)
				{
					if (flag_ == EFlag::eFlagInterrupt) return eStatusSuccess;
					cb_->onLog(eLogInfo, UTILS::STRING::SFormat("read in reg addr:{0},quant:{1}", addr, quantityPerRequest));
					//rc = modbusClient_->readInputRegisters(addr, quantityPerRequest, buf);
					rc = modbusClient_->readInputRegisters(addr, quantityPerRequest, buf, sendVec, recvVec);
					logIEMSPacket(deviceId, sendVec, recvVec);
					if (rc > 0)
					{
						errCount_ = 0;
						memcpy(slaveTable.second.tableInputRegisters.data() + addr, buf.data(), rc * 2);
					}
					else
					{
						cb_->onLog(eLogError, "while read input registers error : " + modbusClient_->getLastError() + ", addr=" + std::to_string(addr));
						errCount_++;
						if (rc == COMM::ICommModbus::ERR_LINK)
							return EStatusCode::eStatusNeedReopen;
						//else
						//	return EStatusCode::eStatusFail;
					}
					addr += quantityPerRequest;
					int remainRegisters = slaveTable.second.scaddr->maxInputRegisterAddr - addr + 1;
					quantityPerRequest = remainRegisters / quantityPerRequest > 0 ? quantityPerRequest : remainRegisters;
					if (devConfig.requestInterval > 0)
					{
						uint64_t total = 0;
						while (true)
						{
							if (flag_ == EFlag::eFlagInterrupt) return eStatusSuccess;
							std::this_thread::sleep_for(std::chrono::milliseconds(50));
							total += 50;
							if (total >= devConfig.requestInterval)
								break;
						}
					}
				}
				cb_->onLog(eLogInfo, UTILS::STRING::SFormat("read in reg addr:{0},quant:{1}", addr, quantityPerRequest));
			}
		}

		return rsc;
	}

	EStatusCode CDriverEMSBoxModbus_build202528::requestNonContiguousData(std::string deviceId)
	{
		EStatusCode rsc = EStatusCode::eStatusSuccess;
		auto devConfig = deviceIsContiguous_[deviceId];
		auto& nonContiguousDataIndexTables = deviceNonContiguousDataIndexTables_[deviceId];
		for (auto& nonContiguousDataIndexTablesIter : nonContiguousDataIndexTables)
		{
			auto& contiguousDataTable = deviceContiguousDataTables_[deviceId][nonContiguousDataIndexTablesIter.first];
			modbusClient_->setSlaveId(nonContiguousDataIndexTablesIter.first);

			for (auto& segmentIter : nonContiguousDataIndexTablesIter.second.bits)
			{
				int addr = segmentIter.minAddr;
				int quantityPerRequest = devConfig.quantityPerRequest;
				if (quantityPerRequest > segmentIter.maxAddr - segmentIter.minAddr)
					quantityPerRequest = segmentIter.maxAddr - segmentIter.minAddr + 1;

				std::vector<uint8_t> buf(quantityPerRequest);
				int rc = -1;
				std::vector<uint8_t> sendVec, recvVec;
				while (quantityPerRequest > 0)
				{
					if (flag_ == EFlag::eFlagInterrupt) return eStatusSuccess;
					//rc = modbusClient_->readBits(addr, quantityPerRequest, buf);
					rc = modbusClient_->readBits(addr, quantityPerRequest, buf, sendVec, recvVec);
					logIEMSPacket(deviceId, sendVec, recvVec);
					if (rc > 0)
					{
						errCount_ = 0;
						memcpy(contiguousDataTable.tableBits.data() + addr, buf.data(), rc);
					}
					else
					{
						cb_->onLog(eLogError, "while read bits error : " + modbusClient_->getLastError() + ", addr=" + std::to_string(addr));
						errCount_++;
						if (rc == COMM::ICommModbus::ERR_LINK)
							return eStatusNeedReopen;
					}
					addr += quantityPerRequest;
					int remainBits = segmentIter.maxAddr - addr + 1;
					quantityPerRequest = remainBits / quantityPerRequest > 0 ? quantityPerRequest : remainBits;
					if (devConfig.requestInterval > 0)
					{
						uint64_t total = 0;
						while (true)
						{
							if (flag_ == EFlag::eFlagInterrupt) return eStatusSuccess;
							std::this_thread::sleep_for(std::chrono::milliseconds(50));
							total += 50;
							if (total >= devConfig.requestInterval)
								break;
						}
					}
				}
			}

			for (auto& segmentIter : nonContiguousDataIndexTablesIter.second.inputBits)
			{
				int quantityPerRequest = devConfig.quantityPerRequest;
				if (quantityPerRequest > segmentIter.maxAddr - segmentIter.minAddr)
					quantityPerRequest = segmentIter.maxAddr - segmentIter.minAddr + 1;

				int addr = segmentIter.minAddr;
				std::vector<uint8_t> buf(quantityPerRequest);
				int rc = -1;
				std::vector<uint8_t> sendVec, recvVec;
				while (quantityPerRequest > 0)
				{
					if (flag_ == EFlag::eFlagInterrupt) return eStatusSuccess;
					rc = modbusClient_->readInputBits(addr, quantityPerRequest, buf, sendVec, recvVec);
					logIEMSPacket(deviceId, sendVec, recvVec);
					if (rc > 0)
					{
						errCount_ = 0;
						memcpy(contiguousDataTable.tableInputBits.data() + addr, buf.data(), rc);
					}
					else
					{
						cb_->onLog(eLogError, "while read input bits error : " + modbusClient_->getLastError() + ", addr=" + std::to_string(addr));
						errCount_++;
						if (rc == COMM::ICommModbus::ERR_LINK)
							return eStatusNeedReopen;
					}
					addr += quantityPerRequest;
					int remainBits = segmentIter.maxAddr - addr + 1;
					quantityPerRequest = remainBits / quantityPerRequest > 0 ? quantityPerRequest : remainBits;
					if (devConfig.requestInterval > 0)
					{
						uint64_t total = 0;
						while (true)
						{
							if (flag_ == EFlag::eFlagInterrupt) return eStatusSuccess;
							std::this_thread::sleep_for(std::chrono::milliseconds(50));
							total += 50;
							if (total >= devConfig.requestInterval)
								break;
						}
					}
				}
			}

			for (auto& segmentIter : nonContiguousDataIndexTablesIter.second.registers)
			{
				int quantityPerRequest = devConfig.quantityPerRequest;
				if (quantityPerRequest > segmentIter.maxAddr - segmentIter.minAddr)
					quantityPerRequest = segmentIter.maxAddr - segmentIter.minAddr + 1;

				int addr = segmentIter.minAddr;
				std::vector<uint16_t> buf(quantityPerRequest);
				int rc = -1;
				std::vector<uint8_t> sendVec, recvVec;
				while (quantityPerRequest > 0)
				{
					if (flag_ == EFlag::eFlagInterrupt) return eStatusSuccess;
					rc = modbusClient_->readRegisters(addr, quantityPerRequest, buf, sendVec, recvVec);
					logIEMSPacket(deviceId, sendVec, recvVec);
					if (rc > 0)
					{
						errCount_ = 0;
						memcpy(contiguousDataTable.tableRegisters.data() + addr, buf.data(), rc * sizeof(uint16_t));
					}
					else
					{
						cb_->onLog(eLogError, "while read registers error : " + modbusClient_->getLastError() + ", addr=" + std::to_string(addr));
						errCount_++;
						if (rc == COMM::ICommModbus::ERR_LINK)
							return eStatusNeedReopen;
					}
					addr += quantityPerRequest;
					int remainRegisters = segmentIter.maxAddr - addr + 1;
					quantityPerRequest = remainRegisters / quantityPerRequest > 0 ? quantityPerRequest : remainRegisters;
					if (devConfig.requestInterval > 0)
					{
						uint64_t total = 0;
						while (true)
						{
							if (flag_ == EFlag::eFlagInterrupt) return eStatusSuccess;
							std::this_thread::sleep_for(std::chrono::milliseconds(50));
							total += 50;
							if (total >= devConfig.requestInterval)
								break;
						}
					}
				}
			}

			for (auto& segmentIter : nonContiguousDataIndexTablesIter.second.inputRegisters)
			{
				int quantityPerRequest = devConfig.quantityPerRequest;
				if (quantityPerRequest > segmentIter.maxAddr - segmentIter.minAddr)
					quantityPerRequest = segmentIter.maxAddr - segmentIter.minAddr + 1;

				int addr = segmentIter.minAddr;
				std::vector<uint16_t> buf(quantityPerRequest);
				int rc = -1;
				std::vector<uint8_t> sendVec, recvVec;
				while (quantityPerRequest > 0)
				{
					if (flag_ == EFlag::eFlagInterrupt) return eStatusSuccess;
					rc = modbusClient_->readInputRegisters(addr, quantityPerRequest, buf, sendVec, recvVec);
					logIEMSPacket(deviceId, sendVec, recvVec);
					if (rc > 0)
					{
						errCount_ = 0;
						memcpy(contiguousDataTable.tableInputRegisters.data() + addr, buf.data(), rc * sizeof(uint16_t));
					}
					else
					{
						cb_->onLog(eLogError, "while read input registers error : " + modbusClient_->getLastError() + ", addr=" + std::to_string(addr));
						errCount_++;
						if (rc == COMM::ICommModbus::ERR_LINK)
							return eStatusNeedReopen;
					}
					addr += quantityPerRequest;
					int remainRegisters = segmentIter.maxAddr - addr + 1;
					quantityPerRequest = remainRegisters / quantityPerRequest > 0 ? quantityPerRequest : remainRegisters;

					if (devConfig.requestInterval > 0)
					{
						uint64_t total = 0;
						while (true)
						{
							if (flag_ == EFlag::eFlagInterrupt) return eStatusSuccess;
							std::this_thread::sleep_for(std::chrono::milliseconds(50));
							total += 50;
							if (total >= devConfig.requestInterval)
								break;
						}
					}
				}
			}
		}
		return rsc;
	}

	bool CDriverEMSBoxModbus_build202528::parseData(const SDriverEMSBoxModbus_build202528PointProtocol* pointProtocol, std::string deviceId, const SIEMSDeviceArg& config, SValue& value)
	{
		bool ret = true;
		auto& slaveTable = deviceContiguousDataTables_[deviceId][pointProtocol->slaveId];
		switch (pointProtocol->addrType)
		{
		case EModbusAddrType::eInputCoils:
			value = (bool)slaveTable.tableInputBits[pointProtocol->address];
			break;
		case EModbusAddrType::eOutputCoils:
			value = (bool)slaveTable.tableBits[pointProtocol->address];
			break;
		case EModbusAddrType::eHoldingRegisters:
			value = data2svalue(slaveTable.tableRegisters.data() + pointProtocol->address, pointProtocol->valueType, config.swapMode, pointProtocol->offset);
			break;
		case EModbusAddrType::eInternalRegisters:
			value = data2svalue(slaveTable.tableInputRegisters.data() + pointProtocol->address, pointProtocol->valueType, config.swapMode, pointProtocol->offset);
			break;
		default:
			break;
		}
		
		return ret;
	}

	SDriverEMSBoxModbus_build202528PointProtocol* CDriverEMSBoxModbus_build202528::createPointProtocol(OriginProtocol& originProtocol)
	{
		SDriverEMSBoxModbus_build202528PointProtocol* protocol = nullptr;
		if (originProtocol["SlaveId"].empty())
		{
			return protocol;
		}
		int slaveId = atoi(originProtocol["SlaveId"].c_str());
		if (slaveId < 0)
		{
			return protocol;
		}

		if (originProtocol["Address"].empty())
		{
			return protocol;
		}

		int baseAddress = atoi(originProtocol["BaseAddress"].c_str());
		if (baseAddress != 1 || originProtocol["BaseAddress"].empty())
		{
			baseAddress = 0;
		}
		int quantityPerRequest = atoi(originProtocol["QuantityPerRequest"].c_str());
		if (quantityPerRequest < 1 || quantityPerRequest > 120 || originProtocol["QuantityPerRequest"].empty())
		{
			quantityPerRequest = 120;
		}
		int requestInterval = atoi(originProtocol["RequestInterval"].c_str());
		if (requestInterval < 0 || originProtocol["RequestInterval"].empty())
		{
			requestInterval = 0;
		}

		std::string valueType = originProtocol["ValueType"];
		int address = -1;
		int offset = -1;
		auto strVec = UTILS::splitString(originProtocol["Address"], ".");
		int addrType = strVec[0][0] - '0';
		strVec[0].erase(strVec[0].begin());
		address = atoi(strVec[0].c_str());
		address -= baseAddress;
		if (address < 0)
		{
			return protocol;
		}

		if (addrType == EModbusAddrType::eInputCoils || addrType == EModbusAddrType::eOutputCoils)
		{
			if (strVec.size() > 1)
			{
				return protocol;
			}
			else
			{
				valueType = "Boolean";
			}
		}
		else if (addrType == EModbusAddrType::eInternalRegisters || addrType == EModbusAddrType::eHoldingRegisters)
		{
			if (strVec.size() > 1 && valueType == "Boolean")
			{
				offset = atoi(strVec[1].c_str());
				if (offset > 15)
				{
					return protocol;
				}
			}
		}
		else
		{
			return protocol;
		}
		
		char swapMode = 'n';
		std::string byteOrder = originProtocol["ByteOrder"];
		if (!byteOrder.empty())
		{
			auto byteOrderIter = ByteOrder_SwapMode_Mappings.find(byteOrder);
			if (byteOrderIter == ByteOrder_SwapMode_Mappings.end())
			{
				return protocol;
			}
			swapMode = byteOrderIter->second;
		}

		auto valueTypeIter = Array2SValueTable.find(valueType);
		if (valueTypeIter == Array2SValueTable.end() && valueType != "Boolean")
		{
			return protocol;
		}
		
		protocol = new SDriverEMSBoxModbus_build202528PointProtocol;
		protocol->slaveId = slaveId;
		protocol->addrType = (EModbusAddrType)addrType;
		protocol->address = address;
		protocol->deviceId = originProtocol["_device"];
		protocol->offset = offset;
		protocol->swapMode = swapMode;
		protocol->valueType = valueType;
		protocol->mixAddress = atoi(originProtocol["Address"].c_str());
		protocol->key = std::to_string(slaveId) + "." + originProtocol["Address"] + "." + byteOrder + "." + valueType;

		devicePointsMap_[protocol->deviceId].push_back(protocol);
		deviceIsContiguous_[protocol->deviceId] = SIEMSDeviceArg();
		if (originProtocol.find("IsContiguousAddress") != originProtocol.end())
		{
			deviceIsContiguous_[protocol->deviceId].isContiguousAddress = originProtocol["IsContiguousAddress"] == "true";
		}
		deviceIsContiguous_[protocol->deviceId].slaveId = protocol->slaveId;
		deviceIsContiguous_[protocol->deviceId].swapMode = protocol->swapMode;
		deviceIsContiguous_[protocol->deviceId].quantityPerRequest = quantityPerRequest;
		deviceIsContiguous_[protocol->deviceId].requestInterval = requestInterval;
		deviceIsContiguous_[protocol->deviceId].baseAddress = baseAddress;

		switch ((EModbusAddrType)addrType)
		{
		case eOutputCoils: 
			//if (address > contiguousDataTables_[slaveId].maxBitAddr)
			//{
			//	contiguousDataTables_[slaveId].maxBitAddr = address;
			//}
			if (address > deviceContiguousDataTables_[protocol->deviceId][slaveId].maxBitAddr)
			{
				deviceContiguousDataTables_[protocol->deviceId][slaveId].maxBitAddr = address;
			}


			//if (address < contiguousDataTables_[slaveId].minBitAddr)
			//{
			//	contiguousDataTables_[slaveId].minBitAddr = address;
			//}
			if (address < deviceContiguousDataTables_[protocol->deviceId][slaveId].minBitAddr)
			{
				deviceContiguousDataTables_[protocol->deviceId][slaveId].minBitAddr = address;
			}

			break;
		case eInputCoils: 
			//if (address > contiguousDataTables_[slaveId].maxInputBitAddr)
			//{
			//	contiguousDataTables_[slaveId].maxInputBitAddr = address;
			//}
			if (address > deviceContiguousDataTables_[protocol->deviceId][slaveId].maxInputBitAddr)
			{
				deviceContiguousDataTables_[protocol->deviceId][slaveId].maxInputBitAddr = address;
			}
			//if (address < contiguousDataTables_[slaveId].minInputBitAddr)
			//{
			//	contiguousDataTables_[slaveId].minInputBitAddr = address;
			//}
			if (address < deviceContiguousDataTables_[protocol->deviceId][slaveId].minInputBitAddr)
			{
				deviceContiguousDataTables_[protocol->deviceId][slaveId].minInputBitAddr = address;
			}
			break;
		case eInternalRegisters: 
			//if (address > contiguousDataTables_[slaveId].maxInputRegisterAddr)
			//{
			//	contiguousDataTables_[slaveId].maxInputRegistersValueType = valueType;
			//	contiguousDataTables_[slaveId].maxInputRegisterAddr = address;
			//}
			if (address > deviceContiguousDataTables_[protocol->deviceId][slaveId].maxInputRegisterAddr)
			{
				deviceContiguousDataTables_[protocol->deviceId][slaveId].maxInputRegistersValueType = valueType;
				deviceContiguousDataTables_[protocol->deviceId][slaveId].maxInputRegisterAddr = address;
			}
			//if (address < contiguousDataTables_[slaveId].minInputRegisterAddr)
			//{
			//	contiguousDataTables_[slaveId].minInputRegisterAddr = address;
			//}
			if (address < deviceContiguousDataTables_[protocol->deviceId][slaveId].minInputRegisterAddr)
			{
				deviceContiguousDataTables_[protocol->deviceId][slaveId].minInputRegisterAddr = address;
			}
			break;
		case eHoldingRegisters: 
			//if (address > contiguousDataTables_[slaveId].maxRegisterAddr)
			//{
			//	contiguousDataTables_[slaveId].maxRegistersValueType = valueType;
			//	contiguousDataTables_[slaveId].maxRegisterAddr = address;
			//}
			if (address > deviceContiguousDataTables_[protocol->deviceId][slaveId].maxRegisterAddr)
			{
				deviceContiguousDataTables_[protocol->deviceId][slaveId].maxRegistersValueType = valueType;
				deviceContiguousDataTables_[protocol->deviceId][slaveId].maxRegisterAddr = address;
			}
			//if (address < contiguousDataTables_[slaveId].minRegisterAddr)
			//{
			//	contiguousDataTables_[slaveId].minRegisterAddr = address;
			//}
			if (address < deviceContiguousDataTables_[protocol->deviceId][slaveId].minRegisterAddr)
			{
				deviceContiguousDataTables_[protocol->deviceId][slaveId].minRegisterAddr = address;
			}

			break;
		default:
			break;
		}
		
		return protocol;
	}

	void CDriverEMSBoxModbus_build202528::logIEMSPacket(std::string& deviceID, std::vector<uint8_t>& send_data, std::vector<uint8_t>& recv_data)
	{
		if (send_data.size() > 0)
		{
			cb_->onLog(ELogLevel::eLogPacket, UTILS::toIEMSPacketMessage(deviceID, "send", bytesToHexString(send_data.data(), send_data.size())));
		}
		if (send_data.size() > 0)
		{
			cb_->onLog(ELogLevel::eLogPacket, UTILS::toIEMSPacketMessage(deviceID, "recv", bytesToHexString(recv_data.data(), recv_data.size())));
		}
	}

	void CDriverEMSBoxModbus_build202528::modbusLog(std::string str)
	{
		cb_->onLog(ELogLevel::eLogPacket, str);
	}

	void CDriverEMSBoxModbus_build202528::controlLedThread()
	{
		int light = 1;
		while (!exitLedControl_)
		{
			if (cb_)
			{
				cb_->onChannelIEMSLedBrightness(ledName, light);
			}
			if (light == 1)
				light = 0;
			else if (light == 0)
				light = 1;
			std::this_thread::sleep_for(std::chrono::milliseconds(500));
		}

		if (cb_)
		{
			cb_->onChannelIEMSLedBrightness(ledName, 0);
		}
	}

	CDriverEMSBoxModbus_build202528::CDriverEMSBoxModbus_build202528() :
		flag_(EFlag::eFlagNone),
		cb_(nullptr),
		modbusClient_(nullptr),
		mbct_(EModBusConnectType::MBCT_Unknow),
		errCount_(0),
		timeoutMs_(2000),
		isFirstOpen_(true)
	{
	}

	CDriverEMSBoxModbus_build202528::~CDriverEMSBoxModbus_build202528()
	{
		exitLedControl_ = true;
		if (ledControlThread_)
		{
			if(ledControlThread_->joinable())
			{
				ledControlThread_->join();
			}
			delete ledControlThread_;
			ledControlThread_ = nullptr;
		}
		if (cb_)
		{
			cb_->onChannelIEMSLedBrightness(ledName, 0);
		}	
	}

	bool CDriverEMSBoxModbus_build202528::open(const SProtocolNode& pn)
	{
		cb_->onLog(eLogInfo, "open0");

		SDriverEMSBoxModbus_build202528ChannelProtocol* cp = static_cast<SDriverEMSBoxModbus_build202528ChannelProtocol*>(pn.protocol);
		modbusClient_ = dynamic_cast<COMM::ICommModbus*>(COMM::CFactory::getInstance().produce("CommModbus"));
		if (cp->serverIp.empty())
		{
			modbusClient_->setup(cp->deviceName, cp->baudRate, cp->dataBit, cp->parity, cp->stopBit);
			mbct_ = EModBusConnectType::MBCT_RTU;
		}
		else
		{
			modbusClient_->setup(cp->serverIp, cp->serverPort);
			mbct_ = EModBusConnectType::MBCT_TCP;
		}

		modbusClient_->setLogCallBack(cb_);
		//modbusClient_->setResponseTimeout(5, 0);
		timeoutMs_ = cp->timeoutMs <= 0 ? timeoutMs_ : cp->timeoutMs;
		// modbusClient_->setResponseTimeout(timeoutMs_ / 1000, timeoutMs_ % 1000 * 1000);
		bool ret = modbusClient_->connect();
		if (!ret) 
		{ 
			cb_->onLog(eLogError, modbusClient_->getLastError()); 
			return ret;
		}

		if (isFirstOpen_)
		{
			isFirstOpen_ = false;

			//SProtocolNode& root = const_cast<SProtocolNode&>(pn);
			//std::sort(root.sub.begin(), root.sub.end(), [](const SProtocolNode& pna, const SProtocolNode& pnb) {
			//	SDriverEMSBoxModbus_build202528PointProtocol* ppa = static_cast<SDriverEMSBoxModbus_build202528PointProtocol*>(pna.protocol);
			//	SDriverEMSBoxModbus_build202528PointProtocol* ppb = static_cast<SDriverEMSBoxModbus_build202528PointProtocol*>(pnb.protocol);
			//	return ppa->mixAddress > ppb->mixAddress;
			//	});

			for (auto& points : devicePointsMap_)
			{
				std::sort(points.second.begin(), points.second.end(), [](const SDriverEMSBoxModbus_build202528PointProtocol* pna, const SDriverEMSBoxModbus_build202528PointProtocol* pnb) {
					return pna->mixAddress > pnb->mixAddress;
					});
			}

			for (auto& contiguousDataTables : deviceContiguousDataTables_)
			{
				for (auto& iter : contiguousDataTables.second)
				{
					std::string valueType = iter.second.maxInputRegistersValueType;
					if (valueType.length() > 2)
					{
						if (valueType[valueType.length() - 2] - '3' >= 0)
						{
							iter.second.maxInputRegisterAddr += 1;
						}
					}

					valueType = iter.second.maxRegistersValueType;
					if (valueType.length() > 2)
					{
						if (valueType[valueType.length() - 2] - '3' >= 0)
						{
							iter.second.maxRegisterAddr += 1;
						}
					}

					if (iter.second.maxBitAddr >= 0)
					{
						iter.second.tableBits.resize(iter.second.maxBitAddr + 1 + 1, 0);
					}

					if (iter.second.maxInputBitAddr >= 0)
					{
						iter.second.tableInputBits.resize(iter.second.maxInputBitAddr + 1 + 1, 0);
					}

					if (iter.second.maxRegisterAddr >= 0)
					{
						iter.second.tableRegisters.resize(iter.second.maxRegisterAddr + 1 + 1, 0);
					}

					if (iter.second.maxInputRegisterAddr >= 0)
					{
						iter.second.tableInputRegisters.resize(iter.second.maxInputRegisterAddr + 1 + 1, 0);
					}

					iter.second.scaddr = new SContiguousAddr(iter.second.minBitAddr, iter.second.minInputBitAddr,
						iter.second.minInputRegisterAddr, iter.second.minRegisterAddr,
						iter.second.maxBitAddr, iter.second.maxInputBitAddr,
						iter.second.maxInputRegisterAddr, iter.second.maxRegisterAddr);
				}
			}

			for (auto deviceIsCon : deviceIsContiguous_)
			{
				if (!deviceIsCon.second.isContiguousAddress)
				{
					auto& pVec = devicePointsMap_[deviceIsCon.first];
					for (auto& pp : pVec)
					{
						auto& contiguousDataTable = deviceContiguousDataTables_[deviceIsCon.first][pp->slaveId];
						switch (pp->addrType)
						{
						case EModbusAddrType::eInputCoils:
							contiguousDataTable.tableInputBits[pp->address] = 1;
							break;
						case EModbusAddrType::eOutputCoils:
							contiguousDataTable.tableBits[pp->address] = 1;
							break;
						case EModbusAddrType::eHoldingRegisters:
							contiguousDataTable.tableRegisters[pp->address] = 1;
							if (pp->valueType.length() > 2)
							{
								if (pp->valueType[pp->valueType.length() - 2] - '3' >= 0)
								{
									contiguousDataTable.tableRegisters[pp->address + 1] = 1;
								}
							}
							break;
						case EModbusAddrType::eInternalRegisters:
							contiguousDataTable.tableInputRegisters[pp->address] = 1;
							if (pp->valueType.length() > 2)
							{
								if (pp->valueType[pp->valueType.length() - 2] - '3' >= 0)
								{
									contiguousDataTable.tableInputRegisters[pp->address + 1] = 1;
								}
							}
							break;
						default:
							break;
						}
					}
					for (auto& contiguousDataTablesIter : deviceContiguousDataTables_[deviceIsCon.first])
					{
						auto& nonContiguousDataIndexTable = deviceNonContiguousDataIndexTables_[deviceIsCon.first][contiguousDataTablesIter.first];
						// bits
						int tableBitsSize = contiguousDataTablesIter.second.tableBits.size();
						for (int low = contiguousDataTablesIter.second.minBitAddr, high = low; high < tableBitsSize; )
						{
							if (contiguousDataTablesIter.second.tableBits[high])
							{
								++high;
							}
							else
							{
								if (low < high)
								{
									SNonContiguousDataIndexTable::SSegment segment;
									segment.minAddr = low;
									segment.maxAddr = high - 1;
									nonContiguousDataIndexTable.bits.emplace_back(std::move(segment));
								}

								low = ++high;
							}
						}

						// input bits
						int tableInputBitsSize = contiguousDataTablesIter.second.tableInputBits.size();
						for (int low = contiguousDataTablesIter.second.minInputBitAddr, high = low; high < tableInputBitsSize; )
						{
							if (contiguousDataTablesIter.second.tableInputBits[high])
							{
								++high;
							}
							else
							{
								if (low < high)
								{
									SNonContiguousDataIndexTable::SSegment segment;
									segment.minAddr = low;
									segment.maxAddr = high - 1;
									nonContiguousDataIndexTable.inputBits.emplace_back(std::move(segment));
								}

								low = ++high;
							}
						}

						// registers
						int tableRegistersSize = contiguousDataTablesIter.second.tableRegisters.size();
						for (int low = contiguousDataTablesIter.second.minRegisterAddr, high = low; high < tableRegistersSize; )
						{
							if (contiguousDataTablesIter.second.tableRegisters[high])
							{
								++high;
							}
							else
							{
								if (low < high)
								{
									SNonContiguousDataIndexTable::SSegment segment;
									segment.minAddr = low;
									segment.maxAddr = high - 1;
									nonContiguousDataIndexTable.registers.emplace_back(std::move(segment));
								}

								low = ++high;
							}
						}

						// input registers
						int tableInputRegistersSize = contiguousDataTablesIter.second.tableInputRegisters.size();
						for (int low = contiguousDataTablesIter.second.minInputRegisterAddr, high = low; high < tableInputRegistersSize; )
						{
							if (contiguousDataTablesIter.second.tableInputRegisters[high])
							{
								++high;
							}
							else
							{
								if (low < high)
								{
									SNonContiguousDataIndexTable::SSegment segment;
									segment.minAddr = low;
									segment.maxAddr = high - 1;
									nonContiguousDataIndexTable.inputRegisters.emplace_back(std::move(segment));
								}

								low = ++high;
							}
						}
					}
				}
			}
		}

		deviceIter_ = deviceIsContiguous_.begin();
		errCount_ = 0;
		cb_->onLog(eLogInfo, "open1");
		//cb_->onStatus(1);

		exitLedControl_ = false;
		if (!ledControlThread_)
		{
			ledControlThread_ = new std::thread(&CDriverEMSBoxModbus_build202528::controlLedThread, this);
		}

		return ret;
	}

	bool CDriverEMSBoxModbus_build202528::close(const SProtocolNode& pn)
	{
		cb_->onLog(eLogInfo, "close0");
		if (modbusClient_)
		{
			modbusClient_->disconnect();
			COMM::CFactory::getInstance().destory(modbusClient_);
			modbusClient_ = nullptr;
		}

		exitLedControl_ = true;
		if (ledControlThread_)
		{
			if(ledControlThread_->joinable())
			{
				ledControlThread_->join();
			}
			delete ledControlThread_;
			ledControlThread_ = nullptr;
		}

		//contiguousDataTables_.clear();
		//nonContiguousDataIndexTables_.clear();

		if (cb_)
		{
			cb_->onChannelIEMSLedBrightness(ledName, 0);
		}
		cb_->onLog(eLogInfo, "close1");
		return true;
	}

	EStatusCode CDriverEMSBoxModbus_build202528::control(const IProtocol* const channelProtocol, const IProtocol* const deviceProtocol, const IProtocol* const pointProtocol, const SControlInfo& controlInfo)
	{
		const auto& value = controlInfo.value;
		std::vector<uint16_t> data;
		EStatusCode rc = EStatusCode::eStatusFail;
		const SDriverEMSBoxModbus_build202528PointProtocol* pp = static_cast<const SDriverEMSBoxModbus_build202528PointProtocol*>(pointProtocol);
		modbusClient_->setSlaveId(pp->slaveId);
		switch (pp->addrType)
		{
		case EModbusAddrType::eOutputCoils:
			rc = modbusClient_->writeBit(pp->address, value.asBool()) < 0 ? EStatusCode::eStatusFail : EStatusCode::eStatusSuccess;
			break;
		case EModbusAddrType::eHoldingRegisters:
			if (pp->valueType == "Boolean")
			{
				modbusClient_->readRegisters(pp->address, 1, data);
				uint16_t regValue = UTILS::arr2var<uint32_t, uint16_t>((const uint8_t*)data.data(), pp->swapMode);
				if (!UTILS::setBit(regValue, pp->offset, value.asBool()))
					return EStatusCode::eStatusFail;
				if (modbusClient_->writeRegister(pp->address, regValue) != 1)
					return EStatusCode::eStatusFail;
				else
					rc = EStatusCode::eStatusSuccess;
				//modbusClient_->readRegisters(pp->address, 1, data);
				//uint16_t regValue = UTILS::arr2var<uint32_t, uint16_t>((const uint8_t*)data.data(), pp->swapMode);
				//if (!UTILS::setBits(regValue, pp->offsets, bitValue))
				//{
				//	break;
				//}

				//if (modbusClient_->writeRegister(pp->address, svalue.ui32) != 1)
				//{
				//}
				//else
				//{
				//	rc = eStatusSuccess;
				//}
			}
			else
			{
				auto convertFuncIter = SValue2ArrayTable.find(pp->valueType.c_str());
				if (convertFuncIter != SValue2ArrayTable.end())
				{
					convertFuncIter->second(value, data, pp->swapMode);
					switch (mbct_)
					{
					case EModBusConnectType::MBCT_TCP:
					{
						if (modbusClient_->writeRegisters(pp->address, data) == data.size())
						{
							rc = eStatusSuccess;
						}
					}
					break;
					case EModBusConnectType::MBCT_RTU:
					{
						if (data.size() == 1)
						{
							if (modbusClient_->writeRegister(pp->address, data[0]) == data.size())
							{
								rc = eStatusSuccess;
							}
						}
						else
						{
							if (modbusClient_->writeRegisters(pp->address, data) == data.size())
							{
								rc = eStatusSuccess;
							}
						}
					}
					break;
					default:
						break;
					}
				}
			}
			break;
		default:
			break;
		}
		if (rc != eStatusSuccess) cb_->onLog(eLogError, modbusClient_->getLastError());
		return rc;
	}

	EStatusCode CDriverEMSBoxModbus_build202528::controlSet(const std::unordered_map<std::string, std::vector<SControlSetInfo>>& controlValues)
	{
		return EStatusCode();
	}

	void CDriverEMSBoxModbus_build202528::setCallback(IDriverCallback* cb)
	{
		cb_ = cb;
	}

	EStatusCode CDriverEMSBoxModbus_build202528::poll(const SProtocolNode& pn)
	{
		if (errCount_ > 10)
		{
			cb_->onLog(ELogLevel::eLogError, "Continuous error count > 10, reopen");
			return EStatusCode::eStatusNeedReopen;
		}
		EStatusCode rsc = EStatusCode::eStatusSuccess;
		if (deviceIsContiguous_.empty())
			return rsc;
		if (deviceIter_ == deviceIsContiguous_.end())
			deviceIter_ = deviceIsContiguous_.begin();
		if (deviceIter_ == deviceIsContiguous_.end())
			return rsc;

		std::string deviceId = deviceIter_->first;
		SIEMSDeviceArg config = deviceIter_->second;
		if (deviceIter_->second.isContiguousAddress)
		{
			rsc = requestContiguousData(deviceId);
		}
		else
		{
			rsc = requestNonContiguousData(deviceId);
		}
		deviceIter_++;

		if (rsc != EStatusCode::eStatusSuccess)
			return rsc;
		
		for (auto& pp : devicePointsMap_[deviceId])
		{
			if (flag_ == EFlag::eFlagInterrupt)	return rsc;
			SData data;
			if (parseData(pp, deviceId, config, data.value))
			{
				cb_->onData(pp->key, data);
			}
		}

		return rsc;
	}

	std::string CDriverEMSBoxModbus_build202528::getPointKey(const IProtocol* const channelProtocol, const IProtocol* const deviceProtocol, const IProtocol* const pointProtocol)
	{
		const SDriverEMSBoxModbus_build202528PointProtocol* pp = static_cast<const SDriverEMSBoxModbus_build202528PointProtocol*>(pointProtocol);
		return pp->key;                                                                                                                                          
	}

	IProtocol* CDriverEMSBoxModbus_build202528::createProtocol(EProtocolType type, OriginProtocol& originProtocol)
	{
		switch (type)
		{
		case EProtocolType::eProtocolLink:
		{
			SDriverEMSBoxModbus_build202528ChannelProtocol* protocol = new SDriverEMSBoxModbus_build202528ChannelProtocol;

			//tcp
			protocol->serverIp = originProtocol["ServerIp"];
			protocol->serverPort = UTILS::str2Number<int>(originProtocol["ServerPort"]);
			if (originProtocol["ServerPort"].empty())
			{
				protocol->serverPort = 502;
			}

			//rtu
			if (protocol->serverIp.empty())
			{
				std::string deviceNameRaw = originProtocol["DeviceName"];

#if defined(_MSC_VER) || defined(__MINGW32__)
				if (deviceNameRaw.length() < 4)
				{
					return nullptr;
				}
				int portNum = UTILS::str2Number<int>(deviceNameRaw.substr(3, deviceNameRaw.size()));
				if (portNum > 9)
				{
					protocol->deviceName.append("\\\\.\\");
				}
#endif // WIN
				ledName = deviceNameRaw;
				protocol->deviceName.append(deviceNameRaw);								//The device argument specifies the name of the serial port handled by the OS, eg. "/dev/ttyS0" or "/dev/ttyUSB0". On Windows, it��s necessary to prepend COM name with "\\.\" for COM number greater than 9, eg. "\\\\.\\COM10". See http://msdn.microsoft.com/en-us/library/aa365247(v=vs.85).aspx for details
				protocol->baudRate = UTILS::str2Number<int>(originProtocol["BaudRate"]);	//The baud argument specifies the baud rate of the communication, eg. 9600, 19200, 57600, 115200, etc.
				protocol->parity = originProtocol["Parity"][0];									//The parity argument can have one of the following values : N for none   E for even  O for odd
				protocol->dataBit = UTILS::str2Number<int>(originProtocol["DataBit"]);		//The data_bits argument specifies the number of bits of data, the allowed values are 5, 6, 7 and 8.
				protocol->stopBit = UTILS::str2Number<int>(originProtocol["StopBit"]);		//The stop_bits argument specifies the bits of stop, the allowed values are 1 and 2.
			}

			// generic

			if (!originProtocol["TimeoutMs"].empty())
			{
				protocol->timeoutMs = atoi(originProtocol["TimeoutMs"].c_str());
			}

			/*protocol->maxClients = atoi(originProtocol["MaxClients"].c_str());
			if (protocol->maxClients < 1 || protocol->quantityPerRequest > 10 || originProtocol["MaxClients"].empty())
			{
				protocol->maxClients = 1;
			}*/
			
			return protocol;
		}
		case EProtocolType::eProtocolDevice:
		{
			return nullptr;
		}
		case EProtocolType::eProtocolPoint:
		{
			return createPointProtocol(originProtocol);
		}
		default:
			break;
		}
		return nullptr;
	}

	void CDriverEMSBoxModbus_build202528::destoryProtocol(EProtocolType type, IProtocol* protocol)
	{
		if (protocol)
		{
			delete protocol;
		}
	}

	void CDriverEMSBoxModbus_build202528::setFlag(EFlag flag)
	{
		flag_ = flag;
	}

} //namespace DRIVER end