<template>
  <div class="container">
    <div class="forward-box">
      <!-- 使用v-if="false"暂时隐藏MQTT卡片，但保留代码 -->
      <el-card v-if="false" class="box-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <div class="title-switch">
              <span>MQTT</span>
              <el-switch v-model="mqttForm.enable" class="header-switch" />
            </div>
            <div class="btns">
              <el-button type="primary" :loading="mqttLoading" @click="saveMQTT" :disabled="mqttForm.enable">保存</el-button>
              <el-button @click="resetMQTT" :disabled="mqttForm.enable">重置</el-button>
            </div>
          </div>
        </template>
        <el-form ref="mqttFormRef" :model="mqttForm" label-width="120px">
          <el-form-item label="连接状态">
            <div class="status-box">
              <span class="status" :class="{ 'connected': mqttForm.enable }">
                <el-icon>
                  <component :is="mqttForm.enable ? 'SuccessFilled' : 'CircleCloseFilled'" />
                </el-icon>
                <span>{{ mqttForm.enable ? '已连接' : '未连接' }}</span>
              </span>
            </div>
          </el-form-item>
          <el-form-item label="服务器地址">
            <el-input v-model="mqttForm.serverAddress" :disabled="mqttForm.enable" style="width: 300px" />
          </el-form-item>
          <el-form-item label="端口">
            <el-input-number v-model="mqttForm.port" :disabled="mqttForm.enable" :min="1" :max="65535" style="width: 120px" />
          </el-form-item>
          <el-form-item label="用户名">
            <el-input v-model="mqttForm.username" :disabled="mqttForm.enable" style="width: 300px" />
          </el-form-item>
          <el-form-item label="密码">
            <el-input v-model="mqttForm.password" :disabled="mqttForm.enable" type="password" style="width: 300px" show-password />
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <div class="title-switch">
              <span>OPC UA Server</span>
              <el-switch v-model="opcForm.enable" class="header-switch" @change="handleOpcEnableChange" />
            </div>
            <div class="btns">
              <!-- <el-button type="primary" :loading="opcLoading" @click="saveOPC" :disabled="opcForm.enable">保存</el-button> -->
              <!-- <el-button @click="resetOPC" :disabled="opcForm.enable">重置</el-button> -->
            </div>
          </div>
        </template>
        <el-form ref="opcFormRef" :model="opcForm" label-width="120px">
          <el-form-item label="连接状态">
            <div class="status-box">
              <span class="status" :class="{ 'connected': opcConnected }">
                <el-icon>
                  <component :is="opcConnected ? 'SuccessFilled' : 'CircleCloseFilled'" />
                </el-icon>
                <span>{{ opcConnected ? '已开启' : '未开启' }}</span>
              </span>
            </div>
          </el-form-item>
          <el-form-item label="服务器地址">
            <el-input v-model="opcForm.serverAddress" :disabled="opcForm.enable" style="width: 300px" />
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts" name="ConfigForward">
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
import { ElMessage } from "element-plus";
import { SuccessFilled, CircleCloseFilled } from "@element-plus/icons-vue";
import type { FormInstance } from "element-plus/es/components/form";
import { getConfigOPCServerState, setConfigOPCServerState } from "@/api/modules/configuration";

// MQTT表单
const mqttFormRef = ref<FormInstance>();
const mqttLoading = ref(false);
const mqttForm = reactive({
  enable: false,
  serverAddress: '',
  port: 1883,
  username: '',
  password: ''
});

// OPC表单
const opcFormRef = ref<FormInstance>();
const opcLoading = ref(false);
const opcForm = reactive({
  enable: false,
  serverAddress: ''
});

// OPC服务器连接状态
const opcConnected = ref(false);

// OPC服务器endpoint
const opcServerEndpoint = ref('');

// OPC服务器状态轮询定时器
const opcStateTimer = ref<NodeJS.Timer | null>(null);

// 获取OPC服务器状态
const fetchOPCServerState = async () => {
  try {
    const res = await getConfigOPCServerState();
    console.log('OPC服务器状态:', res);
    if (res.result && res.result.resultCode === "0") {
      // 更新连接状态和开关状态
      opcConnected.value = res.data.state;
      opcForm.enable = res.data.state;
      // 保存endpoint
      opcServerEndpoint.value = res.data.endpoint;
      // 设置服务器地址
      opcForm.serverAddress = res.data.endpoint;
    }
  } catch (error) {
    console.error('获取OPC服务器状态失败:', error);
  }
};

// 监听开关状态变化
const handleOpcEnableChange = async (value: boolean) => {
  try {
    // 调用设置接口
    const res = await setConfigOPCServerState({
      state: value,
      endpoint: value ? opcForm.serverAddress : ''
    });
    
    if (res.result && res.result.resultCode === "0") {
      if (value && opcServerEndpoint.value) {
        opcForm.serverAddress = opcServerEndpoint.value;
      }
    } else {
      // 如果设置失败，回滚开关状态
      opcForm.enable = !value;
      ElMessage.error('设置OPC服务器状态失败');
    }
  } catch (error) {
    // 如果发生错误，回滚开关状态
    opcForm.enable = !value;
    console.error('设置OPC服务器状态失败:', error);
    ElMessage.error('设置OPC服务器状态失败');
  }
};

// 开始OPC服务器状态轮询
const startOPCStatePolling = () => {
  // 先停止之前的轮询（如果有）
  stopOPCStatePolling();
  
  // 立即执行一次
  fetchOPCServerState();
  
  // 设置定时器，每1秒执行一次
  opcStateTimer.value = setInterval(fetchOPCServerState, 1000);
};

// 停止OPC服务器状态轮询
const stopOPCStatePolling = () => {
  if (opcStateTimer.value) {
    clearInterval(opcStateTimer.value);
    opcStateTimer.value = null;
  }
};

// MQTT相关方法
const saveMQTT = async () => {
  mqttLoading.value = true;
  try {
    // TODO: 调用保存MQTT配置的API
    ElMessage.success('MQTT配置保存成功');
    mqttForm.enable = true; // 保存成功后启用连接
  } catch (error) {
    console.error(error);
    ElMessage.error('MQTT配置保存失败');
  } finally {
    mqttLoading.value = false;
  }
};

const resetMQTT = () => {
  mqttFormRef.value?.resetFields();
};

// OPC相关方法
const saveOPC = async () => {
  opcLoading.value = true;
  try {
    // TODO: 调用保存OPC配置的API
    ElMessage.success('OPC配置保存成功');
    opcForm.enable = true; // 保存成功后启用连接
  } catch (error) {
    console.error(error);
    ElMessage.error('OPC配置保存失败');
  } finally {
    opcLoading.value = false;
  }
};

const resetOPC = () => {
  opcFormRef.value?.resetFields();
};

// 组件挂载时
onMounted(() => {
  // 开始OPC服务器状态轮询
  startOPCStatePolling();
});

// 组件卸载前
onBeforeUnmount(() => {
  // 停止OPC服务器状态轮询
  stopOPCStatePolling();
});
</script>

<style scoped lang="scss">
.container {
  width: 100%;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
  
  .forward-box {
    width: 750px;
    margin: 0 auto;

    .box-card {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      :deep(.el-card__header) {
        background-color: #f7f7f7;
      }

      .card-header {
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title-switch {
          display: flex;
          align-items: center;
          gap: 20px;
        }
      }

      .btns {
        display: flex;
        gap: 20px;
      }

      .status-box {
        .status {
          display: inline-flex;
          align-items: center;
          gap: 4px;
          color: #f56c6c;

          &.connected {
            color: #67c23a;
          }
        }
      }
    }
  }
}
</style> 
