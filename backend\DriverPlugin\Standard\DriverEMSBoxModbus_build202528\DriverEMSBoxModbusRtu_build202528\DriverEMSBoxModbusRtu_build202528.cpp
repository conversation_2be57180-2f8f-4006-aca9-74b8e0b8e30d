#include "DriverEMSBoxModbusRtu_build202528.h"

namespace DRIVER
{
	CDriverEMSBoxModbusRtu_build202528::CDriverEMSBoxModbusRtu_build202528()
	{
	}

	CDriverEMSBoxModbusRtu_build202528::~CDriverEMSBoxModbusRtu_build202528()
	{
	}
}

extern "C"
{
	DRIVER_API DRIVER::IDriver* createDriver()
	{
		return new DRIVER::CDriverEMSBoxModbusRtu_build202528;
	}

	DRIVER_API void destoryDriver(DRIVER::IDriver* p)
	{
		if (p) { delete p; }
	}
}