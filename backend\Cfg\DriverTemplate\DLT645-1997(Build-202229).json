{"name": "DLT645-1997(Build-202229)", "type": "empty", "category": "电力行业通信标准", "categoryNumber": 12, "systemClass": 1, "scriptType": "c++", "information": "多功能电能表通信规约", "2DModel": [], "3DModel": [], "version": 1, "iemsProtocolClass": "dlt645", "iemsProtocolInterfaceType": "rs485", "argument": [{"name": "Address", "label": "设备号", "description": "设备号", "required": true, "inputType": "input", "type": "string", "defaultValue": "", "_level": 2}, {"name": "Code", "label": "编码", "description": "编码", "required": true, "inputType": "select", "type": "number", "_level": 4, "items": [{"text": "正向有功总电能", "value": 36880}, {"text": "反向有功总电能", "value": 36896}, {"text": "A相电压", "value": 46609}, {"text": "B相电压", "value": 46610}, {"text": "C相电压", "value": 46611}, {"text": "AB线电压", "value": 46737}, {"text": "BC线电压", "value": 46738}, {"text": "CA线电压", "value": 46739}, {"text": "A相电流", "value": 46625}, {"text": "B相电流", "value": 46626}, {"text": "C相电流", "value": 46627}, {"text": "总有功功率", "value": 46640}, {"text": "A相有功功率", "value": 46641}, {"text": "B相有功功率", "value": 46642}, {"text": "C相有功功率", "value": 46643}, {"text": "总无功功率", "value": 46656}, {"text": "A相无功功率", "value": 46657}, {"text": "B相无功功率", "value": 46658}, {"text": "C相无功功率", "value": 46659}, {"text": "总视在功率", "value": 46688}, {"text": "A相视在功率", "value": 46689}, {"text": "B相视在功率", "value": 46690}, {"text": "C相视在功率", "value": 46691}], "defaultValue": ""}, {"name": "__unuse", "label": "__unuse", "description": "__unuse", "required": false, "inputType": "input", "type": "string", "defaultValue": "", "_level": 3}], "attribute": [{"code": "_online", "name": "在线状态", "type": "DI", "value": {"ON": "在线", "OFF": "离线"}, "history": {"change": false}, "pointType": "internalState", "description": "设备在线状态"}], "classType": "link", "connection": [{"name": "DeviceName", "label": "串口", "description": "串口地址", "required": true, "inputType": "select", "type": "string", "defaultValue": "/dev/ttymxc6", "visible": true, "_level": 1, "useIEMSCom": true, "items": [{"text": "COM1", "value": "/dev/ttymxc6"}, {"text": "COM2", "value": "/dev/ttymxc7"}, {"text": "COM3", "value": "/dev/ttymxc1"}, {"text": "COM4", "value": "/dev/ttymxc3"}]}, {"name": "BaudRate", "label": "波特率", "description": "波特率", "required": true, "type": "number", "visible": true, "min": 110, "max": 921600, "defaultValue": 9600, "inputType": "select", "_level": 1, "items": [{"text": "110", "value": 110}, {"text": "300", "value": 300}, {"text": "600", "value": 600}, {"text": "1200", "value": 1200}, {"text": "2400", "value": 2400}, {"text": "4800", "value": 4800}, {"text": "9600", "value": 9600}, {"text": "14400", "value": 14400}, {"text": "19200", "value": 19200}, {"text": "38400", "value": 38400}, {"text": "57600", "value": 57600}, {"text": "115200", "value": 115200}, {"text": "230400", "value": 230400}, {"text": "380400", "value": 380400}, {"text": "460800", "value": 460800}, {"text": "921600", "value": 921600}]}, {"name": "Parity", "label": "校验位", "description": "奇偶校验位", "required": true, "type": "string", "visible": true, "defaultValue": "N", "inputType": "select", "_level": 1, "items": [{"text": "None", "value": "N"}, {"text": "Odd", "value": "O"}, {"text": "Even", "value": "E"}, {"text": "<PERSON>", "value": "M"}, {"text": "Space", "value": "S"}]}, {"name": "DataBit", "label": "数据位", "description": "数据位", "required": true, "type": "number", "visible": true, "min": 5, "max": 8, "defaultValue": 8, "inputType": "select", "_level": 1, "items": [{"text": "5", "value": 5}, {"text": "6", "value": 6}, {"text": "7", "value": 7}, {"text": "8", "value": 8}]}, {"name": "StopBit", "label": "停止位", "description": "停止位", "required": true, "type": "number", "visible": true, "min": 1, "max": 2, "defaultValue": 1, "inputType": "select", "_level": 1, "items": [{"text": "1", "value": 1}, {"text": "1.5", "value": 1.5}, {"text": "2", "value": 2}]}, {"name": "Version", "label": "版本", "description": "版本", "type": "string", "inputType": "input", "defaultValue": "1997", "_level": 1, "visible": false}], "driverFile": {"Linux ARMv7": "DriverEMSBoxDLT645_build202528"}, "description": "多功能电表通信协议(DLT645-1997)", "creationTime": 1612493004344, "systemVersion": "0.0.1", "modificationTime": 1612511289523, "argumentAscription": "point"}