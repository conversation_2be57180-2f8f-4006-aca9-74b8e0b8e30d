import router from "@/routers/index";
import { isType } from "@/utils/util";
import { LOGIN_URL } from "@/config/config";
import { ElNotification } from "element-plus";
import { getNetWorkConfigList } from "@/api/modules/netport";
import { getBasicInfo } from "@/api/modules/basic"
import { GlobalStore } from "@/stores";
import { AuthStore } from "@/stores/modules/auth";

// 引入 views 文件夹下所有 vue 文件
const modules = import.meta.glob("@/views/**/*.vue");

/**
 * 初始化动态路由
 */
export const initDynamicRouter = async () => {
	const authStore = AuthStore();
	const globalStore = GlobalStore();
	try {
		// 1.获取菜单列表 && 按钮权限（可合并到一个接口获取，根据后端不同可自行改造）
		await authStore.getAuthMenuList();
		// await authStore.getAuthButtonList();
		const basicInfo:any = await getBasicInfo();
		globalStore.setBasicInfo(basicInfo.data)
		// 判断网络配置是否生效
		const { data } = await getNetWorkConfigList();
		authStore.setEnableChange(data.enableChange);
		const defaultList = [...authStore.authMenuListGet]
		let isfind = defaultList.find((item: any)=>item.name == "netport");
		// let isfindData = defaultList.find((item: any)=>item.name == "realData");
		// if(isfindData && basicInfo && basicInfo.data && basicInfo.data.env === "iEMS-COM4"){
		// 	isfindData!.meta.isHide = true
		// } 
		// else{
		// 	isfindData!.meta.isHide = false
		// }
		if (data.enableChange) {
			isfind!.meta.isHide = false;
		}
		else {
			isfind!.meta.isHide = true;
		}
		authStore.setAuthMenuList(defaultList);


		// 2.判断当前用户有没有菜单权限
		if (!authStore.authMenuListGet.length) {
			ElNotification({
				title: "无权限访问",
				message: "当前账号无任何菜单权限，请联系系统管理员！",
				type: "warning",
				duration: 3000
			});
			globalStore.setToken("");
			router.replace(LOGIN_URL);
			return Promise.reject("No permission");
		}

		// 3.添加动态路由
		authStore.flatMenuListGet.forEach((item: any) => {
			item.children && delete item.children;
			if (item.component && isType(item.component) == "string") {
				item.component = modules["/src/views" + item.component + ".vue"];
			}
			if (item.meta.isFull) {
				router.addRoute(item);
			} else {
				router.addRoute("layout", item);
			}
		});
	} catch (error) {
		// 💢 当按钮 || 菜单请求出错时，重定向到登陆页
		globalStore.setToken("");
		router.replace(LOGIN_URL);
		return Promise.reject(error);
	}
};
