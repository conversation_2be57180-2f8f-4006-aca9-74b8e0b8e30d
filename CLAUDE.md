# CLAUDE.md

这个文件为Claude Code (claude.ai/code) 提供在此代码库中工作的指导。

## 项目概述
工业物联网网关系统（DriverX），通过统一接口连接各种自动化设备和软件应用，支持多种物联网平台（阿里云IoT、中控IoT等）。系统设计具有高稳定性，通过协议驱动插件支持二次开发。

## 架构

### 核心组件
- **DriverX** (`backend/DriverX/`)：主网关引擎，管理设备通信
- **DriverEngine** (`backend/DriverX/DriverEngine.*`)：驱动管理和数据处理核心
- **Gateway** (`backend/DriverX/Gateway.*`)：数据路由的网关抽象层
- **Communicate** (`backend/Communicate/`)：通信协议（TCP、UDP、串口、MQTT、Modbus）
- **SystemSetting** (`backend/SystemSetting/`)：系统配置和设置管理
- **OPCUAServer** (`backend/OPCUAServer/`)：OPC UA服务器实现

### 插件架构
- **DriverPlugin** (`backend/DriverPlugin/`)：协议驱动插件
  - 标准驱动在 `Standard/` 子目录
  - 自定义驱动在 `Custom/` 子目录
- **IDriver.h**：所有协议驱动的基础接口

### 前端
- **Vue.js管理界面** (`frontend/config/`)：主要的基于Web的管理界面
- **边缘配置** (`frontend/edgeConfig/` & `frontend/edgeConfig2.0/`)：设备配置工具

## 构建系统

### 构建命令
```bash
# Linux x64
./backend/build_linux64.sh

# ARM32
./backend/build_arm32.sh

# ARM64
./backend/build_arm64.sh

# UOS64
./backend/build_uos64.sh

# Windows x86
./build_win_x86.bat
```

### 打包创建
```bash
# 交互式选择
./packge.sh

# 直接平台选择
./packge.sh linux64 software
./packge.sh arm64 hardware
./packge.sh arm32 software
```

### 配置文件
- **驱动配置**：`backend/Cfg/DriverXConfig.json`
- **平台映射**：`backend/Cfg/DriverTemplate/`（协议特定配置）
- **Modbus映射**：`backend/Cfg/ModbusMappingTable.json`

## 开发工作流

### Git分支策略
- **develop**：开发环境稳定分支
- **pre-release**：测试环境稳定分支
- **master**：生产环境稳定分支
- **feature-{name}-{task}**：功能开发分支
- **fixbug-{name}-{bug}**：bug修复分支

### 构建流程
1. **配置**：基于CMake，使用平台特定工具链
2. **依赖**：预构建库在 `backend/Library/` 和 `backend/OpenSource/`
3. **前端构建**：Vue.js构建集成到包创建中
4. **打包**：创建平台特定命名的tar归档

## 关键接口

### 驱动插件接口
- **IDriver.h**：协议实现的基驱动接口
- **ICommunicate.h**：通信抽象层
- **IOutTo.h**：物联网平台的数据输出接口

### 数据结构
- **SValue**：支持int、real、string、boolean、JSON的通用数据类型系统
- **ChannelDataSet**：来自设备的结构化数据收集
- **GatewayConfig**：网关实例的配置映射

## 测试
- **单元测试**：前端基于Jest，后端集成测试
- **配置测试文件**：位于 `backend/Config4Test/`，支持各种协议
- **驱动测试**：`backend/DriverX/DriverTest.*`

## 平台支持
- **Linux x64**：主要开发平台
- **ARM32/ARM64**：嵌入式设备支持
- **UOS64**：统信UOS支持
- **Windows**：通过Visual Studio支持x86

## 目录结构
```
edge/
├── backend/              # C++后端源码
│   ├── DriverX/          # 主网关引擎
│   ├── Communicate/      # 通信协议
│   ├── DriverPlugin/     # 协议驱动
│   ├── SystemSetting/    # 配置管理
│   └── Library/          # 第三方依赖
├── frontend/             # Web界面
│   ├── config/           # 主管理界面（Vue.js）
│   └── edgeConfig/       # 设备配置工具
├── doc/                  # 文档
└── deprecated/           # 遗留代码
```

## 环境设置
- **CMake**：需要版本3.10+
- **C++**：C++17标准
- **Node.js**：前端构建所需
- **交叉编译**：ARM工具链在 `toolchain_*.cmake`