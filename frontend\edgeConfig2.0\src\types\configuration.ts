// 协议模板类型
export interface ProtocolTemplate {
  id: string;
  name: string;
  type: string;
  channel?: ChannelParam[];
  device?: DeviceParam[];
  point?: PointParam[];
}

// 通道参数类型
export interface ChannelParam {
  label: string;
  name: string;
  defaultValue?: string;
  value?: string;
  level?: number;
}

// 设备参数类型
export interface DeviceParam {
  label: string;
  name: string;
  defaultValue?: string;
  value?: string;
  level?: number;
}

// 点位参数类型
export interface PointParam {
  label: string;
  name: string;
  defaultValue?: string;
  value?: string;
  level?: number;
}

// 协议节点类型
export interface ProtocolNode {
  id: string;
  name: string;
  type: 'prototype' | 'instance' | 'device';
  children: ProtocolNode[];
  parentId?: string;
  protocol?: any[];
  points?: Point[];
}

// 点位类型
export interface Point {
  id: string;
  code: string;
  name: string;
  description?: string;
  address: string;
  type: string;
  pointType?: string;
  protocol?: any[];
}

// 组态模板类型
export interface ConfigTemplate {
  id: string;
  name: string;
  link: {
    [key: string]: {
      instances: Instance[];
    };
  };
}

// 实例类型
export interface Instance {
  id: string;
  name: string;
  devices: Device[];
}

// 设备类型
export interface Device {
  id: string;
  name: string;
  points: Point[];
}

// 右键菜单状态类型
export interface ContextMenuState {
  visible: boolean;
  x: number;
  y: number;
  data?: any;
  node?: any;
}

// 对话框状态类型
export interface DialogState {
  visible: boolean;
  title?: string;
}

// 表单类型
export interface PrototypeForm {
  type: string;
}

export interface InstanceForm {
  id: string;
  name: string;
  instanceName: string;
  parentId: string;
  prototypeName?: string;
  channelParams?: ChannelParam[];
}

export interface DeviceForm {
  id: string;
  name: string;
  instanceName: string;
  parentId: string;
  protocol?: any[];
  deviceParams?: DeviceParam[];
}

export interface PointForm {
  id: string;
  code: string;
  name: string;
  description: string;
  address: string;
  type: string;
  pointType?: string;
  protocol?: any[];
}

// 编辑模板表单类型
export interface EditTemplateForm {
  id: string;
  name: string;
}

// 模板设备表单类型
export interface TemplateDeviceForm {
  name: string;
  parentId: string;
  instanceName: string;
  templateId: string;
  deviceParams: DeviceParam[];
}

// 协议类型选项类型
export interface ProtocolTypeOption {
  label: string;
  value: string;
}

// 创建模板表单类型
export interface CreateTemplateForm {
  name: string;
}

// 编辑实例表单类型
export interface EditInstanceForm extends InstanceForm {
  prototypeName: string;
} 