@echo off
setlocal enabledelayedexpansion

REM 检查参数数量
if "%~3"=="" (
    echo Usage: %0 ^<name^> ^<path^> ^<env^>
    exit /b 1
)

REM 设置变量
set "str1=%~1"
set "str2=%~2"
set "str3=%~3"

REM 获取当前时间
for /f "tokens=2 delims==" %%I in ('wmic os get localdatetime /value') do set datetime=%%I
set "current_time=!datetime:~0,4!-!datetime:~4,2!-!datetime:~6,2! !datetime:~8,2!:!datetime:~10,2!:!datetime:~12,2!"

REM 使用 PowerShell 创建 JSON 内容
set "output_path=!str2!packageInfo"
powershell -Command "$json = @{createTime='!current_time!'; name='!str1!'; env='!str3!'} | ConvertTo-Json; $json | Out-File -FilePath '!output_path!' -Encoding UTF8"

REM 输出成功信息
echo JSON data written to !output_path!

endlocal
