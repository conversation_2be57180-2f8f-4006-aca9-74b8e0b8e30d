﻿#include "GatewayAIOT.h"


#include <stdio.h>
#include <stdlib.h>
#include <iostream>
#include <fstream>
#include <string>

#ifdef WIN32
#include "windows.h"
#else
#include <dirent.h>
#include <unistd.h>
#include <algorithm>
#include <sys/stat.h>
#define MAX_PATH 260
#endif

#include <unordered_map>

#include "zip.h"
#include <iostream>
#include <fstream>
#include <regex>

#include "json/json.h"
#include "curl/curl.h"
#include "yyjson.h"
#include "tinyxml2.h"

#include "Utils/String.hpp"
#include "Utils/Utils.hpp"
#include "ModbusTcpServer.hpp"
#include "PathManager.h"
#include "Utils/EMSBOXTool.h"
#include "IEMSSetting/iEMSSetting.h"

namespace GATEWAY
{
	static bool StringToJsonValue(const std::string& body, Json::Value &root)
	{
		JSONCPP_STRING err;
		Json::CharReaderBuilder builder;
		std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
		if (!reader->parse(body.c_str(), body.c_str() + body.size(), &root, &err))
		{
			return false;
		}
		return true;
	}

	static bool getFileContent(const std::string& path, std::string& content)
	{
		if (path.empty())
			return false;
		std::ifstream ifs(path);
		if (!ifs.is_open())
		{
			return false;
		}
		ifs.seekg(0, ifs.end);
		int length = ifs.tellg();
		ifs.seekg(0, ifs.beg);

		char* preadd = new char[length + 1];
		memset(preadd, 0, length + 1);
		ifs.read(preadd, length);
		content = preadd;
		delete[] preadd;
		return true;
	}

#ifdef WIN32
	static bool getFilesContent(const std::string& path, std::vector<std::string>& filesContent)
	{
		_finddata_t fd;
		std::string tmpstr = path + "/*";
		auto lfd = _findfirst(tmpstr.c_str(), &fd);
		if (lfd != -1) {
			do {
				if (strcmp(fd.name, ".") == 0 || strcmp(fd.name, "..") == 0)
					continue;
				else// if (fd.attrib == 32 || fd.attrib == 0)
				{
					std::string tmpcontent;
					if (getFileContent(path + "/" + fd.name, tmpcontent)) {
						filesContent.emplace_back(std::move(tmpcontent));
					}
					else {
						_findclose(lfd);
						return false;
					}
				}
				/*else {
					continue;
				}*/
			} while (_findnext(lfd, &fd) == 0);
		}
		else {
			return false;
		}
		_findclose(lfd);
		return true;
	}

	static bool getFilesName(const std::string& path, std::vector<std::string>& filesName)
	{
		_finddata_t fd;
		std::string tmpstr = path + "/*";
		auto lfd = _findfirst(tmpstr.c_str(), &fd);
		if (lfd != -1) {
			do {
				if (strcmp(fd.name, ".") == 0 || strcmp(fd.name, "..") == 0)
					continue;
				else if (fd.attrib == 32 || fd.attrib == 0)
				{
					filesName.emplace_back(fd.name);
				}
				else {
					continue;
				}
			} while (_findnext(lfd, &fd) == 0);
		}
		else {
			return false;
		}
		_findclose(lfd);
		return true;
	}

	static bool getFilesInfo(const std::string& path, std::vector<SFileInfo>& filesInfo)
	{
		_finddata_t fd;
		std::string tmpstr = path + "/*";
		auto lfd = _findfirst(tmpstr.c_str(), &fd);
		if (lfd != -1) {
			do {
				if (strcmp(fd.name, ".") == 0 || strcmp(fd.name, "..") == 0)
					continue;
				else if (fd.attrib == 32 || fd.attrib == 0)
				{
					//printf("fd.name:%s, size:%lu, access:%lld, create:%lld, write:%lld.\n", fd.name, fd.size, fd.time_access, fd.time_create, fd.time_write);
					SFileInfo sfi;
					sfi.name = fd.name;
					sfi.modificationTime = fd.time_write;
					sfi.size = fd.size;
					filesInfo.emplace_back(sfi);
				}
				else {
					continue;
				}
			} while (_findnext(lfd, &fd) == 0);
		}
		else {
			return false;
		}
		_findclose(lfd);
		return true;
	}

	static std::string UtfToGbk(const char* utf8)
	{
		int len = MultiByteToWideChar(CP_UTF8, 0, utf8, -1, NULL, 0);
		wchar_t* wstr = new wchar_t[len + 1];
		memset(wstr, 0, len + 1);
		MultiByteToWideChar(CP_UTF8, 0, utf8, -1, wstr, len);
		len = WideCharToMultiByte(CP_ACP, 0, wstr, -1, NULL, 0, NULL, NULL);

		char* str = new char[len + 1];
		memset(str, 0, len + 1);
		WideCharToMultiByte(CP_ACP, 0, wstr, -1, str, len, NULL, NULL);
		if (wstr) delete[]wstr;

		std::string result = str;
		if (str) delete[]str;

		return result;
	}
#else
	static bool getFilesContent(const std::string& path, std::vector<std::string>& filesContent)
	{
		DIR* dir;
		struct dirent* ptr;
		if ((dir = opendir(path.c_str())) == NULL)
		{
			return false;
		}
		while ((ptr = readdir(dir)) != NULL)
		{
			if (strcmp(ptr->d_name, ".") == 0 || strcmp(ptr->d_name, "..") == 0)
				continue;
			else if (ptr->d_type == 8)
			{
				std::string tmpcontent;
				if (getFileContent(path + "/" + ptr->d_name, tmpcontent)) {
					filesContent.emplace_back(std::move(tmpcontent));
				}
				else {
					closedir(dir);
					return false;
				}
			}
			else
			{
				continue;
			}
		}
		closedir(dir);
		return true;
	}

	static bool getFilesName(const std::string& path, std::vector<std::string>& filesName)
	{
		DIR* dir;
		struct dirent* ptr;
		if ((dir = opendir(path.c_str())) == NULL)
		{
			return false;
		}
		while ((ptr = readdir(dir)) != NULL)
		{
			if (strcmp(ptr->d_name, ".") == 0 || strcmp(ptr->d_name, "..") == 0)
				continue;
			else if (ptr->d_type == 8)
			{
				filesName.emplace_back(ptr->d_name);
			}
			else
			{
				continue;
			}
		}
		closedir(dir);
		return true;
	}

	static bool getFilesInfo(const std::string& path, std::vector<SFileInfo>& filesInfo)
	{
		DIR* dir;
		struct dirent* ptr;
		if ((dir = opendir(path.c_str())) == NULL)
		{
			return false;
		}
		while ((ptr = readdir(dir)) != NULL)
		{
			if (strcmp(ptr->d_name, ".") == 0 || strcmp(ptr->d_name, "..") == 0)
				continue;
			else if (ptr->d_type == 8)
			{
				std::string file_path = path + "/" + ptr->d_name;
				struct stat buf;
				if (stat(file_path.c_str(), &buf) == 0)
				{
					SFileInfo sfi;
					sfi.name = ptr->d_name;
					sfi.modificationTime = buf.st_mtime;
					sfi.size = buf.st_size;
					filesInfo.emplace_back(sfi);
				}
			}
			else
			{
				continue;
			}
		}
		closedir(dir);
		return true;
	}
#endif

	//example 
	// linux
	//dir = "./hello/world/"
	//dir = "/mnt/hello/world/"
	// windows
	//dir = "./hello/world/"
	//dir = ".\\hello\\world\\"
	//dir = "D::\\hello\\world\\"
	static void clearFolder(std::string dir)
	{
#ifdef WIN32
		size_t pos = 0;
		while ((pos = dir.find("/", pos)) != std::string::npos)
		{
			dir.replace(pos, 1, "\\");
			++pos;
		}

		system(std::string("rd /s/q ").append(dir).c_str());
		system(std::string("mkdir ").append(dir).c_str());
#else
		system(std::string("rm -rf ").append(dir).append("*").c_str());
#endif
	}

	CGatewayAIOT::CGatewayAIOT() :
		timeDifference_(0),
		httpServerPort_(8899),
		httpClient_(nullptr),
		modbusTcpClient_(nullptr),
		mainThread_(nullptr),
		sendDataThread_(nullptr),
		reportEventThread_(nullptr),
		httpServerThread_(nullptr),
		reload_(false),
		blank_(true),
		enableLocalDataCaching_(false),
		localDataCachingCount_(-1),
		enableModbusServer_(false),
		systemSetting_(nullptr),
		enableCloudControl_(true),
		uploadInfoThread_(nullptr),
		lc_(nullptr),
		enableNetWorkChange_(false),
		uploadInfoNow_(false),
		uploadManageThread_(nullptr),
		killSelf_(true),
		regularlyReportInterval_(0),
		recordReportData_(false),
		processStartTime_(time(0)),
		totalLinkCount_(0),
		totalDeviceCount_(0),
		innerPointCount_(0),
		normalPointCount_(0),
		logFileSize_(10),
		logBaseSize_(1024 * 1024),
		logFileCount_(5),
		logLevel_(0),
		env_(EGatewayEnv::Normal),
		updateDataToOpcuaServerThread_(nullptr)
	{
		lc_ = CLogCaching::get_instance();

#ifdef WIN32
		killSelf_ = false;
#else
		killSelf_ = true;
#endif // WIN32

	}

	CGatewayAIOT::~CGatewayAIOT()
	{
		if (env_ == EGatewayEnv::iEMSCOM4)
		{
			localIEMSLedExitControl_ = true;
			if (localIEMSLedThread_)
			{
				if (localIEMSLedThread_->joinable())
					localIEMSLedThread_->join();
				delete localIEMSLedThread_;
				localIEMSLedThread_ = nullptr;
			}
			CiEMSSetting::getInstance().controlRunningStateLed(EIEMSRunningStateLed::Run, 0);
			CiEMSSetting::getInstance().controlRunningStateLed(EIEMSRunningStateLed::Err, 0);
		}
	}

	void CGatewayAIOT::loadGatewayConfig()
	{
		//std::string configFilePath = CONFIG_PATH;
		//if (isMultiGatewayEnv)
		//{
		//	configFilePath = GENERATE_MULTI_GATEWAY_FILE_PATH(launchPath, driverXId, "AIOTConfig.json");
		//}
		std::string configFilePath = CPathManager::getInstance().getAIOTConfigPath();
		std::ifstream ifs(configFilePath);
		if (ifs.is_open())
		{
			JSONCPP_STRING err;
			Json::Value root;
			Json::CharReaderBuilder builder;
			if (Json::parseFromStream(builder, ifs, &root, &err))
			{
				mainPlatform_.clientId = root["clientID"].asString();
				mainPlatform_.mqttUrl = root["mqttURL"].asString();
				mainPlatform_.mqttUrl = mainPlatform_.mqttUrl.substr(mainPlatform_.mqttUrl.rfind("/") + 1, mainPlatform_.mqttUrl.length());
				mainPlatform_.mqttUsername = root["username"].asString();
				mainPlatform_.mqttPassword = root["password"].asString();
				mainPlatform_.resourceServer = root["resoureIP"].asString();
				versionUri_ = API_VERSION + mainPlatform_.clientId;
				configUri_ = API_DOWNLOAD_CONFIG + mainPlatform_.clientId;
				cloudTimeUri_ = API_CLOUD_TIME;

				auto& address = root["address"];
				if (!address.empty() && address.isArray())
				{
					for (int i = 0; i < address.size(); i++)
					{
						SDataDistribution dd;
						dd.mqttUrl = address[i]["mqttURL"].asString();
						dd.mqttUrl = dd.mqttUrl.substr(dd.mqttUrl.rfind("/") + 1, dd.mqttUrl.length());
						dd.mqttUsername = address[i]["username"].asString();
						dd.mqttPassword = address[i]["password"].asString();
						dd.clientId = address[i]["clientID"].asString();
						dd.resourceServer = address[i]["resoureIP"].asString();
						dataDistributionVec_.emplace_back(std::move(dd));
					}
				}
			}
			ifs.close();
		}
	}

	bool CGatewayAIOT::loadIEMSCfg()
	{
		std::string body;
		if (!getFileContent(HARDWARE_IEMS_CFG_ENV, body))
		{
			std::cout << "if (!getFileContent(HARDWARE_IEMS_CFG_ENV, body))" << std::endl;
			CiEMSSetting::getInstance().controlRunningStateLed(EIEMSRunningStateLed::Err, 1);
			return false;
		}
		Json::Value root;
		if (!StringToJsonValue(body, root))
		{
			std::cout << "if (!StringToJsonValue(body, root))" << std::endl;
			CiEMSSetting::getInstance().controlRunningStateLed(EIEMSRunningStateLed::Err, 1);
			return false;
		}

		CiEMSSetting::getInstance().initialize(root);

		iemsConfig_.product = root["product"].asString();
		iemsConfig_.dev_module = root["dev_module"].asString();
		iemsConfig_.register_max_num = root["register_max_num"].asUInt64();
		
		if (root.isMember("com_link_param") && root["com_link_param"].type() == Json::arrayValue)
		{
			for (auto& com : root["com_link_param"])
			{
				SIEMSCOMInfo ci;
				if (iemsStringToTransferInterfaceMap.find(com["type"].asString()) !=
					iemsStringToTransferInterfaceMap.end())
				{
					ci.type = iemsStringToTransferInterfaceMap[com["type"].asString()];
				}
				else
				{
					continue;
				}
				ci.com_name = com["com_name"].asString();
				ci.tty_channel = com["tty_channel"].asString();
				ci.led = com["led"].asString();

				iemsConfig_.com_link_param[ci.com_name] = ci;
			}
		}

		if (root.isMember("slave_max_limit") && root["slave_max_limit"].type() == Json::objectValue)
		{
			iemsConfig_.slave_max_limit.rs232 = root["slave_max_limit"]["rs232"].asUInt64();
			iemsConfig_.slave_max_limit.rs485 = root["slave_max_limit"]["rs485"].asUInt64();
			iemsConfig_.slave_max_limit.tcp = root["slave_max_limit"]["tcp"].asUInt64();
		}

		if (root.isMember("protocol_down") && root["protocol_down"].type() == Json::objectValue)
		{
			for (auto proIter = root["protocol_down"].begin(); proIter != root["protocol_down"].end(); proIter++)
			{
				std::string protocolName = proIter.name();
				SIEMSProtocolDown pd;
				pd.enable = root["protocol_down"][protocolName]["enable"].asBool();
				for (auto& proType : root["protocol_down"][protocolName]["type"])
				{
					if (iemsStringToTransferInterfaceMap.find(proType.asString()) !=
						iemsStringToTransferInterfaceMap.end())
					{
						pd.type[iemsStringToTransferInterfaceMap[proType.asString()]] = true;
					}
					iemsConfig_.protocol_down[protocolName] = pd;
				}
			}
		}

		if (localIEMSLedThread_ == nullptr)
		{
			localIEMSLedExitControl_ = false;
			localIEMSLedThread_ = new std::thread(&CGatewayAIOT::localIEMSLedControl, this);
		}
		//std::cout << iemsConfig_.toString() << std::endl;
	}

	void CGatewayAIOT::loadLocalDriverTemplate()
	{
		std::string path = CPathManager::getInstance().getLocalDriverTemplatePath();
		
		// 判断路径是否存在
		bool pathExists = false;
	#ifdef WIN32
		// Windows系统使用GetFileAttributesA检查路径
		DWORD fileAttributes = GetFileAttributesA(path.c_str());
		pathExists = (fileAttributes != INVALID_FILE_ATTRIBUTES && 
					 (fileAttributes & FILE_ATTRIBUTE_DIRECTORY));
	#else
		// Linux系统使用已有的getFilesName函数间接检查路径
		std::vector<std::string> dummy;
		pathExists = getFilesName(path, dummy);
	#endif

		if (!pathExists) {
			logger_->error("Local driver template path does not exist: {0}", path);
			return;
		}
		
		// 获取所有文件名
		std::vector<std::string> allFiles;
		if (!getFilesName(path, allFiles)) 
		{
			logger_->error("Failed to get files from path: {0}", path);
			return;
		}
		
		// 过滤出.json结尾的文件
		std::vector<std::string> templateFiles;
		for (const auto& file : allFiles) 
		{
			if (UTILS::endsWith(file, ".json")) 
			{
				templateFiles.push_back(file);
			}
		}
		// 依次读取每个.json文件的内容并解析
		for (const auto& fileName : templateFiles) 
		{
			// 构建完整的文件路径
			std::string filePath = path + "/" + fileName;
			
			// 读取文件内容
			std::string fileContent;
			if (!getFileContent(filePath, fileContent)) 
			{
				logger_->error("Failed to read file content: {0}", filePath);
				continue;
			}
			
			// 解析JSON内容
			Json::Value root;
			if (!StringToJsonValue(fileContent, root)) 
			{
				logger_->error("Failed to parse JSON from file: {0}", filePath);
				continue;
			}
			
			// 处理解析后的JSON对象
			// TODO: 根据需求处理jsonRoot对象
			SClassLink  scl;
			for (auto driverFileIter = root["driverFile"].begin(); driverFileIter != root["driverFile"].end(); driverFileIter++)
			{
				scl.driverFile[driverFileIter.name()] = driverFileIter->asString();
			}
			if (root.isMember("scriptType") && root["scriptType"].isString())
			{
				scl.scriptType = root["scriptType"].asString();
			}
			if (root.isMember("javascriptExpresstion") && root["javascriptExpresstion"].isString())
			{
				scl.javascriptExpresstion = root["javascriptExpresstion"].asString();
			}
			if (root.isMember("iemsProtocolClass") && root["iemsProtocolClass"].isString())
			{
				scl.iemsProtocolClass = root["iemsProtocolClass"].asString();
			}
			if (root.isMember("iemsProtocolInterfaceType") && root["iemsProtocolInterfaceType"].isString())
			{
				scl.iemsProtocolInterfaceType = root["iemsProtocolInterfaceType"].asString();
			}
			const auto& arguments = root["argument"];
			scl.argumentStr = arguments.toStyledString();
			for (auto ag : arguments)
			{
				SClassLink::SArgument argument;
				argument.visible = true;
				argument.required = true;
				argument.name = ag["name"].asString();
				argument.label = ag["label"].asString();
				argument.description = ag["description"].asString();
				argument.type = ag["type"].asString();
				argument.defaultValue = ag["defaultValue"].asString();
				argument.inputType = ag["inputType"].asString();
				if (ag.isMember("items"))
					argument.items = ag["items"];
				argument.option = ag["option"].asInt();
				if (ag.isMember("visible"))
					argument.visible = ag["visible"].asBool();
				if (ag.isMember("required"))
					argument.required = ag["required"].asBool();
				argument.level = SClassLink::SArgumentLevle(ag["_level"].asInt());
				scl.arguments.emplace_back(std::move(argument));
			}
			const auto& connectionArgs = root["connection"];
			scl.connectionStr = connectionArgs.toStyledString();
			for (auto connectionArg : connectionArgs)
			{
				SClassLink::SConnectionArg argument;
				argument.visible = true;
				argument.required = true;
				argument.useIEMSCom = false;
				argument.name = connectionArg["name"].asString();
				argument.label = connectionArg["label"].asString();
				argument.description = connectionArg["description"].asString();
				argument.type = connectionArg["type"].asString();
				argument.defaultValue = connectionArg["defaultValue"].asString();
				argument.inputType = connectionArg["inputType"].asString();
				if (connectionArg.isMember("items"))
					argument.items = connectionArg["items"];
				argument.option = connectionArg["option"].asInt();
				if (connectionArg.isMember("visible"))
					argument.visible = connectionArg["visible"].asBool();
				if (connectionArg.isMember("required"))
					argument.required = connectionArg["required"].asBool();
				if (connectionArg.isMember("useIEMSCom"))
					argument.useIEMSCom = connectionArg["useIEMSCom"].asBool();
				argument.level = SClassLink::SArgumentLevle(connectionArg["_level"].asInt());
				scl.connection.emplace_back(std::move(argument));
			}
			scl.classId = root["classID"].asString();
			scl.creationTime = root["creationTime"].asLargestUInt();
			scl.modificationTime = root["modificationTime"].asLargestUInt();
			scl.name = root["name"].asString();
			const auto& attributes = root["attribute"];
			for (int i = 0; i < attributes.size(); i++)
			{
				SAttribute attribute;
				attribute.code = attributes[i]["code"].asString();
				attribute.name = attributes[i]["name"].asString();
				attribute.description = attributes[i]["description"].asString();
				attribute.pointType = attributes[i]["pointType"].asString();
				attribute.type = attributes[i]["type"].asString();
				for (auto valueAttrIter = attributes[i]["value"].begin(); valueAttrIter != attributes[i]["value"].end(); valueAttrIter++)
				{
					attribute.value[valueAttrIter.name()] = valueAttrIter->asString();
				}
				scl.attributes.emplace_back(std::move(attribute));
			}
			localLinkTemplates_[scl.name] = scl;
		}
		//std::cout << localLinkTemplates_.size() << std::endl;
	}

	void CGatewayAIOT::loadManageConfig()
	{
		std::ifstream ifs(MANAGE_CONFIG_PATH);
		if (ifs.is_open())
		{
			JSONCPP_STRING err;
			Json::Value root;
			Json::CharReaderBuilder builder;
			if (Json::parseFromStream(builder, ifs, &root, &err))
			{
				std::string serialNumber;
				getSerialNumber(serialNumber);
				if (!serialNumber.empty())
				{
					gmrc_.isReport = root["enable"].asBool();
					gmrc_.interval = root["interval"].asInt();
					gmrc_.alarmTopic = serialNumber + "/alarm";
					gmrc_.infoTopic = serialNumber + "/info";
					gmrc_.cpuAlarm = root["cpuAlarm"].asInt();
					gmrc_.cpuAlarmSamplingInterval = root["cpuAlarmSamplingInterval"].asInt();
					gmrc_.cpuAlarmPercent = root["cpuAlarmPercent"].asInt();
					gmrc_.memoryAlarm = root["memoryAlarm"].asInt();
					gmrc_.diskAlarm = root["diskAlarm"].asInt();
					gmrc_.managePlatform.clientId = "edge_" + serialNumber;
					gmrc_.managePlatform.mqttUrl = root["mqttURL"].asString();
					gmrc_.managePlatform.mqttUsername = root["username"].asString();
					gmrc_.managePlatform.mqttPassword = root["password"].asString();
					gmrc_.girc.basic = root["uploadInfo"]["reportBasic"].asBool();
					gmrc_.girc.runtime = root["uploadInfo"]["reportRuntime"].asBool();
					gmrc_.girc.interval = root["uploadInfo"]["reportInterval"].asInt();
				}
			}
		}

		logger_->info(std::string("manageReport:") + (gmrc_.isReport ? "true" : "false"));
		if (gmrc_.isReport)
		{
			logger_->info("manageReport interval:{0}, alarmTopic:{1}, infoTopic:{2}, cpuAlarm:{3}, cpuAlarmSamplingInterval:{4}, cpuAlarmPercent:{5}, memoryAlarm:{6}, diskAlarm:{7}", gmrc_.interval, gmrc_.alarmTopic, gmrc_.infoTopic, gmrc_.cpuAlarm, gmrc_.cpuAlarmSamplingInterval, gmrc_.cpuAlarmPercent, gmrc_.memoryAlarm, gmrc_.diskAlarm);
			logger_->info("manageUploadInfo basic:{0}, runtime:{1}, interval:{2}", gmrc_.girc.basic, gmrc_.girc.runtime, gmrc_.girc.interval);
		}
	}

	template<typename T>
	int CGatewayAIOT::writeDriverXConfig(const std::string& key, const T& value)
	{
		std::unique_lock<std::mutex> lock(driverXConfigMutex_);
		std::string DriverXConfigPath = CPathManager::getInstance().getDriverXConfigPath();
		//std::ifstream ifs("./DriverXConfig.json");
		std::ifstream ifs(DriverXConfigPath);
		if (!ifs.is_open())
		{
			logger_->error("failed to open DriverXConfig.json");
			return -1;
		}

		JSONCPP_STRING err;
		Json::Value root;
		Json::CharReaderBuilder builder;
		if (!Json::parseFromStream(builder, ifs, &root, &err))
		{
			logger_->error("failed to parse DriverXConfig.json, error={0}", err.c_str());
			ifs.close();
			return -1;
		}
		ifs.close();

		std::string gkey = "GatewayAIOT/" + getGatewayId();
		root[gkey][key] = value;
		//std::ofstream ofs("./DriverXConfig.json");
		std::ofstream ofs(DriverXConfigPath);
		if (ofs.is_open())
		{
			std::string data = root.toStyledString();
			ofs.write(data.c_str(), data.size());
			ofs.close();
		}
		return 0;
	}

	void CGatewayAIOT::loadModbusMappingTable(ModbusMappingTable& mmt, const std::string& file)
	{
		mmt.clear();

		std::ifstream ifs;
		ifs.open(file);
		if (!ifs.is_open())
		{
			return;
		}

		JSONCPP_STRING err;
		Json::Value root;
		Json::CharReaderBuilder builder;
		if (!Json::parseFromStream(builder, ifs, &root, &err))
		{
			return;
		}
		ifs.close();

		for (auto linkIter = root.begin(); linkIter != root.end(); ++linkIter)
		{
			for (auto deviceIter = linkIter->begin(); deviceIter != linkIter->end(); ++deviceIter)
			{
				SModbusParam smp;
				for (auto pointIter = deviceIter->begin(); pointIter != deviceIter->end(); ++pointIter)
				{
					smp.addr = (*pointIter)["Addr"].asInt();
					smp.pointType = (*pointIter)["PointType"].asString();
					smp.dataType = (*pointIter)["DataType"].asString();

					std::string pointKey = linkIter.name() + "." + deviceIter.name() + "." + pointIter.name();
					if (smp.dataType == "bool")
					{
						modbusCoilAddrPointKeyMappingTable_[smp.addr] = pointKey;
					}
					else
					{
						modbusRegisterAddrPointKeyMappingTable_[smp.addr] = pointKey + "." + smp.dataType;
					}

					mmt[linkIter.name()][deviceIter.name()][pointIter.name()] = smp;
				}
			}
		}
	}

	void CGatewayAIOT::saveModbusMappingTable(const ModbusMappingTable& mmt, const std::string& file)
	{
		Json::Value root;
		for (const auto& linkIter : mmt)
		{
			for (const auto& deviceIter : linkIter.second)
			{
				Json::Value j;
				for (const auto& pointIter : deviceIter.second)
				{
					j[pointIter.first]["Addr"] = pointIter.second.addr;
					j[pointIter.first]["PointType"] = pointIter.second.pointType;
					j[pointIter.first]["DataType"] = pointIter.second.dataType;
				}
				root[linkIter.first][deviceIter.first] = std::move(j);
			}
		}

		std::ofstream ofs(file);
		if (ofs.is_open())
		{
			std::string data = root.toStyledString();
			ofs.write(data.c_str(), data.size());
			ofs.close();
		}
	}

	int CGatewayAIOT::getModbusAddr(ModbusMappingTable& mmt, const std::string& channelName, const std::string& deviceName, const std::string& pointName)
	{
		int rc = -1;
		auto li = mmt.find(channelName);
		if (li != mmt.end())
		{
			auto di = li->second.find(deviceName);
			if (di != li->second.end())
			{
				auto pi = di->second.find(pointName);
				if (pi != di->second.end()) rc = pi->second.addr;
			}
		}
		return rc;
	}

	void CGatewayAIOT::onWriteModbusRequest(std::vector<uint8_t>& req)
	{
		int offset = 0;
		int slave = req[offset];
		int function = req[offset + 1];
		uint16_t address = (req[offset + 2] << 8) + req[offset + 3];
		DRIVER::SCtrlInfo sci;
		sci.async = false;
		sci.type = DRIVER::SCtrlInfo::eWrite;
		switch (function) {
		case MODBUS_FC_WRITE_SINGLE_COIL: {
			int data = (req[offset + 4] << 8) + req[offset + 5];
			auto findIter = modbusCoilAddrPointKeyMappingTable_.find(address);
			if (findIter != modbusCoilAddrPointKeyMappingTable_.end())
			{
				std::vector<std::string> strVec = UTILS::splitString(findIter->second, ".");
				if (strVec.size() == 3)
				{
					sci.channelName = strVec[0];
					sci.deviceName = strVec[1];
					sci.pointName = strVec[2];
				}
			}
			sci.value = data ? true : false;
			std::vector<DRIVER::SCtrlInfo> scis{ sci };
			bool b = driverEngine_->control(scis);
		}
										break;
		case MODBUS_FC_WRITE_SINGLE_REGISTER: {
			int data = (req[offset + 4] << 8) + req[offset + 5];
			auto findIter = modbusRegisterAddrPointKeyMappingTable_.find(address);
			if (findIter != modbusRegisterAddrPointKeyMappingTable_.end())
			{
				std::vector<std::string> strVec = UTILS::splitString(findIter->second, ".");
				if (strVec.size() == 4)
				{
					sci.channelName = strVec[0];
					sci.deviceName = strVec[1];
					sci.pointName = strVec[2];
				}
			}
			sci.value = data;
			std::vector<DRIVER::SCtrlInfo> scis{ sci };
			bool b = driverEngine_->control(scis);
		}
											break;
		case MODBUS_FC_WRITE_MULTIPLE_COILS: {
			int nb = (req[offset + 4] << 8) + req[offset + 5];
			int nb_bits = req[offset + 5];
			/* 6 = byte count */
			//modbus_set_bits_from_bytes(mb_mapping->tab_bits, mapping_address, nb, &req[offset + 6]);
		}
										   break;
		case MODBUS_FC_WRITE_MULTIPLE_REGISTERS: {
			int nb = (req[offset + 4] << 8) + req[offset + 5];
			int nb_bytes = req[offset + 6];
			int i, j;
			for (i = 0, j = 7; i < nb; i += 2, j += 4) {
				/* 7,8,9,10 = first value */

				auto findIter = modbusRegisterAddrPointKeyMappingTable_.find(address + i);
				if (findIter != modbusRegisterAddrPointKeyMappingTable_.end())
				{
					std::vector<std::string> strVec = UTILS::splitString(findIter->second, ".");
					if (strVec.size() == 4)
					{
						sci.channelName = strVec[0];
						sci.deviceName = strVec[1];
						sci.pointName = strVec[2];
						const std::string& dataType = strVec[3];
						if (dataType == "int32")
						{
							sci.value = *(int*)(req.data() + j);
						}
						else if (dataType == "uint32")
						{
							sci.value = *(uint32_t*)(req.data() + j);
						}
						else if (dataType == "float")
						{
							sci.value = *(float*)(req.data() + j);
						}
					}
					std::vector<DRIVER::SCtrlInfo> scis{ sci };
					bool b = driverEngine_->control(scis);
				}
			}
		}
											   break;
		default:
			break;
		}
	}

	void CGatewayAIOT::mainThread()
	{
		blank_ = false;
		if (!blank_)
		{
			//mainPlatform_.mqttClient->configure(mainPlatform_.clientId, mainPlatform_.mqttUrl, mainPlatform_.mqttUsername, mainPlatform_.mqttPassword, 
			//	std::string(CRT_PATH) + "ca.crt", std::string(CRT_PATH) + "client.crt", std::string(CRT_PATH) + "client.key");
			mainPlatform_.mqttClient->configure(mainPlatform_.clientId, mainPlatform_.mqttUrl, mainPlatform_.mqttUsername, mainPlatform_.mqttPassword,
				CPathManager::getInstance().getCrtPath() + "ca.crt", CPathManager::getInstance().getCrtPath() + "client.crt", CPathManager::getInstance().getCrtPath() + "client.key");
			mainPlatform_.mqttClient->connect(1000);

			{
				std::unique_lock<std::mutex> lock(mqttListMutex_);
				for (int i = 0; i < dataDistributionVec_.size(); i++)
				{
					auto& dd = dataDistributionVec_[i];
					//dd.mqttClient->configure(dd.clientId, dd.mqttUrl, dd.mqttUsername, dd.mqttPassword, 
					//	std::string(CRT_PATH) + "ca.crt", std::string(CRT_PATH) + "client.crt", std::string(CRT_PATH) + "client.key");
					dd.mqttClient->configure(dd.clientId, dd.mqttUrl, dd.mqttUsername, dd.mqttPassword,
						CPathManager::getInstance().getCrtPath() + "ca.crt", CPathManager::getInstance().getCrtPath() + "client.crt", CPathManager::getInstance().getCrtPath() + "client.key");
					dd.mqttClient->connect(1000);
				}
			}
		}

		// 连接运维平台
		if (gmrc_.isReport)
		{
			//gmrc_.managePlatform.mqttClient->configure(gmrc_.managePlatform.clientId, gmrc_.managePlatform.mqttUrl, gmrc_.managePlatform.mqttUsername, 
			//	gmrc_.managePlatform.mqttPassword, std::string(MANAGE_CRT_PATH) + "ca.crt", std::string(MANAGE_CRT_PATH) + "client.crt", std::string(MANAGE_CRT_PATH) + "client.key");
			gmrc_.managePlatform.mqttClient->configure(gmrc_.managePlatform.clientId, gmrc_.managePlatform.mqttUrl, gmrc_.managePlatform.mqttUsername, gmrc_.managePlatform.mqttPassword, 
				CPathManager::getInstance().getManagerCrtPath() + "ca.crt", CPathManager::getInstance().getManagerCrtPath() + "client.crt", CPathManager::getInstance().getManagerCrtPath() + "client.key");
			gmrc_.managePlatform.mqttClient->connect(1000);

			uploadManageThread_ = new std::thread(&CGatewayAIOT::uploadManageThread, this);
		}

		CModbusTcpSlave slave;
		if (enableModbusServer_)
		{
			slave.setDataCB(std::bind(&CGatewayAIOT::onWriteModbusRequest, this, std::placeholders::_1));
			if (slave.setup())
			{
				if (slave.start())
				{
					modbusTcpClient_->setup("127.0.0.1", 1502);
					modbusTcpClient_->setSlaveId(1);
					modbusTcpClient_->connect();
					modbusTcpClient_->writeBit(0xFFFF, true);
				}
			}
		}

		sog_.cloudConnectionStatus = false;
		sog_.cloudConfigGetStatus = false;

		auto cloudConfigure = [this]() -> bool {
			//std::string cloudConfigPath = CLOUD_CONFIG_PATH;
			//if (isMultiGatewayEnv)
			//{
			//	cloudConfigPath = GENERATE_MULTI_GATEWAY_FILE_PATH(launchPath, driverXId, "cloudConfig/");
			//}
			std::string cloudConfigPath = CPathManager::getInstance().getCloudConfigPath();
			//std::cout << u"下载配置   " << cloudConfigPath << std::endl;

			clearFolder(cloudConfigPath);
			if (downloadConfig("http://" + mainPlatform_.resourceServer + configUri_, mainPlatform_.clientId))
			{
				insertLog(eConfigAlteration, u8"下载配置成功");
				if (loadConfig(sog_, slcs_))
				{
					insertLog(eConfigAlteration, u8"加载配置成功");
					std::vector<std::string> topics;
					for (const auto& deviceIdIter : deviceIdVec_)
					{
						topics.emplace_back(deviceIdIter + "/" + TOPIC_TYPE_CONTROL);
					}
					for (const auto& iter : ChannelIdVec_) 
					{
						topics.emplace_back(iter + "/" + TOPIC_TYPE_CONTROL);
						topics.emplace_back(iter + "/" + TOPIC_TYPE_CONTROLSET);
					}
					if (mainPlatform_.mqttClient->getMqttConnectionStatus())
					{
						topics.emplace_back(TOPIC_TYPE_DATA_CALL_ALL);
						topics.emplace_back(TOPIC_TYPE_DATA_CALL_SGL + sog_.id);
						mainPlatform_.mqttClient->subscribe(topics);
					}
					{
						std::unique_lock<std::mutex> lock(mqttListMutex_);
						for (const auto& iter : dataDistributionVec_)
						{
							if (iter.mqttClient->getMqttConnectionStatus())
							{
								iter.mqttClient->subscribe(topics);
							}
						}
					}

					// get totalDeviceCount innerPointCount normalPointCount
					totalLinkCount_ = slcs_.size();
					for (auto it = slcs_.begin(); it != slcs_.end(); it++)
					{
						totalDeviceCount_ += it->second.deviceTable.size();

						for (auto dit = it->second.deviceTable.begin(); dit != it->second.deviceTable.end(); dit++)
						{
							innerPointCount_ += dit->second.innerPointTable.size();
							normalPointCount_ += dit->second.pointTable.size();
						}
					}

					for (auto it = slcs_.begin(); it != slcs_.end(); it++)
					{
						it->second.logSize = logFileSize_;
						it->second.logBaseSize = logBaseSize_;
						it->second.logCount = logFileCount_;
						it->second.logLevel = logLevel_;
					}

					//for (auto cit = slcs_.begin(); cit != slcs_.end(); cit++)
					//{
					//	std::string channelName = cit->first;
					//	for (auto dit = cit->second.deviceTable.begin(); dit != cit->second.deviceTable.end(); dit++)
					//	{
					//		std::string deviceName = dit->first;
					//		for (auto pit = dit->second.pointTable.begin(); pit != dit->second.pointTable.end(); pit++)
					//		{
					//			std::string pointName = pit->first;
					//			configurationDatas_[channelName][deviceName][pointName] = DRIVER::SData();
					//		}
					//	}
					//}

					if (isMultiGatewayEnv)
					{
						for (auto it = slcs_.begin(); it != slcs_.end(); it++)
						{
							it->second.multiGatewatConfig["LaunchPath"] = launchPath;
							it->second.multiGatewatConfig["DriverXId"] = driverXId;
						}
					}

					getLocalEnv();
					sog_.cloudConfigGetStatus = true;
					configure(slcs_);
					return true;
				}
				else
				{
					sog_.cloudConfigGetStatus = false;
					insertLog(eConfigAlteration, u8"加载配置失败");
				}
			}
			else 
			{
				insertLog(eConfigAlteration, u8"下载配置失败");
				sog_.cloudConfigGetStatus = false;
			}
			return false;
		};

		while (running_)
		{
			reload_ = false;
			driverEngine_->deleteAllDriver();

			while (running_ && blank_)
			{
				std::this_thread::sleep_for(std::chrono::seconds(2));
			}

			bool finished = false;
			do
			{
				finished = cloudConfigure();

				if (!finished)
				{
					std::this_thread::sleep_for(std::chrono::seconds(5));
				}
			} while (running_ && !finished && !reload_);

			sendDataThread_ = new std::thread(&CGatewayAIOT::sendDataThread, this);
			reportEventThread_ = new std::thread(&CGatewayAIOT::reportEventThread, this);
			uploadInfoThread_ = new std::thread(&CGatewayAIOT::uploadInfoThread, this);

			// work loop
			while (running_ && !reload_)
			{
				// memory leak (linux)
				/*int64_t td = 0;
				if (getTimeDifference(td))
				{
					timeDifference_ = td;
				}*/

				SCloudMessageSptr scms;
				{
					if (!reload_)
					{
						std::unique_lock<std::mutex> lock(cloudMessageQueueMutex_);
						if (cloudMessageQueue_.empty())
						{
							mainCondVar_.wait_for(lock, std::chrono::seconds(1));
						}
						else
						{
							scms = cloudMessageQueue_.front();
							cloudMessageQueue_.pop();
						}
					}
				}

				if (!scms)
				{
					continue;
				}

				if (scms->topic.rfind(TOPIC_TYPE_CONFIG) != std::string::npos)
				{
					if (killSelf_)
						_exit(0);
					reload_ = true;
					insertLog(eConfigAlteration, u8"配置变更");
				}
				else if (scms->topic.rfind(TOPIC_TYPE_CONTROL) != std::string::npos)
				{
					control(scms);
				}
				else if (scms->topic.rfind(TOPIC_TYPE_CONTROLSET) != std::string::npos)
				{
					controlSet(scms);
				}
				else if (scms->topic.find(TOPIC_TYPE_DATA_CALL_ALL) != std::string::npos)
				{
					sendAllData();
				}
				else if (scms->topic.find(TOPIC_TYPE_DATA_CALL_SGL) != std::string::npos)
				{
					sendAllData();
				}
			}


			// exit
			if (sendDataThread_)
			{
				if (sendDataThread_->joinable())
				{
					sendDataThread_->join();
				}
				else
				{
					sendDataThread_->detach();
				}
				delete sendDataThread_;
				sendDataThread_ = nullptr;
			}

			eventCondVar_.notify_all();
			if (reportEventThread_)
			{
				if (reportEventThread_->joinable())
				{
					reportEventThread_->join();
				}
				else
				{
					reportEventThread_->detach();
				}
				delete reportEventThread_;
				reportEventThread_ = nullptr;
			}

			if (uploadInfoThread_)
			{
				if (uploadInfoThread_->joinable())
				{
					uploadInfoThread_->join();
				}
				else
				{
					uploadInfoThread_->detach();
				}
				delete uploadInfoThread_;
				uploadInfoThread_ = nullptr;
			}
		}

		if (uploadManageThread_)
		{
			if (uploadManageThread_->joinable())
			{
				uploadManageThread_->join();
			}
			else
			{
				uploadManageThread_->detach();
			}
			delete uploadManageThread_;
			uploadManageThread_ = nullptr;
		}
	}

	void CGatewayAIOT::updateDataToOpcuaServerThread()
	{
		while (running_.load())
		{
			SDataSet dataSet;
			{
				std::unique_lock<std::mutex> lock(dataSetQueueMutex_);
				if (dataSetQueue_.empty())
				{
					if (opcServer_.isRunning() && !opcuaCacheMap_.empty())
					{
						for (auto it : opcuaCacheMap_)
						{
							std::string pointCode = it.first;
							DRIVER::SData data = it.second;
							sendDataToOpcuaServer(pointCode, data);
						}
						opcuaCacheMap_.clear();
					}

					dataSetQueueCondVar_.wait_for(lock, std::chrono::seconds(1));
				}
				else
				{
					dataSet = dataSetQueue_.front();
					dataSetQueue_.pop();
				}
			}
			if (!dataSet.channelName.empty())
			{
				std::string channelId = dataSet.channelName;
				auto channelIter = slcs_.find(channelId);
				if (channelIter != slcs_.end())
				{
					std::string channelName = channelIter->second.alias;
					for (auto dev : *dataSet.dataSetSptr)
					{
						std::string deviceId = dev.first;
						auto deviceIter = channelIter->second.deviceTable.find(deviceId);
						if (deviceIter != channelIter->second.deviceTable.end())
						{
							std::string deviceName = deviceIter->second.alias;
							for (auto poi : dev.second)
							{
								std::string pointId = poi.first;
								auto pointIter = deviceIter->second.pointTable.find(pointId);
								if (pointIter != deviceIter->second.pointTable.end())
								{
									std::string pointName = pointIter->second.alias;
									std::string pointCode;
									pointCode.append(channelName).append(".")
										.append(deviceName).append(".")
										.append(pointName);
									//std::cout << pointCode << std::endl;

									if (opcServer_.isRunning())
									{
										sendDataToOpcuaServer(pointCode, poi.second);
									}
									else
									{
										opcuaCacheMap_.insert(std::make_pair(pointCode, poi.second));
									}
								}
							}
						}
					}
				}
			}
		}

		//std::cout << __func__ << std::endl;
		return;
	}

	void CGatewayAIOT::sendDataToOpcuaServer(std::string& pointCode, DRIVER::SData& data)
	{
		switch (data.value.getType())
		{
		case SValue::eValueInt:
		{
			int64_t value = data.value.toInt<int64_t>();
			opcServer_.updateVariable(pointCode, SubData{ value });
		}
		break;
		case SValue::eValueUInt:
		{
			uint64_t value = data.value.toInt<uint64_t>();
			opcServer_.updateVariable(pointCode, SubData{ value });
		}
		break;
		case SValue::eValueReal:
		{
			double value = data.value.toFloat<double>();
			opcServer_.updateVariable(pointCode, SubData{ value });
		}
		break;
		case SValue::eValueString:
		{
			std::string value = data.value.toString();
			opcServer_.updateVariable(pointCode, SubData{ value });
		}
		break;
		case SValue::eValueBoolean:
		{
			bool value = data.value.toBool();
			opcServer_.updateVariable(pointCode, SubData{ value });
		}
		break;
		case SValue::eValueJson:
		{
			std::string value = data.value.toString();
			Json::Value jv;
			if (!StringToJsonValue(value, jv))
				break;
			opcServer_.updateVariable(pointCode, SubData{ value });
		}
		default: break;
		}
	}

	void CGatewayAIOT::opcuaServerWriteCallBack(const std::tuple<std::string, std::string, std::string>& pointInfo, const SubData& value, const UA_NodeId* nodeId)
	{
		//std::cout << std::endl << nodeId << std::endl;

		auto [channelName, deviceName, pointName] = pointInfo;
		std::cout << channelName << "   " << deviceName << "   " << pointName << std::endl;

		for (auto& channel : slcs_)
		{
			if (channel.second.alias == channelName)
			{
				for (auto& device : channel.second.deviceTable)
				{
					if (device.second.alias == deviceName)
					{
						for (auto& point : device.second.pointTable)
						{
							if (point.second.alias == pointName)
							{
								Json::Value root;
								root["linkid"] = channel.first;
								root["deviceid"] = device.first;
								root["code"] = point.first;
								root["type"] = "control";

								// 根据value的类型获取值
								if (std::holds_alternative<bool>(value.data)) {
									root["value"] = std::get<bool>(value.data);
								}
								else if (std::holds_alternative<int8_t>(value.data)) {
									root["value"] = std::get<int8_t>(value.data);
								}
								else if (std::holds_alternative<uint8_t>(value.data)) {
									root["value"] = std::get<uint8_t>(value.data);
								}
								else if (std::holds_alternative<int16_t>(value.data)) {
									root["value"] = std::get<int16_t>(value.data);
								}
								else if (std::holds_alternative<uint16_t>(value.data)) {
									root["value"] = std::get<uint16_t>(value.data);
								}
								else if (std::holds_alternative<int32_t>(value.data)) {
									root["value"] = std::get<int32_t>(value.data);
								}
								else if (std::holds_alternative<uint32_t>(value.data)) {
									root["value"] = std::get<uint32_t>(value.data);
								}
								else if (std::holds_alternative<int64_t>(value.data)) {
									root["value"] = static_cast<Json::Int64>(std::get<int64_t>(value.data));
								}
								else if (std::holds_alternative<uint64_t>(value.data)) {
									root["value"] = static_cast<Json::UInt64>(std::get<uint64_t>(value.data));
								}
								else if (std::holds_alternative<float>(value.data)) {
									root["value"] = std::get<float>(value.data);
								}
								else if (std::holds_alternative<double>(value.data)) {
									root["value"] = std::get<double>(value.data);
								}
								else if (std::holds_alternative<std::string>(value.data)) {
									root["value"] = std::get<std::string>(value.data);
								}
								else {
									// 处理monostate或其他未知类型
									root["value"] = Json::Value::null;
								}

								SCloudMessageSptr cloudMessageSptr(new SCloudMessage);
								cloudMessageSptr->payload = root.toStyledString();
								control(cloudMessageSptr);
							}
						}
					}
				}
			}
		}
	}

	void CGatewayAIOT::getUploadInfo(int64_t nowTime, bool basic, bool runtime, Json::Value& pkg)
	{
		pkg["timestamp"] = nowTime;
		if (girc_.basic)
		{
			std::string product = sog_.className;
			std::string serialNumber;
			getSerialNumber(serialNumber);
			std::string runVersion;
			getRunFirmwareInfo(runVersion);

			Json::Value basicInfo;
			basicInfo["product"] = product;
			basicInfo["serialNumber"] = serialNumber.empty() ? product : serialNumber;
			basicInfo["runVersion"] = runVersion;

			//Json::FastWriter writer;
			//pkg["state"]["basicInfo"] = writer.write(basicInfo);
			pkg["state"]["basicInfo"] = basicInfo;
			pkg["updatetime"]["basicInfo"] = nowTime;
			pkg["quality"]["basicInfo"] = Json::arrayValue;
		}
		if (girc_.runtime)
		{
			Json::Value runtimeInfo;

			//system
			SCPUInfo cif;
			systemSetting_->getCPUInfo(cif);
			SMemoryInfo mif;
			systemSetting_->getMemoryInfo(mif);
			SStorageInfo sif;
			systemSetting_->getCurrentStorageInfo(sif);
			NetworkConfigList ncl;
			systemSetting_->getNetworkConfigList(ncl);
			runtimeInfo["cpuInfo"]["usage"] = cif.usage;
			runtimeInfo["memoryInfo"]["memTotal"] = mif.memTotal;
			runtimeInfo["memoryInfo"]["memFree"] = mif.memFree;
			runtimeInfo["memoryInfo"]["memUsed"] = mif.memUsed;
			runtimeInfo["memoryInfo"]["swapTotal"] = mif.swapTotal;
			runtimeInfo["memoryInfo"]["swapFree"] = mif.swapFree;
			runtimeInfo["memoryInfo"]["swapUsed"] = mif.swapUsed;
			runtimeInfo["storageInfo"]["name"] = sif.name;
			runtimeInfo["storageInfo"]["capacity"] = sif.capacity;
			runtimeInfo["storageInfo"]["used"] = sif.used;
			runtimeInfo["storageInfo"]["free"] = sif.free;
			for (auto it = ncl.begin(); it != ncl.end(); it++)
			{
				Json::Value nwc;
				nwc["type"] = it->type;
				nwc["name"] = it->name;
				nwc["ipv4"] = it->ipv4;
				nwc["netmask"] = it->netmask;
				nwc["mac"] = it->mac;
				nwc["ipv6"] = it->ipv6;
				nwc["rxPackets"] = it->rxPackets;
				nwc["rxBytes"] = it->rxBytes;
				nwc["txPackets"] = it->txPackets;
				nwc["txBytes"] = it->txBytes;
				nwc["linkDetected"] = it->linkDetected;
				runtimeInfo["netWorkConfigList"].append(nwc);
			}

			//cloudConfig
			runtimeInfo["clientID"] = mainPlatform_.clientId;
			runtimeInfo["username"] = mainPlatform_.mqttUsername;
			runtimeInfo["password"] = mainPlatform_.mqttPassword;
			runtimeInfo["resoureIP"] = mainPlatform_.resourceServer;
			runtimeInfo["mqttURL"] = "ssl://" + mainPlatform_.mqttUrl;
			runtimeInfo["connectStatus"] = mainPlatform_.mqttClient->getMqttConnectionStatus();
			{
				std::unique_lock<std::mutex> lock(mqttListMutex_);
				for (int i = 0; i < dataDistributionVec_.size(); i++)
				{
					const auto& dd = dataDistributionVec_[i];
					runtimeInfo["address"][i]["clientID"] = dd.clientId;
					runtimeInfo["address"][i]["username"] = dd.mqttUsername;
					runtimeInfo["address"][i]["password"] = dd.mqttPassword;
					runtimeInfo["address"][i]["resoureIP"] = dd.resourceServer;
					runtimeInfo["address"][i]["mqttURL"] = "ssl://" + dd.mqttUrl;
					runtimeInfo["address"][i]["connectStatus"] = dd.mqttClient->getMqttConnectionStatus();
				}
			}

			//cloud time
			int64_t cloudTime = 0;
			getCloudTime(cloudTime);
			time_t t = cloudTime / 1000;
			struct tm* tm = localtime(&t);
			char datestr[64];
			strftime(datestr, sizeof(datestr), "%Y-%m-%d %H:%M:%S", tm);
			runtimeInfo["cloudTime"] = datestr;
			//cloud version
			std::string cloudVersion;
			if (getCloudFirmwareInfo(cloudVersion) == 0)
			{
				runtimeInfo["cloudVersion"] = cloudVersion;
			}
			//other config
			runtimeInfo["disConnCache"] = enableLocalDataCaching_;
			runtimeInfo["cloudControlAuth"] = enableCloudControl_;

			//Json::FastWriter writer;
			//pkg["state"]["runtimeInfo"] = writer.write(runtimeInfo);
			pkg["state"]["runtimeInfo"] = runtimeInfo;
			pkg["updatetime"]["runtimeInfo"] = nowTime;
			pkg["quality"]["runtimeInfo"] = Json::arrayValue;
		}

	}

	void CGatewayAIOT::uploadInfoThread()
	{
		//避免启动第一次上送时emq还未连接
		std::this_thread::sleep_for(std::chrono::seconds(5));

		int64_t lastTime = 0;
		while (running_ && !reload_)
		{
			if (girc_.isReport)
			{
				int64_t nowTime = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
				if (abs(nowTime - lastTime) / 1000 >= girc_.interval || uploadInfoNow_)
				{
					lastTime = nowTime;
					uploadInfoNow_ = false;					
					
					if (girc_.basic || girc_.runtime)
					{
						Json::Value pkg;
						getUploadInfo(nowTime, girc_.basic, girc_.runtime, pkg);

						std::string topic = sog_.id + "/data";
						std::string payload = pkg.toStyledString();
						//printf("topic:%s, payload:%s.\n", topic.c_str(), payload.c_str());
						if (mainPlatform_.mqttClient->getMqttConnectionStatus())
						{
							mainPlatform_.mqttClient->publish(topic, payload);
						}
						{
							std::unique_lock<std::mutex> lock(mqttListMutex_);
							for (int i = 0; i < dataDistributionVec_.size(); i++)
							{
								auto& dd = dataDistributionVec_[i];
								if (dd.mqttClient->getMqttConnectionStatus())
									dd.mqttClient->publish(topic, payload);
							}
						}
					}
				}
			}
			std::this_thread::sleep_for(std::chrono::seconds(1));
		}
	}

	void CGatewayAIOT::uploadManageThread()
	{
		//避免启动第一次上送时emq还未连接
		std::this_thread::sleep_for(std::chrono::seconds(5));

		int64_t lastTime = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
		int64_t lastCpuSamplingTime = 0;             //cpu上次采样时间
		int64_t lastUploadInfoTime = 0;              //上次上送基本/运行信息时间
		int cpuSamplingTimes = 0, cpuAlarmTimes = 0; //采样次数，报警次数
		while (running_)
		{
			int64_t nowTime = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
			if (abs(nowTime - lastTime) / 1000 >= gmrc_.interval)
			{
				lastTime = nowTime;
				Json::Value pkg = Json::arrayValue;
				int index = 0;

				SStorageInfo sif;
				if (systemSetting_->getCurrentStorageInfo(sif))
				{
					if (sif.capacity != 0 && sif.used / sif.capacity >= gmrc_.diskAlarm)
						pkg[index++] = 1;
				}

				SMemoryInfo mif;
				if (systemSetting_->getMemoryInfo(mif))
				{
					if (mif.memTotal != 0 && mif.memUsed / mif.memTotal >= gmrc_.memoryAlarm)
						pkg[index++] = 2;
				}

				if (cpuSamplingTimes > 0)
				{
					if (cpuAlarmTimes / cpuSamplingTimes >= gmrc_.cpuAlarmPercent)
					{
						pkg[index++] = 3;
					}
					cpuSamplingTimes = 0;
					cpuAlarmTimes = 0;
				}

				if (index == 0)
					pkg[0] = 0;

				std::string payload = pkg.toStyledString();
				if (gmrc_.managePlatform.mqttClient->getMqttConnectionStatus())
					gmrc_.managePlatform.mqttClient->publish(gmrc_.alarmTopic, payload, 0, 1);
			}

			if (abs(nowTime - lastCpuSamplingTime) / 1000 >= gmrc_.cpuAlarmSamplingInterval)
			{
				lastCpuSamplingTime = nowTime;
				SCPUInfo cif;
				if (systemSetting_->getCPUInfo(cif))
				{
					cpuSamplingTimes += 1;
					if (cif.usage >= gmrc_.cpuAlarm)
						cpuAlarmTimes += 1;
				}
			}

			//上送基本/运行信息
			if (gmrc_.girc.interval > 0 && abs(nowTime - lastUploadInfoTime) >= gmrc_.girc.interval)
			{
				lastUploadInfoTime = nowTime;

				if (gmrc_.girc.basic || gmrc_.girc.runtime)
				{
					Json::Value pkg;
					getUploadInfo(nowTime, gmrc_.girc.basic, gmrc_.girc.runtime, pkg);

					std::string payload = pkg.toStyledString();
					if (gmrc_.managePlatform.mqttClient->getMqttConnectionStatus())
					{
						gmrc_.managePlatform.mqttClient->publish(gmrc_.infoTopic, payload, 0, 1);
					}
				}
			}

			std::this_thread::sleep_for(std::chrono::seconds(1));
		}
	}

	void CGatewayAIOT::sendDataThread()
	{
		std::vector<uint16_t> modbusBuffer(2);

		JSONCPP_STRING err;
		Json::Value root;
		Json::CharReaderBuilder builder;
		std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
		
		while (running_ && !reload_)
		{
			//bool uploadLocalCache = (enableLocalDataCaching_ && !ldc_.empty() && mainPlatform_.mqttClient->getMqttConnectionStatus());
			//bool uploadLocalCache = true;

			SDataSet dataSet;
			{
				std::unique_lock<std::mutex> lock(dataSetQueueMutex_);
				if (dataSetQueue_.empty())  
				{
					//if (!uploadLocalCache) //需要上送缓存数据时不sleep
					//{
					//	dataSetQueueCondVar_.wait_for(lock, std::chrono::seconds(1));
					//}
					dataSetQueueCondVar_.wait_for(lock, std::chrono::seconds(1));
				}
				else
				{
					dataSet = dataSetQueue_.front();
					dataSetQueue_.pop();
				}
			}

			if (!dataSet.channelName.empty())
			{
				for (auto deviceIter = dataSet.dataSetSptr->begin(); deviceIter != dataSet.dataSetSptr->end(); deviceIter++)
				{
					Json::Value state;
					Json::Value quality;
					Json::Value updatetime;

					for (auto pointIter = deviceIter->second.begin(); pointIter != deviceIter->second.end(); pointIter++)
					{
						switch (pointIter->second.value.getType())
						{
						case SValue::eValueInt:
							state[pointIter->first] = pointIter->second.value.toInt<int64_t>(); break;
						case SValue::eValueUInt:
							state[pointIter->first] = pointIter->second.value.toInt<uint64_t>(); break;
						case SValue::eValueReal:
							state[pointIter->first] = pointIter->second.value.toFloat<double>(); break;
						case SValue::eValueString:
							state[pointIter->first] = pointIter->second.value.toString(); break;
						case SValue::eValueBoolean:
							state[pointIter->first] = pointIter->second.value.toBool(); break;
						case SValue::eValueJson:
						{
							const auto& str = pointIter->second.value.toString();
							if (!reader->parse(str.c_str(), str.c_str() + str.size(), &root, &err))
							{
								continue;
							}
							state[pointIter->first] = root; break;
						}
						default: break;
						}

						if (enableModbusServer_)
						{
							int addr = getModbusAddr(modbusMappingTable_, dataSet.channelName, deviceIter->first, pointIter->first);
							if (addr >= 0)
							{
								float f32;
								switch (pointIter->second.value.getType())
								{
								case SValue::eValueInt:
									f32 = pointIter->second.value.asInt<int64_t>();
									memcpy(modbusBuffer.data(), &f32, sizeof(float));
									modbusTcpClient_->writeRegisters(addr, modbusBuffer);
								case SValue::eValueUInt:
									f32 = pointIter->second.value.asInt<uint64_t>();
									memcpy(modbusBuffer.data(), &f32, sizeof(float));
									modbusTcpClient_->writeRegisters(addr, modbusBuffer);
								case SValue::eValueReal:
									f32 = pointIter->second.value.asFloat();
									memcpy(modbusBuffer.data(), &f32, sizeof(float));
									modbusTcpClient_->writeRegisters(addr, modbusBuffer);
								case SValue::eValueBoolean:
									modbusTcpClient_->writeBit(addr, pointIter->second.value.asBool());
								default: break;
								}
							}

						}

						//if (configurationDatas_.find(dataSet.channelName) != configurationDatas_.end())
						//{
						//	if (configurationDatas_[dataSet.channelName].find(deviceIter->first) != configurationDatas_[dataSet.channelName].end())
						//	{
						//		if (configurationDatas_[dataSet.channelName][deviceIter->first].find(pointIter->first) != configurationDatas_[dataSet.channelName][deviceIter->first].end())
						//		{
						//			configurationDatas_[dataSet.channelName][deviceIter->first][pointIter->first] = pointIter->second;
						//		}
						//	}
						//}

						Json::Value qualityArr = Json::arrayValue;
						for (int i = 0; i < pointIter->second.qualityCodeVec.size(); i++)
						{
							switch (pointIter->second.qualityCodeVec[i])
							{
							case DRIVER::EQualityCode::none : qualityArr[i] = "none"; break;
							case DRIVER::EQualityCode::invalid : qualityArr[i] = "invalid"; break;
							case DRIVER::EQualityCode::unknown: qualityArr[i] = "unknown"; break;
							case DRIVER::EQualityCode::alarmst: qualityArr[i] = "alarmst"; break;
							case DRIVER::EQualityCode::alarmh: qualityArr[i] = "alarmh"; break;
							case DRIVER::EQualityCode::alarmhh: qualityArr[i] = "alarmhh"; break;
							case DRIVER::EQualityCode::alarml: qualityArr[i] = "alarml"; break;
							case DRIVER::EQualityCode::alarmll: qualityArr[i] = "alarmll"; break;
							case DRIVER::EQualityCode::scanoff: qualityArr[i] = "scanoff"; break;
							case DRIVER::EQualityCode::bad: qualityArr[i] = "bad"; break;
							case DRIVER::EQualityCode::typeerror: qualityArr[i] = "typeerror"; break;
							case DRIVER::EQualityCode::outofrange: qualityArr[i] = "outofrange"; break;
							default:
								break;
							}
						}
						quality[pointIter->first] = qualityArr;
						updatetime[pointIter->first] = pointIter->second.timestamp;
					}

					Json::Value pkg;
					pkg["timestamp"] = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
					pkg["state"] = std::move(state);
					pkg["quality"] = std::move(quality);
					pkg["updatetime"] = std::move(updatetime);

					std::string topic = deviceIter->first + "/data";
					std::string payload = pkg.toStyledString();

					sendDataToMqtt(deviceIter->first, pkg);					
				}
			}
				
			sendCacheDataToMqtt();
			//std::this_thread::sleep_for(std::chrono::milliseconds(100));
		}
	}

	void CGatewayAIOT::sendAllData()
	{
		//std::cout << __func__ << std::endl;
		std::vector<std::string> channelNames;
		driverEngine_->getChannelNames(channelNames);
		for (const auto& channelName : channelNames)
		{
			DRIVER::ChannelDataSet channelDataSet;
			driverEngine_->getChannelOnline(channelName, channelDataSet);
			driverEngine_->sendAllData(channelName);
			SDataSet aiotDataSet;
			aiotDataSet.channelName = channelName;
			aiotDataSet.dataSetSptr = std::make_shared<DRIVER::ChannelDataSet>(channelDataSet);
			std::unique_lock<std::mutex> lock(dataSetQueueMutex_);
			dataSetQueue_.emplace(std::move(aiotDataSet));
			dataSetQueueCondVar_.notify_one();
		}
	}

	void CGatewayAIOT::sendDataToMqtt(const std::string deviceId, const Json::Value& pkg)
	{
		std::string topic = deviceId + "/data";
		std::string payload = pkg.toStyledString();

		std::string hisTopic = deviceId + "/history";
		Json::Value hisPkg;
		hisPkg["history-data"][0] = pkg;
		SCacheData sdc;
		sdc.key = pkg["timestamp"].asString() + "/" + std::move(hisTopic);
		//sdc.value = hisPkg.toStyledString();
		Json::StreamWriterBuilder builder;
		builder["indentation"] = "";  // 无缩进
		std::string hisString = Json::writeString(builder, hisPkg);
		sdc.value = hisString;

		if (mainPlatform_.mqttClient->getMqttConnectionStatus())
		{
			if (recordReportData_)
			{
				std::string logstr = topic + "\n" + payload;
				logger_->info("upload data : " + logstr);
			}
			mainPlatform_.mqttClient->publish(topic, payload);
		}
		else
		{
			if (enableLocalDataCaching_)
			{
				ldc_.pushBack(sdc, mainPlatform_.mqttUrl);
			}
		}

		{
			std::unique_lock<std::mutex> lock(mqttListMutex_);
			for (int i = 0; i < dataDistributionVec_.size(); i++)
			{
				auto& dd = dataDistributionVec_[i];
				if (dd.mqttClient->getMqttConnectionStatus())
				{
					dd.mqttClient->publish(topic, payload);
				}
				else
				{
					if (enableLocalDataCaching_)
					{
						ldc_.pushBack(sdc, dd.mqttUrl);
					}
				}
			}
		}

	}

	void CGatewayAIOT::sendCacheDataToMqtt()
	{
		SCacheData sdc = ldc_.front(mainPlatform_.mqttUrl);
		if (!sdc.value.empty())
		{
			std::string topic = sdc.key;
			auto pos = topic.find('/');
			if (pos != std::string::npos)
			{
				topic.erase(0, pos + 1);
			}
			if (mainPlatform_.mqttClient->getMqttConnectionStatus())
			{
				mainPlatform_.mqttClient->publish(topic, sdc.value);
				ldc_.popFront(mainPlatform_.mqttUrl);
			}
		}

		{
			std::unique_lock<std::mutex> lock(mqttListMutex_);
			for (int i = 0; i < dataDistributionVec_.size(); i++)
			{
				auto& dd = dataDistributionVec_[i];
				SCacheData sdc = ldc_.front(dd.mqttUrl);
				if (!sdc.value.empty())
				{
					std::string topic = sdc.key;
					auto pos = topic.find('/');
					if (pos != std::string::npos)
					{
						topic.erase(0, pos + 1);
					}
					if (dd.mqttClient->getMqttConnectionStatus())
					{
						dd.mqttClient->publish(topic, sdc.value);
						ldc_.popFront(dd.mqttUrl);
					}
				}
			}
		}

	}

	void CGatewayAIOT::reportEvent(SAIOTEventSptr aiotEventSptr, CMqttClient* mqttClient)
	{
		Json::Value root;
		root["id"] = aiotEventSptr->eventSptr->id;
		root["type"] = aiotEventSptr->eventSptr->type;
		root["info"] = aiotEventSptr->eventSptr->raw;
		root["uuid"] = aiotEventSptr->uuid;

		Json::Value extends;
		for (auto& iter : aiotEventSptr->eventSptr->extends)
		{
			switch (iter.second.getType())
			{
			case SValue::eValueInt:
				extends[iter.first] = iter.second.asInt<int64_t>();
				break;
			case SValue::eValueUInt:
				extends[iter.first] = iter.second.asInt<uint64_t>();
				break;
			case SValue::eValueReal:
				extends[iter.first] = iter.second.asFloat();
				break;
			case SValue::eValueString:
				extends[iter.first] = iter.second.asString();
				break;
			case SValue::eValueBoolean:
				extends[iter.first] = iter.second.asBool();
				break;
			default:
				break;
			}
		}
		root["extends"] = std::move(extends);
		
		int i = 0;
		for (const auto &fileIter : aiotEventSptr->eventSptr->files)
		{
			Json::Value file;
			file["name"] = fileIter.first;
			file["lifetime"] = 604800;
			root["file"][i++] = std::move(file);
		}

		mqttClient->publish(aiotEventSptr->deviceId + "/event", root.toStyledString());
	}

	bool CGatewayAIOT::uploadEventFile(SAIOTEventSptr aiotEventSptr, const std::string& resourceServer)
	{
		bool rv = false;
		std::string eventFilesPath = "http://" + resourceServer + API_EVENT_FILE_PATH + aiotEventSptr->deviceId + "/";
		CURL* curl = curl_easy_init();
		curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "PUT");
		for (const auto& fileIter : aiotEventSptr->eventSptr->files)
		{
			std::string eventFilePath = eventFilesPath + fileIter.first;
			struct curl_slist* headers = nullptr;
			headers = curl_slist_append(headers, std::string("filename:").append(fileIter.first).c_str());
			headers = curl_slist_append(headers, std::string("lifetime:").append(std::to_string(aiotEventSptr->eventSptr->timestamp)).c_str());
			curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
			curl_easy_setopt(curl, CURLOPT_POSTFIELDS, fileIter.second.data());
			curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, fileIter.second.size());
			curl_easy_setopt(curl, CURLOPT_URL, eventFilePath.c_str());
			if (curl_easy_perform(curl) == CURLE_OK)
			{
				rv = true;
				logger_->info("uploadEventFile success, server url = " + resourceServer);
			}
			else
			{
				logger_->info("uploadEventFile fail, server url = " + resourceServer);
			}
		}

		curl_easy_cleanup(curl);
		return rv;
	}

	void CGatewayAIOT::reportEventThread()
	{
		while (running_ && !reload_)
		{
			SAIOTEventSptr eventSptr;
			{
				std::unique_lock<std::mutex> lock(eventQueueMutex_);
				if (eventQueue_.empty())
					eventCondVar_.wait(lock);
				else
				{
					eventSptr = eventQueue_.front();
					eventQueue_.pop();
				}
			}

			if (eventSptr.get())
			{
				if (!eventSptr->eventSptr->files.empty())
				{
					if(uploadEventFile(eventSptr, mainPlatform_.resourceServer))
						reportEvent(eventSptr, mainPlatform_.mqttClient);
				}
				else
				{
					reportEvent(eventSptr, mainPlatform_.mqttClient);
				}
				

				{
					std::unique_lock<std::mutex> lock(mqttListMutex_);
					for (auto& iter : dataDistributionVec_)
					{
						if (!eventSptr->eventSptr->files.empty())
						{
							if(uploadEventFile(eventSptr, iter.resourceServer))
								reportEvent(eventSptr, iter.mqttClient);
						}
						else
						{
							reportEvent(eventSptr, iter.mqttClient);
						}
					}
				}
			}
		}
	}

	bool CGatewayAIOT::getTimeDifference(int64_t& td)
	{
		auto res = httpClient_->get("http://" + mainPlatform_.resourceServer + cloudTimeUri_);
		auto& resp = res.body;
		if (resp.size() > 0)
		{
			JSONCPP_STRING err;
			Json::Value root;
			Json::CharReaderBuilder builder;
			std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
			if (reader->parse((char*)resp.data(), (char*)resp.data() + resp.size(), &root, &err))
			{
				int64_t cloudTime = root["data"].asInt64();
				int64_t localTime = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
				td = cloudTime - localTime;
				return true;
			}
		}

		return false;
	}

	bool CGatewayAIOT::getCloudTime(int64_t& ct)
	{
		auto res = httpClient_->get("http://" + mainPlatform_.resourceServer + cloudTimeUri_);
		auto& resp = res.body;
		if (resp.size() > 0)
		{
			JSONCPP_STRING err;
			Json::Value root;
			Json::CharReaderBuilder builder;
			std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
			if (reader->parse((char*)resp.data(), (char*)resp.data() + resp.size(), &root, &err))
			{
				ct = root["data"].asInt64();
				return true;
			}
		}

		return false;
	}

	bool CGatewayAIOT::downloadVersionList(const std::string& url, const std::string& clientId)
	{
		bool ret = true;
		auto res = httpClient_->get("http://" + mainPlatform_.resourceServer + versionUri_);
		auto& resp = res.body;

		//std::string versionFilePath = std::string(CLOUD_CONFIG_PATH) + "version";
		//if (isMultiGatewayEnv)
		//{
		//	versionFilePath = GENERATE_MULTI_GATEWAY_FILE_PATH(launchPath, driverXId, "cloudConfig/version");
		//}
		std::string versionFilePath = CPathManager::getInstance().getCloudVersionPath();
		std::ofstream ofs;
		ofs.open(versionFilePath);
		if (ofs.is_open())
		{
			ofs.write((const char*)resp.data(), resp.size());
			ofs.close();
		}
		else
		{
			ret = false;
		}
		return ret;
	}

	bool CGatewayAIOT::loadVersionList()
	{
		bool ret = true;

		//std::string versionFilePath = std::string(CLOUD_CONFIG_PATH) + "version";
		//if (isMultiGatewayEnv)
		//{
		//	versionFilePath = GENERATE_MULTI_GATEWAY_FILE_PATH(launchPath, driverXId, "cloudConfig/version");
		//}
		std::string versionFilePath = CPathManager::getInstance().getCloudVersionPath();
		std::ifstream ifs;
		ifs.open(versionFilePath);
		if (ifs.is_open())
		{
			JSONCPP_STRING err;
			Json::Value root;
			Json::CharReaderBuilder builder;
			if (Json::parseFromStream(builder, ifs, &root, &err))
			{
				const Json::Value& productVersionList = root["data"]["productVersionList"];
				for (int i = 0; i < productVersionList.size(); ++i)
				{
					productVerTable_[productVersionList[i]["id"].asString()] = productVersionList[i]["version"].asInt();
				}

				const Json::Value& deviceVersionList = root["data"]["deviceVersionList"];
				for (int i = 0; i < deviceVersionList.size(); ++i)
				{
					deviceVerTable_[deviceVersionList[i]["id"].asString()] = deviceVersionList[i]["version"].asInt();
				}
			}
			else
			{
				ret = false;
			}
		}
		else
		{
			ret = false;
		}
		return ret;
	}

	bool CGatewayAIOT::downloadConfig(const std::string& url, const std::string& gatewayId)
	{
		CURLcode result = CURLE_OK;
		//std::string fileName = CLOUD_CONFIG_PATH + gatewayId + ".tar";
		//if (isMultiGatewayEnv)
		//{
		//	fileName = std::string(GENERATE_MULTI_GATEWAY_FILE_PATH(launchPath, driverXId, "cloudConfig/")) + gatewayId + ".tar";
		//}
		std::string fileName = CPathManager::getInstance().getDownloadConfigTarName(gatewayId);
		//std::cout << __func__ << "   " << fileName << std::endl;
		FILE* fp = fopen(fileName.c_str(), "wb");
		if (!fp)
		{
			return false;
		}

		CURL* http_handle;
		CURLM* multi_handle;

		int still_running = 1; /* keep number of running handles */

		http_handle = curl_easy_init();
		if (!http_handle)
		{
			curl_easy_cleanup(http_handle);
			fclose(fp);
			return false;
		}

		/* set options */
		curl_easy_setopt(http_handle, CURLOPT_URL, url.c_str());
		curl_easy_setopt(http_handle, CURLOPT_WRITEDATA, fp);
		curl_easy_setopt(http_handle, CURLOPT_WRITEFUNCTION, fwrite);
		curl_easy_setopt(http_handle, CURLOPT_TIMEOUT, 600);

		/* init a multi stack */
		multi_handle = curl_multi_init();

		/* add the individual transfers */
		curl_multi_add_handle(multi_handle, http_handle);

		while (still_running && running_) {
			CURLMsg* msg;
			int queued;
			CURLMcode mc = curl_multi_perform(multi_handle, &still_running);
			if (mc != CURLM_OK) 
			{
				result = CURLE_HTTP_RETURNED_ERROR;
				break;
			}

			if (still_running)
				/* wait for activity, timeout or "nothing" */
				mc = curl_multi_poll(multi_handle, NULL, 0, 1000, NULL);

			if (mc) 
			{
				result = CURLE_HTTP_RETURNED_ERROR;
				break;
			}

			do {
				msg = curl_multi_info_read(multi_handle, &queued);
				if (msg) {
					if (msg->msg == CURLMSG_DONE) {
						if (msg->data.result != CURLE_OK)
							result = msg->data.result;
						/* a transfer ended */
						fprintf(stderr, "Transfer completed\n");
					}
				}
			} while (msg);
		}

		curl_multi_remove_handle(multi_handle, http_handle);
		curl_multi_cleanup(multi_handle);
		curl_easy_cleanup(http_handle);
		fclose(fp);

		return result == CURLE_OK;
	}

	bool CGatewayAIOT::loadConfigOld(SObjectGateway& sog, std::map<std::string, DRIVER::SChannelConfig>& sccs)
	{
		logger_->info("loadConfigOld\n");
		//std::string src = CLOUD_CONFIG_PATH + mainPlatform_.clientId + ".tar";
		//if (isMultiGatewayEnv)
		//{
		//	src = GENERATE_MULTI_GATEWAY_FILE_PATH(launchPath, driverXId, "cloudConfig/") + mainPlatform_.clientId + ".tar";
		//}
		std::string src = CPathManager::getInstance().getDownloadConfigTarName(mainPlatform_.clientId);

		//std::string cloudConfigPath = CLOUD_CONFIG_PATH;
		//if (isMultiGatewayEnv)
		//{
		//	cloudConfigPath = std::string(GENERATE_MULTI_GATEWAY_FILE_PATH(launchPath, driverXId, "cloudConfig/"));
		//}
		std::string cloudConfigPath = CPathManager::getInstance().getCloudConfigPath();
		if (zip_extract(src.c_str(), cloudConfigPath.c_str(), nullptr, nullptr) < 0)
		{
			std::cout << src << "   " << cloudConfigPath << "   zip_extract err" << std::endl;
			return false;
		}

		sccs.clear();
		deviceIdVec_.clear();
		ChannelIdVec_.clear();
		SClassProduct scp;
		SClassLink scl;
		SClassGateway scg;
		std::map<std::string, SClassProduct> scps;
		std::map<std::string, SClassLink> scls;
		std::map<std::string, SClassGateway> scgs;

		SObjectDevice sod;
		SObjectLink sol;
		//SObjectGateway sog;

		std::vector<std::string> filesContent;
		if (!getFilesContent(cloudConfigPath.c_str() + mainPlatform_.clientId + "/product", filesContent))
		{
			return false;
		}

		for (const auto& product : filesContent)
		{
			JSONCPP_STRING err;
			Json::Value root;
			Json::CharReaderBuilder builder;
			std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
			if (!reader->parse(product.c_str(), product.c_str() + product.size(), &root, &err))
			{
				return false;
			}


			if (root["classType"].asString() == "product" ||
				root["classType"].asString() == "point")
			{
				scp.classId = root["classID"].asString();
				scp.name = root["name"].asString();
				scp.attributes.clear();
				const auto& attributes = root["attribute"];
				for (int i = 0; i < attributes.size(); i++)
				{
					SAttribute attribute;
					attribute.code = attributes[i]["code"].asString();
					attribute.name = attributes[i]["name"].asString();
					attribute.description = attributes[i]["description"].asString();
					attribute.pointType = attributes[i]["pointType"].asString();
					attribute.type = attributes[i]["type"].asString();
					if (attribute.type[0] == 'A' && attributes[i]["value"].isObject())
					{
						std::string ratio = attributes[i]["value"]["ratio"].asString();
						if (!ratio.empty()) attribute.coefficient = ::atof(ratio.c_str());
						std::string coef = attributes[i]["value"]["offset"].asString();
						if (!coef.empty()) attribute.added = ::atof(coef.c_str());
						std::string maximum = attributes[i]["value"]["maximum"].asString();
						if (!maximum.empty()) attribute.maxValue = ::atof(maximum.c_str());
						std::string minimum = attributes[i]["value"]["minimum"].asString();
						if (!minimum.empty()) attribute.minValue = ::atof(minimum.c_str());
					}
					for (auto valueAttrIter = attributes[i]["value"].begin(); valueAttrIter != attributes[i]["value"].end(); valueAttrIter++)
					{
						attribute.value[valueAttrIter.name()] = valueAttrIter->asString();
					}
					attribute.pos = i;
					scp.attributes[attribute.code] = std::move(attribute);
				}

				scps[scp.classId] = scp;
			}
			else if (root["classType"].asString() == "link")
			{

				for (auto driverFileIter = root["driverFile"].begin(); driverFileIter != root["driverFile"].end(); driverFileIter++)
				{
					scl.driverFile[driverFileIter.name()] = driverFileIter->asString();
				}
				if (root.isMember("scriptType") && root["scriptType"].isString())
				{
					scl.scriptType = root["scriptType"].asString();
				}
				if (root.isMember("javascriptExpresstion") && root["javascriptExpresstion"].isString())
				{
					scl.javascriptExpresstion = root["javascriptExpresstion"].asString();
				}
				const auto& arguments = root["argument"];
				scl.argumentStr = arguments.toStyledString();
				for (int i = 0; i < arguments.size(); i++)
				{
					SClassLink::SArgument argument;
					argument.defaultValue = arguments[i]["defaultValue"].asString();
					argument.name = arguments[i]["name"].asString();
					argument.option = arguments[i]["option"].asInt();
					argument.type = arguments[i]["type"].asString();
					scl.arguments.emplace_back(std::move(argument));
				}
				const auto& connectionArgs = root["connection"];
				scl.connectionStr = connectionArgs.toStyledString();
				for (int i = 0; i < connectionArgs.size(); i++)
				{
					SClassLink::SConnectionArg argument;
					argument.defaultValue = connectionArgs[i]["defaultValue"].asString();
					argument.name = connectionArgs[i]["name"].asString();
					argument.option = connectionArgs[i]["option"].asInt();
					argument.type = connectionArgs[i]["type"].asString();
					scl.connection.emplace_back(std::move(argument));
				}
				scl.classId = root["classID"].asString();
				scl.creationTime = root["creationTime"].asLargestUInt();
				scl.modificationTime = root["modificationTime"].asLargestUInt();
				scl.name = root["name"].asString();
				const auto& attributes = root["attribute"];
				for (int i = 0; i < attributes.size(); i++)
				{
					SAttribute attribute;
					attribute.code = attributes[i]["code"].asString();
					attribute.name = attributes[i]["name"].asString();
					attribute.description = attributes[i]["description"].asString();
					attribute.pointType = attributes[i]["pointType"].asString();
					attribute.type = attributes[i]["type"].asString();
					for (auto valueAttrIter = attributes[i]["value"].begin(); valueAttrIter != attributes[i]["value"].end(); valueAttrIter++)
					{
						attribute.value[valueAttrIter.name()] = valueAttrIter->asString();
					}
					scl.attributes.emplace_back(std::move(attribute));
				}
				scls[scl.classId] = scl;
			}
			else if (root["classType"].asString() == "edge")
			{

				scg.classId = root["classID"].asString();
				scg.name = root["name"].asString();
				const auto& attributes = root["attribute"];
				for (int i = 0; i < attributes.size(); i++)
				{
					SAttribute attribute;
					attribute.code = attributes[i]["code"].asString();
					attribute.name = attributes[i]["name"].asString();
					attribute.description = attributes[i]["description"].asString();
					attribute.pointType = attributes[i]["pointType"].asString();
					attribute.type = attributes[i]["type"].asString();
					for (auto valueAttrIter = attributes[i]["value"].begin(); valueAttrIter != attributes[i]["value"].end(); valueAttrIter++)
					{
						attribute.value[valueAttrIter.name()] = valueAttrIter->asString();
					}
					scg.attributes.emplace_back(attribute);
				}
				scgs[scg.classId] = scg;
			}
		}

		filesContent.clear();
		if (!getFilesContent(cloudConfigPath.c_str() + mainPlatform_.clientId + "/device", filesContent))
		{
			return false;
		}

		for (const auto& device : filesContent)
		{
			JSONCPP_STRING err;
			Json::Value root;
			Json::CharReaderBuilder builder;
			std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
			if (!reader->parse(device.c_str(), device.c_str() + device.size(), &root, &err))
			{
				return false;
			}

			if (root["classType"].asString() == "product" ||
				root["classType"].asString() == "point")
			{
				sod.classId = root["classID"].asString();
				sod.name = root["name"].asString();
				sod.id = root["ID"].asString();
				sod.fatherId = root["fatherID"].asString();
				const auto& arguments = root["protocol"]["argument"];
				auto& scp = scps[sod.classId];
				std::map<std::string, DRIVER::SPointConfig> pointTable;
				std::map<int, std::string> pointPosTable;

				//针对没有协议列的情况，先遍历原型中的外部点位
				for (auto& it_attr : scp.attributes) {
					if (it_attr.second.pointType.substr(0, 8) != "internal") {
						auto& spc = pointTable[it_attr.first];
						spc.alias = it_attr.second.name;
						spc.pointType = it_attr.second.pointType;
						spc.type = it_attr.second.type;
						spc.coefficient = it_attr.second.coefficient;
						spc.added = it_attr.second.added;
						spc.description = it_attr.second.description;
						spc.maxValue = it_attr.second.maxValue;
						spc.minValue = it_attr.second.minValue;
						spc.value = it_attr.second.value;
						pointPosTable[it_attr.second.pos] = it_attr.first;
					}
				}

				Json::Value pointAttributes = root["attribute"];
				for (auto att_iter = pointAttributes.begin(); att_iter != pointAttributes.end(); att_iter++)
				{
					if (att_iter->isMember("valueEnable") && (*att_iter)["valueEnable"].asBool())
					{
						std::string pointCode = (*att_iter)["code"].asString();
					
						if (pointTable.find(pointCode) != pointTable.end())
						{
							pointTable[pointCode].value.clear();
							if (att_iter->isMember("value"))
							{
								for (auto val_iter = (*att_iter)["value"].begin(); val_iter != (*att_iter)["value"].end(); val_iter++)
								{
									pointTable[pointCode].value[val_iter.name()] = val_iter->asString();
								}

								if ((*att_iter)["value"].isMember("maximum"))
								{
									std::string tmpmaximum = (*att_iter)["value"]["maximum"].asString();
									if (!tmpmaximum.empty())
										pointTable[pointCode].maxValue = std::atof(tmpmaximum.c_str());
								}
								if ((*att_iter)["value"].isMember("minimum"))
								{
									std::string tmpminimum = (*att_iter)["value"]["minimum"].asString();
									if (!tmpminimum.empty())
										pointTable[pointCode].minValue = std::atof(tmpminimum.c_str());
								}
								if ((*att_iter)["value"].isMember("ratio"))
								{
									std::string tmpratio = (*att_iter)["value"]["ratio"].asString();
									if (!tmpratio.empty())
										pointTable[pointCode].coefficient = std::atof(tmpratio.c_str());
								}
								if ((*att_iter)["value"].isMember("offset"))
								{
									std::string tmpoffset = (*att_iter)["value"]["offset"].asString();
									if (!tmpoffset.empty())
										pointTable[pointCode].added = std::atof(tmpoffset.c_str());
								}
							}
						}

					}
				}

				for (auto pointIter = arguments.begin(); pointIter != arguments.end(); pointIter++)
				{
					auto it_point = pointTable.find(pointIter.name());
					if (it_point == pointTable.end())
						continue;
					
					DRIVER::OriginProtocol pointProtocol;
					for (auto argIter = pointIter->begin(); argIter != pointIter->end(); argIter++)
					{
						pointProtocol[argIter.name()] = argIter->asString();
					}
					it_point->second.pointProtocol = std::move(pointProtocol);
				}

				//记录在原型中的顺序
				int pos_idx = 0;
				for (auto it_pos : pointPosTable) {
					pointTable[it_pos.second].pos = pos_idx++;
				}

				//内部点
				std::map<std::string, DRIVER::SPointConfig> innerPointTable;
				for (auto& it_attr : scp.attributes) {
					if (it_attr.second.pointType.substr(0, 8) == "internal") {
						innerPointTable[it_attr.first].alias = it_attr.second.name;
						innerPointTable[it_attr.first].pointType = it_attr.second.pointType;
						innerPointTable[it_attr.first].type = it_attr.second.type;
						innerPointTable[it_attr.first].coefficient = it_attr.second.coefficient;
						innerPointTable[it_attr.first].added = it_attr.second.added;
						innerPointTable[it_attr.first].description = it_attr.second.description;
					}
				}

				deviceIdVec_.push_back(sod.id);
				auto findlinkid = std::find(ChannelIdVec_.rbegin(), ChannelIdVec_.rend(), sod.fatherId);
				if (findlinkid == ChannelIdVec_.rend())
					ChannelIdVec_.push_back(sod.fatherId);
				sccs[sod.fatherId].deviceTable[sod.id].name = sod.id;
				sccs[sod.fatherId].deviceTable[sod.id].alias = sod.name;
				sccs[sod.fatherId].deviceTable[sod.id].pointTable = std::move(pointTable);
				sccs[sod.fatherId].deviceTable[sod.id].innerPointTable = std::move(innerPointTable);
			}
			else if (root["classType"].asString() == "link")
			{

				sol.classId = root["classID"].asString();
				sol.name = root["name"].asString();
				sol.id = root["ID"].asString();
				sol.fatherId = root["fatherID"].asString();
				const auto& connectionArgs = root["connection"];
				for (auto connectionArgIter = connectionArgs.begin(); connectionArgIter != connectionArgs.end(); connectionArgIter++)
				{
					sccs[sol.id].channelProtocol[connectionArgIter.name()] = connectionArgIter->asString();
				}

				if (scls[sol.classId].scriptType.compare("javascript") == 0)
				{
					sccs[sol.id].driverType = "DriverJS";
					sccs[sol.id].channelProtocol["_javascriptExpresstion"] = scls[sol.classId].javascriptExpresstion; //js脚本放到连接参数
				}
				else 
				{
					sccs[sol.id].driverType = scls[sol.classId].driverFile.begin()->second;
				}
				sccs[sol.id].name = sol.id;
				sccs[sol.id].alias = sol.name;
				sccs[sol.id].className = scls[sol.classId].name;
				sccs[sol.id].argumentStr = scls[sol.classId].argumentStr;
				sccs[sol.id].connectionStr = scls[sol.classId].connectionStr;
			}
			else if (root["classType"].asString() == "edge")
			{
				sog.classId = root["classID"].asString();
				sog.id = root["ID"].asString();
				sog.name = root["name"].asString();
				const auto& mqtt = root["mqtt"];
				sog.mqttClientID = mqtt["clientID"].asString();
				sog.mqttPassword = mqtt["password"].asString();
				sog.mqttUsername = mqtt["username"].asString();
				sog.className = scgs[sog.classId].name;

				if (root.isMember("projectName") && root["projectName"].isString()) 
				{
					sog.projectName = root["projectName"].asString();
				}
				else 
				{
					sog.projectName = "默认项目";
				}
			}
		}
		return true;
	}

	bool CGatewayAIOT::loadConfig(SObjectGateway& sog, std::map<std::string, DRIVER::SChannelConfig>& sccs)
	{
		logger_->info("loadConfig\n");
		//std::string zipFile = CLOUD_CONFIG_PATH + mainPlatform_.clientId + ".tar";
		//if (isMultiGatewayEnv)
		//{
		//	zipFile = GENERATE_MULTI_GATEWAY_FILE_PATH(launchPath, driverXId, "cloudConfig/") + mainPlatform_.clientId + ".tar";
		//}
		std::string zipFile = CPathManager::getInstance().getDownloadConfigTarName(mainPlatform_.clientId);
		auto pzip = zip_open(zipFile.c_str(), 0, 'r');
		if (!pzip)
		{
			return false;
		}

		long totalEntry = zip_entries_total(pzip);

		std::string entryProduct = mainPlatform_.clientId + "/product/";
		zip_entry_open(pzip, entryProduct.c_str());
		long productIndexStart = zip_entry_index(pzip) + 1;
		zip_entry_close(pzip);
		std::string entryDevice = mainPlatform_.clientId + "/device/";
		zip_entry_open(pzip, entryDevice.c_str());
		long deviceIndexStart = zip_entry_index(pzip) + 1;
		zip_entry_close(pzip);
		long productIndexEnd = deviceIndexStart - 2;
		long deviceIndexEnd = totalEntry - 1;

		if (productIndexStart - 1 != 1 && productIndexStart >= deviceIndexStart)
		{
			bool ret = loadConfigOld(sog, sccs);
			zip_close(pzip);
			return ret;
		}

		sccs.clear();
		deviceIdVec_.clear();
		ChannelIdVec_.clear();

		std::unordered_map<std::string, SClassProduct> scps;
		std::unordered_map<std::string, SClassLink> scls;
		std::unordered_map<std::string, SClassGateway> scgs;

		char* buf = new char[1024 * 1024 * 4]; // 1MB
		size_t bufsize = 0;

		while (productIndexStart <= productIndexEnd && running_)
		{
			zip_entry_openbyindex(pzip, productIndexStart++);
			bufsize = zip_entry_size(pzip);
			
			zip_entry_noallocread(pzip, buf, bufsize);
			zip_entry_close(pzip);
			//std::cout << std::string(buf, buf + bufsize) << std::endl;
			yyjson_doc* doc = yyjson_read((char*)buf, bufsize, 0);
			yyjson_val* root = yyjson_doc_get_root(doc);

			const char* classType = yyjson_get_str(yyjson_obj_get(root, "classType"));

			if (*classType == 'p') // classType == "product"
			{
				SClassProduct scp;
				scp.classId = yyjson_get_str(yyjson_obj_get(root, "classID"));
				//scp.creationTime = root["creationTime"].asLargestUInt();
				//scp.modificationTime = root["modificationTime"].asLargestUInt();
				scp.name = yyjson_get_str(yyjson_obj_get(root, "name"));
				//scp.type = yyjson_get_str(yyjson_obj_get(root, "type"));
				//scp.version = root["version"].asInt();
				//scp.projectId = root["projectID"].asString();
				//scp.systemVersion = root["systemVersion"].asString();
				//scp.classType = root["classType"].asString();
				auto attrs = yyjson_obj_get(root, "attribute");
				size_t idx, max;
				yyjson_val* attr;
				yyjson_arr_foreach(attrs, idx, max, attr) {
					SAttribute attribute;
					attribute.code = yyjson_get_str(yyjson_obj_get(attr, "code"));
					attribute.name = yyjson_get_str(yyjson_obj_get(attr, "name"));
					attribute.description = yyjson_get_str(yyjson_obj_get(attr, "description"));
					attribute.pointType = yyjson_get_str(yyjson_obj_get(attr, "pointType"));
					attribute.type = yyjson_get_str(yyjson_obj_get(attr, "type"));
					auto attrValue = yyjson_obj_get(attr, "value");
					if (attribute.type[0] == 'A' && yyjson_is_obj(attrValue))
					{
						//todo : unit,deadZone,decimal
						auto minValue = yyjson_obj_get(attrValue, "minimum");
						if (minValue)
						{
							attribute.minValue = ::atof(yyjson_get_str(minValue));
						}
						auto maxValue = yyjson_obj_get(attrValue, "maximum");
						if (maxValue)
						{
							attribute.maxValue = ::atof(yyjson_get_str(maxValue));
						}
						auto ratio = yyjson_obj_get(attrValue, "ratio");
						if (ratio)
						{
							attribute.coefficient = ::atof(yyjson_get_str(ratio));
						}
						auto offset = yyjson_obj_get(attrValue, "offset");
						if (offset)
						{
							attribute.added = ::atof(yyjson_get_str(offset));
						}
					}

					yyjson_val* key, * val;
					yyjson_obj_iter iter;
					yyjson_obj_iter_init(attrValue, &iter);
					while ((key = yyjson_obj_iter_next(&iter))) {
						val = yyjson_obj_iter_get_val(key);
						switch (yyjson_get_tag(val)) {
						case YYJSON_TYPE_STR | YYJSON_SUBTYPE_NONE:  attribute.value[yyjson_get_str(key)] = yyjson_get_str(val); break;
						case YYJSON_TYPE_BOOL | YYJSON_SUBTYPE_TRUE:  attribute.value[yyjson_get_str(key)] = "true"; break;
						case YYJSON_TYPE_BOOL | YYJSON_SUBTYPE_FALSE: attribute.value[yyjson_get_str(key)] = "false"; break;
						case YYJSON_TYPE_NUM | YYJSON_SUBTYPE_UINT:  attribute.value[yyjson_get_str(key)] = std::to_string(yyjson_get_uint(val)); break;
						case YYJSON_TYPE_NUM | YYJSON_SUBTYPE_SINT:  attribute.value[yyjson_get_str(key)] = std::to_string(yyjson_get_sint(val)); break;
						case YYJSON_TYPE_NUM | YYJSON_SUBTYPE_REAL:  attribute.value[yyjson_get_str(key)] = std::to_string(yyjson_get_real(val)); break;
						default:                                      attribute.value[yyjson_get_str(key)] = ""; break;
						}
					}

					attribute.pos = idx;
					scp.attributes[attribute.code] = std::move(attribute);
				}

				scps[scp.classId] = std::move(scp);
			}
			else if (*classType == 'l') //classType == "link"
			{
				SClassLink scl;
				yyjson_val* key, * val;
				yyjson_obj_iter iter;
				yyjson_obj_iter_init(yyjson_obj_get(root, "driverFile"), &iter);
				while ((key = yyjson_obj_iter_next(&iter))) {
					val = yyjson_obj_iter_get_val(key);
					scl.driverFile[yyjson_get_str(key)] = yyjson_get_str(val);
				}

				auto scriptType_obj = yyjson_obj_get(root, "scriptType");
				if (scriptType_obj) {
					scl.scriptType = yyjson_get_str(scriptType_obj); //c++ javascript
				}
				auto javascriptExpresstion_obj = yyjson_obj_get(root, "javascriptExpresstion");
				if (javascriptExpresstion_obj) {
					scl.javascriptExpresstion = yyjson_get_str(javascriptExpresstion_obj);
				}
				
				auto args = yyjson_obj_get(root, "argument");
				size_t argument_len = 0;
				char* argument_str = yyjson_val_write(args, 0, &argument_len);
				if (argument_str)
				{
					scl.argumentStr = std::string(argument_str, argument_len);
				}
				size_t idx, max;
				yyjson_val* arg;

				/*yyjson_arr_foreach(args, idx, max, arg) {
					SClassLink::SArgument argument;
					auto defaultValue = yyjson_obj_get(arg, "defaultValue");
					if (defaultValue)
					{
						argument.defaultValue = yyjson_get_str(defaultValue);
					}
					argument.name = yyjson_get_str(yyjson_obj_get(arg, "name"));
					argument.option = yyjson_get_int(yyjson_obj_get(arg, "option"));
					argument.type = yyjson_get_str(yyjson_obj_get(arg, "type"));
					scl.arguments.emplace_back(std::move(argument));
				}*/

				args = yyjson_obj_get(root, "connection");
				size_t connection_len = 0;
				char* connection_str = yyjson_val_write(args, 0, &connection_len);
				if (connection_str)
				{
					scl.connectionStr = std::string(connection_str, connection_len);
				}

				yyjson_arr_foreach(args, idx, max, arg) {
					SClassLink::SConnectionArg argument;
					auto defaultValue = yyjson_obj_get(arg, "defaultValue");
					if (defaultValue)
					{
						switch (yyjson_get_tag(defaultValue)) {
						case YYJSON_TYPE_STR | YYJSON_SUBTYPE_NONE:  argument.defaultValue = yyjson_get_str(defaultValue); break;
						case YYJSON_TYPE_BOOL | YYJSON_SUBTYPE_TRUE:  argument.defaultValue = "true"; break;
						case YYJSON_TYPE_BOOL | YYJSON_SUBTYPE_FALSE: argument.defaultValue = "false"; break;
						case YYJSON_TYPE_NUM | YYJSON_SUBTYPE_UINT:  argument.defaultValue = std::to_string(yyjson_get_uint(defaultValue)); break;
						case YYJSON_TYPE_NUM | YYJSON_SUBTYPE_SINT:  argument.defaultValue = std::to_string(yyjson_get_sint(defaultValue)); break;
						case YYJSON_TYPE_NUM | YYJSON_SUBTYPE_REAL:  argument.defaultValue = std::to_string(yyjson_get_real(defaultValue)); break;
						default:                                      argument.defaultValue = ""; break;
						}
					}
					argument.name = yyjson_get_str(yyjson_obj_get(arg, "name"));
					argument.option = yyjson_get_int(yyjson_obj_get(arg, "option"));
					argument.type = yyjson_get_str(yyjson_obj_get(arg, "type"));
					scl.connection.emplace_back(std::move(argument));
				}

				scl.classId = yyjson_get_str(yyjson_obj_get(root, "classID"));
				//scl.creationTime = root["creationTime"].asLargestUInt();
				//scl.modificationTime = root["modificationTime"].asLargestUInt();
				scl.name = yyjson_get_str(yyjson_obj_get(root, "name"));
				//scl.type = yyjson_get_str(yyjson_obj_get(root, "type"));
				//scl.version = root["version"].asInt();
				//scl.projectId = root["projectID"].asString();
				//scl.systemVersion = root["systemVersion"].asString();
				//scl.classType = root["classType"].asString();


				auto attrs = yyjson_obj_get(root, "attribute");
				yyjson_val* attr;
				yyjson_arr_foreach(attrs, idx, max, attr) {
					SAttribute attribute;
					attribute.code = yyjson_get_str(yyjson_obj_get(attr, "code"));
					attribute.name = yyjson_get_str(yyjson_obj_get(attr, "name"));
					attribute.description = yyjson_get_str(yyjson_obj_get(attr, "description"));
					attribute.pointType = yyjson_get_str(yyjson_obj_get(attr, "pointType"));
					attribute.type = yyjson_get_str(yyjson_obj_get(attr, "type"));
					auto attrValue = yyjson_obj_get(attr, "value");
					yyjson_val* key, * val;
					yyjson_obj_iter iter;
					yyjson_obj_iter_init(attrValue, &iter);
					while ((key = yyjson_obj_iter_next(&iter))) {
						val = yyjson_obj_iter_get_val(key);
						switch (yyjson_get_tag(val)) {
						case YYJSON_TYPE_STR | YYJSON_SUBTYPE_NONE:  attribute.value[yyjson_get_str(key)] = yyjson_get_str(val); break;
						case YYJSON_TYPE_BOOL | YYJSON_SUBTYPE_TRUE:  attribute.value[yyjson_get_str(key)] = "true"; break;
						case YYJSON_TYPE_BOOL | YYJSON_SUBTYPE_FALSE: attribute.value[yyjson_get_str(key)] = "false"; break;
						case YYJSON_TYPE_NUM | YYJSON_SUBTYPE_UINT:  attribute.value[yyjson_get_str(key)] = std::to_string(yyjson_get_uint(val)); break;
						case YYJSON_TYPE_NUM | YYJSON_SUBTYPE_SINT:  attribute.value[yyjson_get_str(key)] = std::to_string(yyjson_get_sint(val)); break;
						case YYJSON_TYPE_NUM | YYJSON_SUBTYPE_REAL:  attribute.value[yyjson_get_str(key)] = std::to_string(yyjson_get_real(val)); break;
						default:                                      attribute.value[yyjson_get_str(key)] = ""; break;
						}
					}

					scl.attributes.emplace_back(std::move(attribute));
				}
				scls[scl.classId] = std::move(scl);
			}
			else if (*classType == 'e') // classType == "edge"
			{
				SClassGateway scg;
				scg.classId = yyjson_get_str(yyjson_obj_get(root, "classID"));
				//scg.creationTime = root["creationTime"].asLargestUInt();
				//scg.modificationTime = root["modificationTime"].asLargestUInt();
				scg.name = yyjson_get_str(yyjson_obj_get(root, "name"));
				//scg.type = yyjson_get_str(yyjson_obj_get(root, "type"));
				//scg.version = root["version"].asInt();
				//scg.projectId = root["projectID"].asString();
				//scg.systemVersion = root["systemVersion"].asString();
				//scg.classType = root["classType"].asString();

				auto attrs = yyjson_obj_get(root, "attribute");
				size_t idx, max;
				yyjson_val* attr;
				yyjson_arr_foreach(attrs, idx, max, attr) {
					SAttribute attribute;
					attribute.code = yyjson_get_str(yyjson_obj_get(attr, "code"));
					attribute.name = yyjson_get_str(yyjson_obj_get(attr, "name"));
					attribute.description = yyjson_get_str(yyjson_obj_get(attr, "description"));
					attribute.pointType = yyjson_get_str(yyjson_obj_get(attr, "pointType"));
					attribute.type = yyjson_get_str(yyjson_obj_get(attr, "type"));
					auto attrValue = yyjson_obj_get(attr, "value");
					yyjson_val* key, * val;
					yyjson_obj_iter iter;
					yyjson_obj_iter_init(attrValue, &iter);
					while ((key = yyjson_obj_iter_next(&iter))) {
						val = yyjson_obj_iter_get_val(key);
						switch (yyjson_get_tag(val)) {
						case YYJSON_TYPE_STR | YYJSON_SUBTYPE_NONE:  attribute.value[yyjson_get_str(key)] = yyjson_get_str(val); break;
						case YYJSON_TYPE_BOOL | YYJSON_SUBTYPE_TRUE:  attribute.value[yyjson_get_str(key)] = "true"; break;
						case YYJSON_TYPE_BOOL | YYJSON_SUBTYPE_FALSE: attribute.value[yyjson_get_str(key)] = "false"; break;
						case YYJSON_TYPE_NUM | YYJSON_SUBTYPE_UINT:  attribute.value[yyjson_get_str(key)] = std::to_string(yyjson_get_uint(val)); break;
						case YYJSON_TYPE_NUM | YYJSON_SUBTYPE_SINT:  attribute.value[yyjson_get_str(key)] = std::to_string(yyjson_get_sint(val)); break;
						case YYJSON_TYPE_NUM | YYJSON_SUBTYPE_REAL:  attribute.value[yyjson_get_str(key)] = std::to_string(yyjson_get_real(val)); break;
						default:                                      attribute.value[yyjson_get_str(key)] = ""; break;
						}
					}

					scg.attributes.emplace_back(std::move(attribute));
				}

				scgs[scg.classId] = std::move(scg);
			}
			yyjson_doc_free(doc);
		}

		while (deviceIndexStart <= deviceIndexEnd && running_)
		{
			zip_entry_openbyindex(pzip, deviceIndexStart++);
			bufsize = zip_entry_size(pzip);
			zip_entry_noallocread(pzip, buf, bufsize);
			zip_entry_close(pzip);

			yyjson_doc* doc = yyjson_read((char*)buf, bufsize, 0);
			yyjson_val* root = yyjson_doc_get_root(doc);
			
			const char* classType = yyjson_get_str(yyjson_obj_get(root, "classType"));
			if (classType == nullptr)
			{
				zip_entry_openbyindex(pzip, deviceIndexStart - 1);
				std::cout << "entry name = " << zip_entry_name(pzip) << std::endl;
				zip_entry_close(pzip);

				std::cout << std::string(buf, buf + bufsize) << std::endl; 
				yyjson_doc_free(doc);
				continue;
			}

			if (*classType == 'p') // classType == "product"
			{
				SObjectDevice sod;
				sod.classId = yyjson_get_str(yyjson_obj_get(root, "classID"));
				//sod.creationTime = yyjson_get_uint(yyjson_obj_get(root, "creationTime"));
				//sod.modificationTime = yyjson_get_uint(yyjson_obj_get(root, "modificationTime"));
				sod.name = yyjson_get_str(yyjson_obj_get(root, "name"));
				sod.id = yyjson_get_str(yyjson_obj_get(root, "ID"));
				sod.fatherId = yyjson_get_str(yyjson_obj_get(root, "fatherID"));
				if (sod.fatherId.empty()) continue;
				//sod.version = yyjson_get_int(yyjson_obj_get(root, "version"));
				//sod.projectId = yyjson_get_str(yyjson_obj_get(root, "projectID"));
				//sod.systemVersion = yyjson_get_str(yyjson_obj_get(root, "systemVersion"));
				//sod.classType = yyjson_get_str(yyjson_obj_get(root, "classType"));
				yyjson_val* arguments = yyjson_obj_get(yyjson_obj_get(root, "protocol"), "argument");
				auto& scp = scps[sod.classId];
				std::map<std::string, DRIVER::SPointConfig> pointTable;
				std::map<int, std::string> pointPosTable;

				//针对没有协议列的情况，先遍历原型中的外部点位
				for (auto& it_attr : scp.attributes) {
					if (it_attr.second.pointType.substr(0, 8) != "internal") {
						auto& spc = pointTable[it_attr.first];
						spc.alias = it_attr.second.name;
						spc.pointType = it_attr.second.pointType;
						spc.type = it_attr.second.type;
						spc.minValue = it_attr.second.minValue;
						spc.maxValue = it_attr.second.maxValue;
						spc.coefficient = it_attr.second.coefficient;
						spc.added = it_attr.second.added;
						spc.description = it_attr.second.description;
						pointPosTable[it_attr.second.pos] = it_attr.first;
					}
				}

				yyjson_val* key, * val;
				yyjson_obj_iter iter;
				yyjson_obj_iter_init(arguments, &iter);
				while (key = yyjson_obj_iter_next(&iter)) {
					val = yyjson_obj_iter_get_val(key);
					std::string pointName = yyjson_get_str(key);
					auto it_point = pointTable.find(pointName);
					if (it_point == pointTable.end())
						continue;

					DRIVER::OriginProtocol pointProtocol;
					yyjson_val* argKey, * argVal;
					yyjson_obj_iter argIter;
					yyjson_obj_iter_init(val, &argIter);
					while ((argKey = yyjson_obj_iter_next(&argIter))) {
						argVal = yyjson_obj_iter_get_val(argKey);
						switch (yyjson_get_tag(argVal)) {
						case YYJSON_TYPE_STR | YYJSON_SUBTYPE_NONE:  pointProtocol[yyjson_get_str(argKey)] = yyjson_get_str(argVal); break;
						case YYJSON_TYPE_BOOL | YYJSON_SUBTYPE_TRUE:  pointProtocol[yyjson_get_str(argKey)] = "true"; break;
						case YYJSON_TYPE_BOOL | YYJSON_SUBTYPE_FALSE: pointProtocol[yyjson_get_str(argKey)] = "false"; break;
						case YYJSON_TYPE_NUM | YYJSON_SUBTYPE_UINT:  pointProtocol[yyjson_get_str(argKey)] = std::to_string(yyjson_get_uint(argVal)); break;
						case YYJSON_TYPE_NUM | YYJSON_SUBTYPE_SINT:  pointProtocol[yyjson_get_str(argKey)] = std::to_string(yyjson_get_sint(argVal)); break;
						case YYJSON_TYPE_NUM | YYJSON_SUBTYPE_REAL:  pointProtocol[yyjson_get_str(argKey)] = std::to_string(yyjson_get_real(argVal)); break;
						default:                                      pointProtocol[yyjson_get_str(argKey)] = ""; break;
						}
					}
					it_point->second.pointProtocol = std::move(pointProtocol);
				}

				//记录在原型中的顺序
				int pos_idx = 0;
				for (auto it_pos : pointPosTable) {
					pointTable[it_pos.second].pos = pos_idx++;
				}

				//内部点
				std::map<std::string, DRIVER::SPointConfig> innerPointTable;
				for (auto& it_attr : scp.attributes) {
					if (it_attr.second.pointType.substr(0, 8) == "internal") {
						auto& spc = innerPointTable[it_attr.first];
						spc.alias = it_attr.second.name;
						spc.pointType = it_attr.second.pointType;
						spc.type = it_attr.second.type;
						spc.minValue = it_attr.second.minValue;
						spc.maxValue = it_attr.second.maxValue;
						spc.coefficient = it_attr.second.coefficient;
						spc.added = it_attr.second.added;
						spc.description = it_attr.second.description;
					}
				}

				deviceIdVec_.push_back(sod.id);
				ChannelIdVec_.push_back(sod.fatherId);
				auto& dev = sccs[sod.fatherId].deviceTable[sod.id];
				dev.name = std::move(sod.id);
				dev.alias = std::move(sod.name);
				dev.pointTable = std::move(pointTable);
				dev.innerPointTable = std::move(innerPointTable);
			}
			else if (*classType == 'l') // classType == "link"
			{
				SObjectLink sol;
				sol.classId = yyjson_get_str(yyjson_obj_get(root, "classID"));
				//sol.creationTime = yyjson_get_uint(yyjson_obj_get(root, "creationTime"));
				//sol.modificationTime = yyjson_get_uint(yyjson_obj_get(root, "modificationTime"));
				sol.name = yyjson_get_str(yyjson_obj_get(root, "name"));
				sol.id = yyjson_get_str(yyjson_obj_get(root, "ID"));
				sol.fatherId = yyjson_get_str(yyjson_obj_get(root, "fatherID"));
				//sol.version = yyjson_get_int(yyjson_obj_get(root, "version"));
				//sol.projectId = yyjson_get_str(yyjson_obj_get(root, "projectID"));
				//sol.systemVersion = yyjson_get_str(yyjson_obj_get(root, "systemVersion"));
				//sol.classType = yyjson_get_str(yyjson_obj_get(root, "classType"));
				yyjson_val* args = yyjson_obj_get(root, "connection");
				yyjson_val* argKey, * argVal;
				yyjson_obj_iter iter;
				yyjson_obj_iter_init(args, &iter);
				while ((argKey = yyjson_obj_iter_next(&iter))) {
					argVal = yyjson_obj_iter_get_val(argKey);
					switch (yyjson_get_tag(argVal)) {
					case YYJSON_TYPE_STR | YYJSON_SUBTYPE_NONE:  sccs[sol.id].channelProtocol[yyjson_get_str(argKey)] = yyjson_get_str(argVal); break;
					case YYJSON_TYPE_BOOL | YYJSON_SUBTYPE_TRUE:  sccs[sol.id].channelProtocol[yyjson_get_str(argKey)] = "true"; break;
					case YYJSON_TYPE_BOOL | YYJSON_SUBTYPE_FALSE: sccs[sol.id].channelProtocol[yyjson_get_str(argKey)] = "false"; break;
					case YYJSON_TYPE_NUM | YYJSON_SUBTYPE_UINT:  sccs[sol.id].channelProtocol[yyjson_get_str(argKey)] = std::to_string(yyjson_get_uint(argVal)); break;
					case YYJSON_TYPE_NUM | YYJSON_SUBTYPE_SINT:  sccs[sol.id].channelProtocol[yyjson_get_str(argKey)] = std::to_string(yyjson_get_sint(argVal)); break;
					case YYJSON_TYPE_NUM | YYJSON_SUBTYPE_REAL:  sccs[sol.id].channelProtocol[yyjson_get_str(argKey)] = std::to_string(yyjson_get_real(argVal)); break;
					default:                                      sccs[sol.id].channelProtocol[yyjson_get_str(argKey)] = ""; break;
					}
				}

				const auto& channelProtocolIter = sccs[sol.id].channelProtocol.find("Cycle");
				if (channelProtocolIter != sccs[sol.id].channelProtocol.end())
				{
					sccs[sol.id].enableUpdateDataOnChanged = false;
					sccs[sol.id].enableUpdateStatusByDriver = true;
				}

				if (scls[sol.classId].scriptType.compare("javascript") == 0)
				{
					sccs[sol.id].driverType = "DriverJS";
					sccs[sol.id].channelProtocol["_javascriptExpresstion"] = scls[sol.classId].javascriptExpresstion; //js脚本放到连接参数
				}
				else 
				{
					sccs[sol.id].driverType = scls[sol.classId].driverFile.begin()->second;
				}
				sccs[sol.id].name = sol.id;
				sccs[sol.id].alias = std::move(sol.name);
				sccs[sol.id].className = scls[sol.classId].name;
				sccs[sol.id].argumentStr = scls[sol.classId].argumentStr;
				sccs[sol.id].connectionStr = scls[sol.classId].connectionStr;
			}
			else if (*classType == 'e') // classType == "edge"
			{
				sog.classId = yyjson_get_str(yyjson_obj_get(root, "classID"));
				sog.id = yyjson_get_str(yyjson_obj_get(root, "ID"));
				//sog.creationTime = yyjson_get_uint(yyjson_obj_get(root, "creationTime"));
				//sog.modificationTime = yyjson_get_uint(yyjson_obj_get(root, "modificationTime"));
				sog.name = yyjson_get_str(yyjson_obj_get(root, "name"));
				//sog.version = yyjson_get_int(yyjson_obj_get(root, "version"));
				//sog.projectId = yyjson_get_str(yyjson_obj_get(root, "projectID"));
				//sog.systemVersion = yyjson_get_str(yyjson_obj_get(root, "systemVersion"));
				//sog.classType = yyjson_get_str(yyjson_obj_get(root, "classType"));
				yyjson_val* mqttArg = yyjson_obj_get(root, "mqtt");
				sog.mqttClientID = yyjson_get_str(yyjson_obj_get(mqttArg, "clientID"));
				sog.mqttPassword = yyjson_get_str(yyjson_obj_get(mqttArg, "password"));
				sog.mqttUsername = yyjson_get_str(yyjson_obj_get(mqttArg, "username"));

				sog.className = scgs[sog.classId].name;
			}

			yyjson_doc_free(doc);
		}

		zip_close(pzip);
		delete[] buf;

		return true;
	}

	bool CGatewayAIOT::loadLocalConfig(SObjectGateway& sog, std::map<std::string, DRIVER::SChannelConfig>& sccs)
	{
		logger_->info("loadLocalConfig\n");

		sccs.clear();
		opcuaPointsMap_.clear();

		std::string configPath = CPathManager::getInstance().getLocalConfigPath();
		std::ifstream ifs(configPath);
		if (!ifs.is_open())
		{
			logger_->error("open localConfig file failed\n");
			return false;
		}

		std::istreambuf_iterator<char> beg(ifs), end;
		std::string content(beg, end);
		ifs.close();

		//std::cout << "localconfig:" << content << std::endl;
		if (!StringToJsonValue(content, localConfigJson_))
		{
			logger_->error("parse localConfig failed\n");
			return false;
		}

		sog.id = localConfigJson_["id"].asString();
		sog.name = localConfigJson_["name"].asString();

		Json::Value links = localConfigJson_["link"];
		for (auto it = links.begin(); it != links.end(); it++)
		{
			std::string driverName = it.name();
			std::string driverFile = (*it)["driverFile"].begin()->asString();

			if (localLinkTemplates_.find(driverName) == localLinkTemplates_.end())
			{
				std::cout << "driver not found : " << driverName << std::endl;
				continue;
			}
			auto linkTemplate = localLinkTemplates_[driverName];
			for (auto instanceIt : (*it)["instances"])
			{
				DRIVER::SChannelConfig channel;
				channel.logSize = logFileSize_;
				channel.logBaseSize = logBaseSize_;
				channel.logCount = logFileCount_;
				channel.logLevel = logLevel_;
				channel.argumentStr = linkTemplate.argumentStr;
				channel.connectionStr = linkTemplate.connectionStr;
				channel.driverType = driverFile;
				channel.className = driverName;
				channel.name = instanceIt["id"].asString();
				channel.alias = instanceIt["name"].asString();

				for (auto cp : instanceIt["protocol"])
				{
					for (auto& connIt : linkTemplate.connection)
					{
						if (strcmp(connIt.name.c_str(), cp["name"].asString().c_str()) == 0)
						{
							channel.channelProtocol[cp["name"].asString()] = cp["value"].asString();
						}
					}
				}

				for (auto jDevice : instanceIt["devices"])
				{
					DRIVER::SDeviceConfig device;
					device.name = jDevice["id"].asString();
					device.alias = jDevice["name"].asString();

					for (auto jPoint : jDevice["points"])
					{
						DRIVER::SPointConfig point;
						point.name = jPoint["code"].asString();
						point.alias = jPoint["name"].asString();
						point.pointType = "stateControl";
						point.type = "AIO";
						for (auto pp : jPoint["protocol"])
						{
							for (auto& argIt : linkTemplate.arguments)
							{
								if (strcmp(argIt.name.c_str(), pp["name"].asString().c_str()) == 0)
								{
									point.pointProtocol[pp["name"].asString()] = pp["value"].asString();
								}
							}
						}

						device.pointTable[jPoint["id"].asString()] = point;
					}

					for (auto& dp : jDevice["protocol"])
					{
						for (auto& connIt : linkTemplate.connection)
						{
							if (strcmp(connIt.name.c_str(), dp["name"].asString().c_str()) == 0)
							{
								channel.channelProtocol[dp["name"].asString()] = dp["value"].asString();
							}
						}

						for (auto& argIt : linkTemplate.arguments)
						{
							if (strcmp(argIt.name.c_str(), dp["name"].asString().c_str()) == 0)
							{
								for (auto& pit : device.pointTable)
								{
									pit.second.pointProtocol[dp["name"].asString()] = dp["value"].asString();
								}
							}
						}
					}

					channel.deviceTable[device.name] = device;
				}

				slcs_[channel.name] = channel;
			}
		}
		
		for (auto lit : slcs_)
		{
			for (auto dit : lit.second.deviceTable)
			{
				for (auto pit : dit.second.pointTable)
				{
					opcuaPointsMap_[lit.second.alias][dit.second.alias].insert(pit.second.name);
				}
			}
		}

		for (auto it = slcs_.begin(); it != slcs_.end(); it++)
		{
			it->second.logSize = logFileSize_;
			it->second.logBaseSize = logBaseSize_;
			it->second.logCount = logFileCount_;
			it->second.logLevel = logLevel_;
		}

		totalLinkCount_ = 0;
		totalDeviceCount_ = 0;
		innerPointCount_ = 0;
		normalPointCount_ = 0;
		totalLinkCount_ = slcs_.size();
		for (auto it = slcs_.begin(); it != slcs_.end(); it++)
		{
			totalDeviceCount_ += it->second.deviceTable.size();

			for (auto dit = it->second.deviceTable.begin(); dit != it->second.deviceTable.end(); dit++)
			{
				innerPointCount_ += dit->second.innerPointTable.size();
				normalPointCount_ += dit->second.pointTable.size();
			}
		}

		opcServer_.addVariable(opcuaPointsMap_);

		logger_->info("Local config loaded: {} channels", sccs.size());

		return true;

#pragma region oldLoad

		// 解析JSON
		yyjson_doc* doc = yyjson_read(content.c_str(), content.length(), 0);
		if (!doc) {
			logger_->error("parse localConfig failed\n");
			return false;
		}

		yyjson_val* root = yyjson_doc_get_root(doc);

		// 解析网关基本信息
		sog.id = yyjson_get_str(yyjson_obj_get(root, "id"));
		sog.name = yyjson_get_str(yyjson_obj_get(root, "name"));

		// 解析连接配置
		yyjson_val* link = yyjson_obj_get(root, "link");
		yyjson_val* key, * val;
		yyjson_obj_iter iter;

		// 遍历所有驱动类型 (ModbusTCP, ModbusRTU等)
		yyjson_obj_iter_init(link, &iter);
		while ((key = yyjson_obj_iter_next(&iter))) {
			const char* driver_class = yyjson_get_str(key);
			yyjson_val* driver_info = yyjson_obj_iter_get_val(key);

			// 获取驱动文件名
			std::string driver_type;
			yyjson_val* driver_file = yyjson_obj_get(driver_info, "driverFile");
			yyjson_val* driver_file_key;
			yyjson_obj_iter driver_file_iter;
			yyjson_obj_iter_init(driver_file, &driver_file_iter);
			while ((driver_file_key = yyjson_obj_iter_next(&driver_file_iter))) {
				const char* key = yyjson_get_str(driver_file_key);
				driver_type = yyjson_get_str(yyjson_obj_get(driver_file, key));
				break;
			}

			yyjson_val* channels = yyjson_obj_get(driver_info, "instances");

			// 遍历该驱动类型下的所有通道
			size_t idx, max;
			yyjson_val* channel;
			yyjson_arr_foreach(channels, idx, max, channel) {
				// 创建通道配置
				//DRIVER::SChannelConfig channel_cfg;
				//channel_cfg.className = driver_class;
				//channel_cfg.driverType = driver_type; // 例如 "ModbusTCP"

				// 通道ID和名称
				const char* localLink_id = yyjson_get_str(yyjson_obj_get(channel, "id"));
				const char* localLink_name = yyjson_get_str(yyjson_obj_get(channel, "name"));

				//channel_cfg.name = channel_id;
				//channel_cfg.alias = yyjson_get_str(yyjson_obj_get(channel, "name"));

				// 解析通道参数
				DRIVER::OriginProtocol channelProtocol;
				yyjson_val* protocol = yyjson_obj_get(channel, "protocol");
				yyjson_val* proto_item;
				size_t proto_idx, proto_max;
				yyjson_arr_foreach(protocol, proto_idx, proto_max, proto_item) {
					const char* name = yyjson_get_str(yyjson_obj_get(proto_item, "name"));
					//const char* value = yyjson_get_str(yyjson_obj_get(proto_item, "value"));
					auto value = yyjson_val_to_string(yyjson_obj_get(proto_item, "value"));
					channelProtocol[name] = value;
				}

				// 解析设备配置
				yyjson_val* devices = yyjson_obj_get(channel, "devices");
				yyjson_val* device;
				size_t device_idx, device_max;
				yyjson_arr_foreach(devices, device_idx, device_max, device) {
					DRIVER::SChannelConfig channel_cfg;
					channel_cfg.className = driver_class;
					channel_cfg.driverType = driver_type;
					channel_cfg.name = yyjson_get_str(yyjson_obj_get(device, "id"));
					channel_cfg.alias = yyjson_get_str(yyjson_obj_get(device, "name"));
					channel_cfg.channelProtocol = channelProtocol;
					channel_cfg.localConfigLinkId = localLink_id;
					channel_cfg.localConfigLinkName = localLink_name;

					DRIVER::SDeviceConfig dev_cfg;
					dev_cfg.name = "device";
					dev_cfg.alias = "device";

					DRIVER::OriginProtocol pointProtocol;
					// 设备协议参数
					yyjson_val* dev_proto = yyjson_obj_get(device, "protocol");
					yyjson_val* dev_proto_item;
					size_t proto2_idx, proto2_max;
					yyjson_arr_foreach(dev_proto, proto2_idx, proto2_max, dev_proto_item) {
						const char* name = yyjson_get_str(yyjson_obj_get(dev_proto_item, "name"));
						//const char* value = yyjson_get_str(yyjson_obj_get(dev_proto_item, "value"));
						auto value = yyjson_val_to_string(yyjson_obj_get(dev_proto_item, "value"));
						//dev_cfg.deviceProtocol[name] = value;
						//判断是链路的连接参数还是点位的协议参数
						auto it_template = localLinkTemplates_.find(channel_cfg.className);
						if (it_template == localLinkTemplates_.end()) {
							logger_->error("cant find template link: {0}", channel_cfg.name);
							continue;
						}
						for (auto& it : it_template->second.connection) {
							if (strcmp(it.name.c_str(), name) == 0) {
								channel_cfg.channelProtocol[name] = value;
							}
						}
						for (auto& it : it_template->second.arguments) {
							if (strcmp(it.name.c_str(), name) == 0) {
								pointProtocol[name] = value;
							}
						}
					}

					// 解析点位配置
					yyjson_val* points = yyjson_obj_get(device, "points");
					yyjson_val* point;
					size_t point_idx, point_max;
					yyjson_arr_foreach(points, point_idx, point_max, point) {
						DRIVER::SPointConfig pt_cfg;
						std::string pointID = yyjson_get_str(yyjson_obj_get(point, "id"));
						pt_cfg.name = yyjson_get_str(yyjson_obj_get(point, "code"));
						pt_cfg.alias = yyjson_get_str(yyjson_obj_get(point, "name"));
						pt_cfg.pointType = "stateControl";
						pt_cfg.type = "AIO";
						
						// 将设备协议参数合并到点位协议
						pt_cfg.pointProtocol = pointProtocol;

						// 点位协议参数
						yyjson_val* pt_proto = yyjson_obj_get(point, "protocol");
						yyjson_val* pt_proto_item;
						size_t pt_proto_idx, pt_proto_max;
						yyjson_arr_foreach(pt_proto, pt_proto_idx, pt_proto_max, pt_proto_item) {
							const char* name = yyjson_get_str(yyjson_obj_get(pt_proto_item, "name"));
							auto value = yyjson_val_to_string(yyjson_obj_get(pt_proto_item, "value"));
							pt_cfg.pointProtocol[name] = value;
						}

						dev_cfg.pointTable[pointID] = pt_cfg;
					}

					channel_cfg.deviceTable[dev_cfg.name] = dev_cfg;

					sccs[channel_cfg.name] = channel_cfg;
				}
			}
		}

		// 清理资源
		yyjson_doc_free(doc);

		for (auto device : slcs_)
		{
			std::string lName = device.second.localConfigLinkName;
			std::string dName = device.second.alias;
			for (auto vDevice : device.second.deviceTable)
			{
				for (auto points : vDevice.second.pointTable)
				{
					std::string pName = points.second.name;
					opcuaPointsMap_[lName][dName].insert(pName);
				}
			}
		}

		for (auto it = slcs_.begin(); it != slcs_.end(); it++)
		{
			it->second.logSize = logFileSize_;
			it->second.logBaseSize = logBaseSize_;
			it->second.logCount = logFileCount_;
			it->second.logLevel = logLevel_;
		}

		opcServer_.addVariable(opcuaPointsMap_);

		logger_->info("Local config loaded: {} channels", sccs.size());
		return true;
#pragma endregion
	}

	void CGatewayAIOT::localIEMSLedControl()
	{
		int flag = 1;
		while (!localIEMSLedExitControl_)
		{
			CiEMSSetting::getInstance().controlRunningStateLed(EIEMSRunningStateLed::Run, flag);
			//std::cout << __func__ << " set run to " << std::to_string(flag) << std::endl;
			if (flag == 1)
				flag = 0;
			else if (flag == 0)
				flag = 1;
			std::this_thread::sleep_for(std::chrono::seconds(1));
		}

		CiEMSSetting::getInstance().controlRunningStateLed(EIEMSRunningStateLed::Run, 0);
	}

	void CGatewayAIOT::getLocalConfigRunningState()
	{
		std::lock_guard<std::mutex> lg(localConfigRunningStateMtx_);
		std::string filePath = CPathManager::getInstance().getLocalConfigStatePath();

		std::ifstream ifs(filePath);
		if (ifs.is_open())
		{
			JSONCPP_STRING err;
			Json::Value root;
			Json::CharReaderBuilder builder;
			if (Json::parseFromStream(builder, ifs, &root, &err))
			{
				localConfigDriverRunningState_ = root["localConfigDriverRunningState"].asBool();
				localConfigOPCUARunningState_ = root["localConfigOPCUARunningState"].asBool();
				opcuaServerEndpoint_ = root["opcuaServerEndpoint"].asString();
			}
			ifs.close();
		}
		//std::cout << localConfigDriverRunningState_ << "   " << localConfigOPCUARunningState_ << std::endl;
	}

	void CGatewayAIOT::setLocalConfigRunningState()
	{
		std::lock_guard<std::mutex> lg(localConfigRunningStateMtx_);
		std::string filePath = CPathManager::getInstance().getLocalConfigStatePath();

		Json::Value root;
		root["localConfigDriverRunningState"] = localConfigDriverRunningState_;
		root["localConfigOPCUARunningState"] = localConfigOPCUARunningState_;
		root["opcuaServerEndpoint"] = opcuaServerEndpoint_;

		std::ofstream ofs(filePath);
		if (ofs.is_open())
		{
			std::string data = root.toStyledString();
			ofs.write(data.c_str(), data.size());
			ofs.close();
		}
		return;
	}

	bool CGatewayAIOT::startLocalConfigWork()
	{
		if (configSource_ == EConfigurationSource::ELocal &&
			!running_.load())
		{
			running_.exchange(true);

			if (updateDataToOpcuaServerThread_ == nullptr)
			{
				updateDataToOpcuaServerThread_ = new std::thread(&CGatewayAIOT::updateDataToOpcuaServerThread, this);
			}
			configure(slcs_);

			return true;
		}
		return false;
	}

	void CGatewayAIOT::startLocalConfigOPCUAWrok()
	{
		opcServer_.init(opcuaServerEndpoint_);
		opcServer_.start();
	}

	std::string CGatewayAIOT::yyjson_val_to_string(yyjson_val* val)
	{
		if (!val) return "";

		yyjson_type type = yyjson_get_type(val);

		switch (type) {
		case YYJSON_TYPE_STR: {
			return std::string(yyjson_get_str(val), yyjson_get_len(val));
		}
		case YYJSON_TYPE_NUM: {
			if (yyjson_is_int(val)) {
				return std::to_string(yyjson_get_int(val));
			}
			else if (yyjson_is_uint(val)) {
				return std::to_string(yyjson_get_uint(val));
			}
			else {
				return std::to_string(yyjson_get_real(val));
			}
		}
		case YYJSON_TYPE_BOOL:
			return yyjson_get_bool(val) ? "true" : "false";
		case YYJSON_TYPE_NULL:
			return "null";
		case YYJSON_TYPE_ARR:
		case YYJSON_TYPE_OBJ: {
			// 如果是数组或对象，使用 JSON 序列化
			char* json_str = yyjson_val_write(val, 0, nullptr);
			if (json_str) {
				std::string result(json_str);
				free(json_str);
				return result;
			}
			return "";
		}
		default:
			return "";
		}
	}

	std::string CGatewayAIOT::control(SCloudMessageSptr cloudMessageSptr)
	{
		std::string ret;
		JSONCPP_STRING err;
		Json::Value root;
		Json::CharReaderBuilder builder;
		std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
		if (!reader->parse(cloudMessageSptr->payload.c_str(), cloudMessageSptr->payload.c_str() + cloudMessageSptr->payload.size(), &root, &err))
		{
			//std::cout << "parse comming message error = " << err << std::endl;
			root["response"] = 106;
			root["result"] = "failed";
			ret = "failed";
			return cloudMessageSptr->topic.empty() ? ret : root.toStyledString();
		} 

		if (!cloudMessageSptr->topic.empty() && !enableCloudControl_)
		{
			Json::Value res;
			res["response"] = 106;
			res["result"] = "control is disabled by edge";
			res["sequence"] = root["sequence"].asInt();

			std::string deviceID = cloudMessageSptr->topic;
			deviceID.replace(deviceID.rfind(TOPIC_TYPE_CONTROL) - 1, strlen(TOPIC_TYPE_CONTROL) + 1, "");
			mainPlatform_.mqttClient->publish(deviceID + "/control/response", res.toStyledString());

			root["response"] = 106;
			root["result"] = "control is disabled by edge";
			return root.toStyledString();
		}

		/*
		single point :
			{
				"type" : "control",
				"point" : "AO1",
				"value" : 1,
				"sequence" : 24,
				"timestamp" : 1713146793546,
				"timeout" : 10
			}

		 batch point :
			{
				"controlDatas": [
					{
						"type": "control",
						"point" : "1",
						"value" : 1,
						"sequence" : 16,
						"timestamp" : 1712890336745,
						"timeout" : 10
					},
					{
						"type": "control",
						"point" : "2",
						"value" : 2,
						"sequence" : 16,
						"timestamp" : 1712890336745,
						"timeout" : 10
					}
				]
			}
		*/

		bool b = false;
		if (!root.isMember("controlDatas"))
		{
			// single point
			DRIVER::SCtrlInfo sci;
			sci.raw = cloudMessageSptr->payload;
			convertSinglePointControl(root, sci);
			sci.source = cloudMessageSptr->cloudID;
			if (cloudMessageSptr->topic.empty()) // web
			{
				sci.async = false;
				sci.channelName = root["linkid"].asString();
				sci.deviceName = root["deviceid"].asString();
				sci.pointName = root["code"].asString();
			}
			else // mqtt
			{
				sci.async = true;
				sci.deviceName = cloudMessageSptr->topic;
				sci.deviceName.replace(sci.deviceName.rfind(TOPIC_TYPE_CONTROL) - 1, strlen(TOPIC_TYPE_CONTROL) + 1, "");
				sci.pointName = root["point"].asString();
				sci.sequence = root["sequence"].asInt();
			}
			// 链路
			std::string channelName = cloudMessageSptr->topic.find('/') != std::string::npos ? cloudMessageSptr->topic.substr(0, cloudMessageSptr->topic.find('/')) : cloudMessageSptr->topic;
			auto slc = slcs_.find(channelName);
			if (slc == slcs_.end())
			{
				std::vector<DRIVER::SCtrlInfo> scis{ sci };
				b = driverEngine_->control(scis);
				//root["response"] = b ? 100 : 106;
				//root["result"] = b ? "succeed" : "failed";
				//ret = b ? "succeed" : "failed";
				//mainPlatform_.mqttClient->publish(sci.deviceName + "/control/response", root.toStyledString());
			}
			else
			{
				sci.channelName = slc->first;
				b = onEngineMqttControl(sci);
			}

		}
		else
		{
			// batch point
			std::vector<DRIVER::SCtrlInfo> scis;
			convertBatchPointControl(root, scis);

			if (cloudMessageSptr->cloudID.empty())
			{
				b = false;
				return "failed";
			}

			for (auto sci = scis.begin(); sci != scis.end(); sci++)
			{
				sci->source = cloudMessageSptr->cloudID;
				sci->async = true;

				sci->deviceName = cloudMessageSptr->topic;
				sci->deviceName.replace(sci->deviceName.rfind(TOPIC_TYPE_CONTROL) - 1, strlen(TOPIC_TYPE_CONTROL) + 1, "");
			}
			// 链路
			std::string channelName = cloudMessageSptr->topic.find('/') != std::string::npos ? cloudMessageSptr->topic.substr(0, cloudMessageSptr->topic.find('/')) : cloudMessageSptr->topic;
			auto slc = slcs_.find(channelName);
			if (slc == slcs_.end())
			{
				b = driverEngine_->control(scis);
				//root["response"] = b ? 100 : 106;
				//root["result"] = b ? "succeed" : "failed";
				//ret = b ? "succeed" : "failed";
				//mainPlatform_.mqttClient->publish(scis[0].deviceName + "/control/response", root.toStyledString());
			}
			else
			{
				//for (auto& sci = scis.begin(); sci != scis.end(); sci++)
				//{
				//	sci->channelName = slc->first;
				//}
			}
		}
		
		// b = driverEngine_->control(sci);
		root["response"] = b ? 100 : 106;
		root["result"] = b ? "succeed" : "failed";
		ret = b ? "succeed" : "failed";
		return cloudMessageSptr->topic.empty() ? ret : root.toStyledString();
	}

	std::string CGatewayAIOT::controlSet(SCloudMessageSptr cloudMessageSptr)
	{
		// control json --> std::unordered_map<std::string,std::unordered_map<SCtrlInfo::Etype, SCtrlInfo>>
		 
		//std::cout << __func__ << std::endl;
		//std::cout << cloudMessageSptr->cloudID << std::endl;
		//std::cout << cloudMessageSptr->topic << std::endl;
		//std::cout << cloudMessageSptr->payload << std::endl;

		std::string channelName = cloudMessageSptr->topic.find('/') != std::string::npos ? cloudMessageSptr->topic.substr(0, cloudMessageSptr->topic.find('/')) : cloudMessageSptr->topic;
		auto slc = slcs_.find(channelName);
		if (slc == slcs_.end())
		{
			std::cout << "can not find channel : " << channelName << std::endl;
			return std::string();
		}
		//std::cout << "find channel : " << channelName << std::endl;

		std::string ret;
		JSONCPP_STRING err;
		Json::Value root;
		Json::CharReaderBuilder builder;
		std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
		if (!reader->parse(cloudMessageSptr->payload.c_str(), cloudMessageSptr->payload.c_str() + cloudMessageSptr->payload.size(), &root, &err))
		{
			//std::cout << "parse comming message error = " << err << std::endl;
			//root["response"] = 106;
			//root["result"] = "failed";
			//ret = "failed";
			//return cloudMessageSptr->topic.empty() ? ret : root.toStyledString();

			std::cout << "parse json error." << std::endl;
			return std::string();
		}

		int sequence = root["sequence"].asInt64();
		int timestamp = root["timestamp"].asUInt64();
		int timeout = root["timeout"].asInt64();

		if (!root.isMember("controlSet") || root["controlSet"].type() != Json::objectValue)
		{
			std::cout << "wrong controlSet object." << std::endl;
			return std::string();
		}
		Json::Value controlSetObj = root["controlSet"];

		std::unordered_map<std::string, std::unordered_map<DRIVER::SCtrlInfo::EType, std::vector<DRIVER::SCtrlInfo>>> ctrlInfos;
		for (auto dit = controlSetObj.begin(); dit != controlSetObj.end(); dit++)
		{
			//std::cout << dit.name() << std::endl;
			Json::Value pcis = controlSetObj[dit.name()];
			if (pcis.type() != Json::arrayValue)
				continue;
			for (auto& pci : pcis)
			{
				DRIVER::SCtrlInfo ci;
				ci.async = true;
				ci.source = cloudMessageSptr->cloudID;
				ci.sequence = sequence;
				ci.timeout = timeout;
				ci.deviceName = dit.name();
				ci.pointName = pci["point"].asString();
				ci.channelName = channelName;
				if (convertSinglePointControl(pci, ci))
				{
					ctrlInfos[dit.name()][ci.type].push_back(ci);
				}
			}
		}
		driverEngine_->controlSet(channelName, ctrlInfos);
		return std::string();
	}

	bool CGatewayAIOT::convertSinglePointControl(Json::Value& root, DRIVER::SCtrlInfo& sci)
	{
		const auto& jv = root["value"];
		switch (jv.type())
		{
		case Json::intValue:
			sci.value = jv.asInt();
			break;
		case Json::uintValue:
			sci.value = jv.asUInt();
			break;
		case Json::realValue:
			sci.value = jv.asFloat();
			break;
		case Json::stringValue:
			sci.value = jv.asString();
			break;
		case Json::booleanValue:
			sci.value = jv.asBool();
			break;
		default:
			break;
		}

		std::string type = root["type"].asString();
		if (type == "control")
		{
			sci.type = DRIVER::SCtrlInfo::eWrite;
		}
		else if (type == "scanoff")
		{
			if (sci.value.asBool())
			{
				sci.type = DRIVER::SCtrlInfo::eOfflinePoint;
			}
			else
			{
				sci.type = DRIVER::SCtrlInfo::eOnlinePoint;
			}

		}
		else if (type == "manset")
		{
			sci.type = DRIVER::SCtrlInfo::emanset;
		}
		else if (type == "TLSM")
		{
			sci.type = DRIVER::SCtrlInfo::eTLSM;
		}
		else if (type == "KZYZ")
		{
			sci.type = DRIVER::SCtrlInfo::eKZYZ;
		}
		else if (type == "JDGP")
		{
			sci.type = DRIVER::SCtrlInfo::eJDGP;
		}
		else if (type == "WXGP")
		{
			sci.type = DRIVER::SCtrlInfo::eWXGP;
		}

		if (root.isMember("timeout"))
		{
			sci.timeout = root["timeout"].asUInt64();
		}
		return true;
	}

	bool CGatewayAIOT::convertBatchPointControl(Json::Value& root, std::vector<DRIVER::SCtrlInfo>& scis)
	{
		auto& controlDatas = root["controlDatas"];
		if (controlDatas.type() != Json::arrayValue)
			return false;

		for (auto it = controlDatas.begin(); it != controlDatas.end(); it++)
		{
			DRIVER::SCtrlInfo sci;
			sci.raw = it->toStyledString();
			const auto& jv = (*it)["value"];
			switch (jv.type())
			{
			case Json::intValue:
				sci.value = jv.asInt();
				break;
			case Json::uintValue:
				sci.value = jv.asUInt();
				break;
			case Json::realValue:
				sci.value = jv.asFloat();
				break;
			case Json::stringValue:
				sci.value = jv.asString();
				break;
			case Json::booleanValue:
				sci.value = jv.asBool();
				break;
			default:
				break;
			}

			std::string type = (*it)["type"].asString();
			if (type == "control")
			{
				sci.type = DRIVER::SCtrlInfo::eWrite;
			}
			else if (type == "scanoff")
			{
				if (sci.value.asBool())
				{
					sci.type = DRIVER::SCtrlInfo::eOfflinePoint;
				}
				else
				{
					sci.type = DRIVER::SCtrlInfo::eOnlinePoint;
				}

			}
			else if (type == "manset")
			{
				sci.type = DRIVER::SCtrlInfo::emanset;
			}
			else if (type == "TLSM")
			{
				sci.type = DRIVER::SCtrlInfo::eTLSM;
			}
			else if (type == "KZYZ")
			{
				sci.type = DRIVER::SCtrlInfo::eKZYZ;
			}
			else if (type == "JDGP")
			{
				sci.type = DRIVER::SCtrlInfo::eJDGP;
			}
			else if (type == "WXGP")
			{
				sci.type = DRIVER::SCtrlInfo::eWXGP;
			}

			if (it->isMember("point"))
				sci.pointName = (*it)["point"].asString();
			if (it->isMember("sequence"))
				sci.sequence = (*it)["sequence"].asInt();
			if (it->isMember("timeout"))
				sci.timeout = (*it)["timeout"].asUInt64();

			scis.push_back(sci);
		}
		return true;
	}

	bool CGatewayAIOT::insertLog(ELogType type, const std::string& content)
	{
		//std::cout << content << std::endl;

		SLog log;
		log.type = type;
		log.createTime = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
		log.content = content;
		SLogMessage *lm = new SLogMessage();
		lm->type = eLogInsert;
		lm->lim.vLog.emplace_back(std::move(log));
		lc_->pushLog(lm);
		return true;
	}

	Json::Value CGatewayAIOT::getLocalEnv()
	{
		if (checkEnv(EGatewayEnv::E500))
		{
			env_ = EGatewayEnv::E500;
		}
		else if (checkEnv(EGatewayEnv::iEMSCOM4))
		{
			env_ = EGatewayEnv::iEMSCOM4;
		}
		else if (isMultiGatewayEnv)
		{
			env_ = EGatewayEnv::Multi;
		}
		else
		{
			env_ = EGatewayEnv::Normal;
		}

		// unuse
		Json::Value root;
		const char* env_var = std::getenv("ECITYOS");
		root["ecityos"] = env_var == nullptr ? std::string() : std::string(env_var);
		return root;
	}

	bool CGatewayAIOT::checkEnv(EGatewayEnv env)
	{
			switch (env)
			{
			case EGatewayEnv::E500: 
			{
				std::string serialNumber = sog_.className;
				if (getSerialNumber(serialNumber))
					return true;
			}
			break;
			case EGatewayEnv::iEMSCOM4: 
			{
				loadIEMSCfg();
				return iemsConfig_.product == envToString.at(env);

				//std::map<std::string, std::string> kv_map;
				//if (UTILS::readkvMapFiles(HARDWARE_EMSBOX_CFG_ENV, kv_map))
				//{
				//	if (kv_map.find("product") != kv_map.end() && kv_map.at("product") == envToString.at(env))
				//		return true;
				//}
			}
			break;
			default:
				break;
			}
		return false;

		//if (deviceModel == "e500")
		//{
		//	std::string serialNumber = sog_.className;
		//	if (getSerialNumber(serialNumber))
		//		return true;
		//}
		//else if (deviceModel == "emsbox")
		//{
		//	std::map<std::string, std::string> kv_map;

		//	if (UTILS::readkvMapFiles(HARDWARE_EMSBOX_CFG_ENV, kv_map))
		//	{
		//		if (kv_map.find("product") != kv_map.end() && kv_map.at("product") == deviceModel)
		//			return true;
		//	}
		//}
		//return false;
	}

	// web start
	//get
	void CGatewayAIOT::httplinkLive(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("linkLive\n");
		auto param = req.params.find("link");
		if (param == req.params.end())
		{
			res.set_content("request params error = item link", "text/plain");
			return;
		}

		Json::Value root;
		if (param->second == "all")
		{
			int i = 0;
			for (auto& slc : slcs_)
			{
				auto& link = root["linkinfo"][i];
				link["linkid"] = slc.first;
				link["linkname"] = slc.second.alias;
				link["live"] = driverEngine_->getDriverOpenStatus(slc.first);
				++i;
			}
		}
		else
		{
			
		}
		res.set_content(root.toStyledString(), "text/plain");
	}

	void CGatewayAIOT::httpdeviceInfo(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("deviceInfo\n");
		auto param = req.params.find("link");
		if (param == req.params.end())
		{
			return;
		}

		Json::Value root, data, result;
		auto slc = slcs_.find(param->second);
		if (slc == slcs_.end())
		{
			return;
		}
		int i = 0;
		data = Json::arrayValue;
		for (auto& sdc : slc->second.deviceTable)
		{
			auto& dev = data[i];
			dev["id"] = sdc.first;
			dev["name"] = sdc.second.alias;
			//dev["haslivestatus"] = true;
			++i;
		}

		root["data"] = std::move(data);
		result["resultCode"] = "0";
		result["resultError"] = "success";
		root["result"] = std::move(result);
		res.set_content(root.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httppointInfo(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("httppointInfo\n");
		auto param = req.params.find("link");
		if (param == req.params.end())
		{
			httpresResult(res, "-1", "request params error = item link");
			return;
		}
		std::string channelName = param->second;

		auto slc = slcs_.find(channelName);
		if (slc == slcs_.end())
		{
			logger_->warn("link not found, linkName = " + channelName);
			httpresResult(res, "-1", "request params error = link not found, linkName = " + channelName);
			return;
		}

		param = req.params.find("device");
		if (param == req.params.end())
		{
			httpresResult(res, "-1", "request params error = item device");
			return;
		}
		std::string deviceName = param->second;

		auto sdc = slc->second.deviceTable.find(deviceName);
		if (sdc == slc->second.deviceTable.end())
		{
			logger_->warn("device not found, deviceName = " + deviceName);
			httpresResult(res, "-1", "request params error = device not found, deviceName = " + deviceName);
			return;
		}

		//筛选条件
		std::string point, pointName;
		param = req.params.find("point");
		if (param != req.params.end())
		{
			point = param->second;
		}
		param = req.params.find("pointName");
		if (param != req.params.end())
		{
			pointName = param->second;
		}

		DRIVER::DeviceDataSet dds;
		driverEngine_->getBufferedData(channelName, deviceName, dds, true);

		Json::Value root, data = Json::arrayValue, result;
		for (auto& spc : sdc->second.pointTable)
		{
			if (!point.empty() && spc.first.find(point) == std::string::npos)
			{
				continue;
			}
			if (!pointName.empty() && spc.second.alias.find(pointName) == std::string::npos)
			{
				continue;
			}
			Json::Value pi;
			pi["code"] = spc.first;
			pi["name"] = spc.second.alias;
			pi["pointType"] = spc.second.pointType;
			pi["type"] = spc.second.type;
			pi["description"] = spc.second.description;
			for (auto& spp : spc.second.pointProtocol)
			{
				pi["protocol"][spp.first] = spp.second;
			}
			auto findIter = dds.find(spc.first);
			if (findIter != dds.end())
			{
				const auto& value = findIter->second.value;
				switch (value.getType())
				{
				case SValue::eValueInt:  pi["value"] = value.toInt<int64_t>(); break;
				case SValue::eValueUInt:  pi["value"] = value.toInt<uint64_t>(); break;
				case SValue::eValueReal:  pi["value"] = value.toFloat<double>(); break;
				case SValue::eValueString: pi["value"] = value.toString(); break;
				case SValue::eValueBoolean:  pi["value"] = value.toBool(); break;
				case SValue::eValueJson: pi["value"] = value.toString(); break;
				default: break;
				}
				pi["quality"] = Json::arrayValue;
				if (findIter->second.quality == -1)
					pi["quality"][0] = "scanoff";
				pi["timestamp"] = findIter->second.timestamp;
			}
			else
			{
				pi["value"] = "point invalid";
				pi["quality"] = Json::arrayValue;
			}

			data[spc.second.pos] = std::move(pi);
		}

		// 筛选后可能出现null
		Json::Value new_data = Json::arrayValue;
		for (int i = 0; i < data.size(); i++)
		{
			if (!data[i].isNull())
				new_data.append(data[i]);
		}

		root["data"] = std::move(new_data);
		result["resultCode"] = "0";
		result["resultError"] = "success";
		root["result"] = std::move(result);
		//std::cout << root.toStyledString() << std::endl;
		res.set_content(root.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpdllinfo(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("dllinfo\n");
#ifdef WIN32
		const char* libSuffix = ".dll";
#else // WIN32
		const char* libSuffix = ".so";
#endif 
		//std::string pluginPath = DRIVER_PLUGIN_PATH;
		//if (isMultiGatewayEnv)
		//{
		//	pluginPath = launchPath + "/../plugins";
		//}
		std::string pluginPath = CPathManager::getInstance().getPluginPath();
		std::vector<std::string> filesName;
		getFilesName(pluginPath, filesName);
		Json::Value root,arr;
		int i = 0;
		for (auto fileNameIter  = filesName.begin(); fileNameIter != filesName.end(); fileNameIter++)
		{
			if (fileNameIter->rfind(libSuffix) != std::string::npos)
			{
				arr[i++]["name"] = *fileNameIter;
			}
		}
		
		root["dll"] = std::move(arr);
		res.set_content(root.toStyledString(), "text/plain");
	}

	void CGatewayAIOT::httpremovedll(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("removedll\n");
	}

	void CGatewayAIOT::httpgetClientinfo(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("getcClientinfo\n");
		//std::string configPath = CONFIG_PATH;
		//if (isMultiGatewayEnv)
		//{
		//	configPath = GENERATE_MULTI_GATEWAY_FILE_PATH(launchPath, driverXId, "AIOTConfig.json");
		//}
		std::string configPath = CPathManager::getInstance().getAIOTConfigPath();
		std::ifstream ifs(configPath);
		if (ifs.is_open())
		{
			std::istreambuf_iterator<char> beg(ifs), end;
			std::string payload(beg, end);
			res.set_content(payload, "text/plain");
			ifs.close();
		}
		else
		{
			res.set_content("error", "text/plain");
		}
	}

	void CGatewayAIOT::httpreload(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("reload\n");
		reload_ = true;
		mainCondVar_.notify_one();
	}

	void CGatewayAIOT::httppcapinfo(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("pcapinfo\n");
	}

	void CGatewayAIOT::httpgetModbusMappingInfo(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("httpgetModbusMappingInfo\n");
		auto param = req.params.find("link");
		if (param == req.params.end())
		{
			res.set_content("request params error = item link", "text/plain");
			return;
		}
		std::string channelName = param->second;

		auto slc = slcs_.find(channelName);
		if (slc == slcs_.end())
		{
			res.set_content("request params error = link not found", "text/plain");
			return;
		}

		param = req.params.find("device");
		if (param == req.params.end())
		{
			res.set_content("request params error = item device", "text/plain");
			return;
		}
		std::string deviceName = param->second;

		auto sdc = slc->second.deviceTable.find(deviceName);
		if (sdc == slc->second.deviceTable.end())
		{
			res.set_content("request params error = device not found", "text/plain");
			return;
		}

		DRIVER::SData data;
		Json::Value root;
		for (const auto& spc : sdc->second.pointTable)
		{
			if (!driverEngine_->getBufferedData(channelName, deviceName, spc.first, data))
			{
				continue;
			}

			Json::Value pi;
			pi["Name"] = spc.second.alias;
			pi["PointType"] = spc.second.pointType;
			pi["Addr"] = getModbusAddr(modbusMappingTable_, channelName, deviceName, spc.first);
			switch (data.value.getType())
			{
			case SValue::eValueInt:  pi["DataType"] = "int"; break;
			case SValue::eValueUInt:  pi["DataType"] = "uint"; break;
			case SValue::eValueReal:  pi["DataType"] = "float"; break;
			case SValue::eValueString: pi["DataType"] = "string"; break;
			case SValue::eValueBoolean:  pi["DataType"] = "bool"; break;
			case SValue::eValueJson: pi["DataType"] = "json"; break;
			default:
				break;
			}
			
			root[spc.first] = std::move(pi);
		}
		res.set_content(root.toStyledString(), "text/plain");
	}

	void CGatewayAIOT::httpgetUserInfo(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("getcClientinfo\n");
		constexpr static char* emptyUserInfo = "{\"Username\":\"\"}";
		
		//std::string userinfoPath = USERINFO_PATH;
		//if (isMultiGatewayEnv)
		//{
		//	userinfoPath = GENERATE_MULTI_GATEWAY_FILE_PATH(launchPath, driverXId, "UserInfo.json");
		//}
		std::string userinfoPath = CPathManager::getInstance().getUserInfoFile();
		std::ifstream ifs(userinfoPath);
		if (ifs.is_open())
		{
			std::istreambuf_iterator<char> beg(ifs), end;
			std::string payload(beg, end);
			res.set_content(payload, "text/plain");
			ifs.close();
		}
		else
		{
			std::ofstream ofs(userinfoPath);
			if (ofs.is_open())
			{
				ofs.write(emptyUserInfo, ::strlen(emptyUserInfo));
				ofs.close();
				res.set_content(emptyUserInfo, "text/plain");
			}
			else
			{
				res.set_content("error", "text/plain");
			}
			res.set_content("error", "text/plain");
		}
	}

	bool CGatewayAIOT::getSerialNumber(std::string &serial)
	{
		char* serial_env = getenv("SUPEDGE");
		if (serial_env)
		{
			serial = std::move(serial_env);
			return true;
		}

		bool rc = getFileContent(serialNumberFile, serial);
		if (rc)
		{
			//去除末尾的换行、回车、空格
			while (true)
			{
				int len = serial.length();
				if (len == 0)
					break;

				if (serial[len - 1] == 10 || serial[len - 1] == 13 || serial[len - 1] == 32)
				{
					serial.pop_back(); //去掉10:换行 13:回车 32:空格
				}
				else 
					break;
			}
		}

		return rc;
	}

	void CGatewayAIOT::httpgetBasicInfo(const httplib::Request& req, httplib::Response& res)
	{
		std::string rErr;

		std::string product = sog_.className;
		std::string serialNumber = sog_.className;
		std::string sogid = sog_.id;
		std::string sogname = sog_.name;
		std::string env = "normal";

		env = envToString[env_];
		//bool rc2 = getSerialNumber(serialNumber);
		//if (rc2)
		//{
		//	env = "e500";
		//}
		//else if (!rc2)
		//{
		//	// try com201

		//	if (checkEnv("com201"))
		//	{
		//		env = "com201";
		//	}
		//}		

		std::string version;
		bool rc3 = (getRunFirmwareInfo(version) == 0);
		if (!rc3)
		{
			rErr += std::string("Get Version Failed.");
		}

		if (configSource_ == EConfigurationSource::ELocal)
		{
			sogid = u8"使用本地组态";
			sogname = u8"使用本地组态";
		}

		Json::Value jv;
		jv["data"]["product"] = product;
		jv["data"]["serialNumber"] = serialNumber.empty() ? product : serialNumber;
		jv["data"]["id"] = sogid;
		jv["data"]["name"] = sogname;
		jv["data"]["version"] = version;
		jv["data"]["env"] = env;
		//jv["data"]["env"] = "e500";

		//jv["result"]["resultCode"] = rc2 && rc3 ? "0" : "-1";
		//jv["result"]["resultError"] = rErr;

		jv["result"]["resultCode"] = "0";
		jv["result"]["resultError"] = "success";

		res.set_content(jv.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpgetCloudConfigInfo(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("getCloudConfigInfo\n");
		Json::Value root, data, result;

		data["clientID"] = mainPlatform_.clientId;
		data["username"] = mainPlatform_.mqttUsername;
		data["password"] = mainPlatform_.mqttPassword;
		data["resoureIP"] = mainPlatform_.resourceServer;
		data["mqttURL"] = "ssl://" + mainPlatform_.mqttUrl;
		data["connectStatus"] = mainPlatform_.mqttClient->getMqttConnectionStatus();

		Json::Value address = Json::arrayValue;
		{
			std::unique_lock<std::mutex> lock(mqttListMutex_);
			for (int i = 0; i < dataDistributionVec_.size(); i++)
			{
				const auto& dd = dataDistributionVec_[i];
				address[i]["clientID"] = dd.clientId;
				address[i]["username"] = dd.mqttUsername;
				address[i]["password"] = dd.mqttPassword;
				address[i]["resoureIP"] = dd.resourceServer;
				address[i]["mqttURL"] = "ssl://" + dd.mqttUrl;
				address[i]["connectStatus"] = dd.mqttClient->getMqttConnectionStatus();
			}
		}
		data["address"] = address;

		//cloud time
		int64_t cloudTime = 0;
		getCloudTime(cloudTime);
		time_t t = cloudTime / 1000;
		struct tm *tm = localtime(&t);
		char datestr[64];
		strftime(datestr, sizeof(datestr), "%Y-%m-%d %H:%M:%S", tm);
		data["cloudTime"] = datestr;

		root["data"] = std::move(data);
		result["resultCode"] = "0";
		result["resultError"] = "success";
		root["result"] = std::move(result);
		res.set_content(root.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpgetRuntimeInfo(const httplib::Request& req, httplib::Response& res)
	{
		//runtime info
		//cpu info
		//memory info
		//disc info
		//net info

		SCPUInfo cif;
		systemSetting_->getCPUInfo(cif);

		SMemoryInfo mif;
		systemSetting_->getMemoryInfo(mif);

		SStorageInfo sif;
		systemSetting_->getCurrentStorageInfo(sif);

		NetworkConfigList ncl;
		systemSetting_->getNetworkConfigList(ncl);

		Json::Value jvReturn;
		jvReturn["result"]["resultCode"] = "0";
		jvReturn["result"]["resultError"] = "";

		jvReturn["data"]["cpuInfo"]["usage"] = cif.usage;

		jvReturn["data"]["memoryInfo"]["memTotal"] = mif.memTotal;
		jvReturn["data"]["memoryInfo"]["memFree"] = mif.memFree;
		jvReturn["data"]["memoryInfo"]["memUsed"] = mif.memUsed;
		jvReturn["data"]["memoryInfo"]["swapTotal"] = mif.swapTotal;
		jvReturn["data"]["memoryInfo"]["swapFree"] = mif.swapFree;
		jvReturn["data"]["memoryInfo"]["swapUsed"] = mif.swapUsed;

		jvReturn["data"]["storageInfo"]["name"] = sif.name;
		jvReturn["data"]["storageInfo"]["capacity"] = sif.capacity;
		jvReturn["data"]["storageInfo"]["used"] = sif.used;
		jvReturn["data"]["storageInfo"]["free"] = sif.free;

		for(auto it = ncl.begin(); it != ncl.end(); it++)
		{
			Json::Value nwc;
			nwc["type"] = it->type;
			nwc["name"] = it->name;
			nwc["ipv4"] = it->ipv4;
			nwc["netmask"] = it->netmask;
			nwc["mac"] = it->mac;
			nwc["ipv6"] = it->ipv6;
			nwc["rxPackets"] = it->rxPackets;
			nwc["rxBytes"] = it->rxBytes;
			nwc["txPackets"] = it->txPackets;
			nwc["txBytes"] = it->txBytes;
			nwc["linkDetected"] = it->linkDetected;
			jvReturn["data"]["netWorkConfigList"].append(nwc);
		}
		res.set_content(jvReturn.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpgetNetWorkConfigList(const httplib::Request& req, httplib::Response& res)
	{
		NetworkConfigList ncl;
		systemSetting_->getNetworkConfigList(ncl);

		Json::Value jvReturn;
		jvReturn["result"]["resultCode"] = "0";
		jvReturn["result"]["resultError"] = "";
		jvReturn["data"]["enableChange"] = enableNetWorkChange_;
		jvReturn["data"]["data"] = Json::arrayValue;

		int i = 0;
		for (auto it = ncl.begin(); it != ncl.end(); it++)
		{
			if (it->type == 4) //回环不返回
				continue;
			Json::Value nwc;
			nwc["type"] = it->type;
			nwc["name"] = it->name;
			nwc["ipv4"] = it->ipv4;
			nwc["netmask"] = it->netmask;
			nwc["mac"] = it->mac;
			nwc["ipv6"] = it->ipv6;
			nwc["dns"] = it->dns;
			nwc["gateway"] = it->gateway;
			nwc["linkDetected"] = it->linkDetected;
			nwc["dhcp"] = it->dhcp;
			nwc["enableChange"] = it->enableChange;
			if (it->type == 2)
				nwc["ssid"] = it->ssid;
			jvReturn["data"]["data"][i++] = nwc;
		}
		res.set_content(jvReturn.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpgetWirelessList(const httplib::Request& req, httplib::Response& res)
	{
		auto param = req.params.find("name");
		if (param == req.params.end())
		{
			httpresResult(res, "-1", "request params error = item name");
			return;
		}
		std::string name = param->second;

		WirelessInfoList wil;
		std::string args = " " + name;
		if (!systemSetting_->getAvailableWirelessList(args, wil))
		{
			httpresResult(res, "-1", "excute failed");
			return;
		}

		Json::Value jvReturn;
		jvReturn["result"]["resultCode"] = "0";
		jvReturn["result"]["resultError"] = "";
		jvReturn["data"] = Json::arrayValue;

		std::map<std::string, std::map<int, SWirelessInfo> > tmpMap;

		int i = 0;
		for (auto it = wil.begin(); it != wil.end(); it++)
		{
			tmpMap[it->ssid].emplace(it->signal, std::move(*it));
		}
		for (auto& it : tmpMap)
		{
			Json::Value wi;
			wi["ssid"] = it.first;
			wi["signal"] = it.second.rbegin()->second.signal;
			wi["needPsk"] = true;
			jvReturn["data"][i++] = wi;
		}
		
		res.set_content(jvReturn.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpsetWirelessDisconnect(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("setWirelessDisconnect\n");

		auto param = req.params.find("name");
		if (param == req.params.end())
		{
			httpresResult(res, "-1", "request params error = item name");
			return;
		}
		std::string name = param->second;

		std::string content = u8"断开无线 name:" + name;
		insertLog(eUserOperate, content);

		std::string args = " " + name;
		systemSetting_->setWirelessDisconnect(args);
		httpresResult(res, "0", "");

		uploadInfoNow_ = true;
	}

	void CGatewayAIOT::httpUpdate(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("httpUpdate\n");

		bool rv = false;
		auto iter = req.headers.find("url");
		if (req.headers.end() != iter)
		{
			COMM::ICommHttp* hc = static_cast<COMM::ICommHttp*>(COMM::CFactory::getInstance().produce("CommHttp"));
			hc->configure("127.0.0.1", 6666);
			COMM::ICommHttp::Headers exHeaders;
			exHeaders["url"] = iter->second;
			auto rv = hc->get("/update", exHeaders);
			std::cout << "httpUpdate body: " << rv.body << std::endl;
			std::cout <<"httpUpdate error: " << rv.error << std::endl;
			COMM::CFactory::getInstance().destory(hc);
		}
		httpresResult(res, "0", "");
	}

	void CGatewayAIOT::httpgetTimeInfo(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("httpgetTimeInfo\n");

		time_t t = time(0);
		struct tm* tm = localtime(&t);
		char datestr[64];
		strftime(datestr, sizeof(datestr), "%Y-%m-%d %H:%M:%S", tm);

		Json::Value jvReturn;
		jvReturn["result"]["resultCode"] = "0";
		jvReturn["result"]["resultError"] = "";
		jvReturn["data"]["localTime"] = datestr;

		STimeSyncInfo tsi;
		if (systemSetting_->getSyncTime(tsi))
		{
			jvReturn["data"]["syncSwitch"] = tsi.Switch;
			jvReturn["data"]["syncAddress"] = tsi.address;
		}

		res.set_content(jvReturn.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpgetVersionInfo(const httplib::Request& req, httplib::Response& res)
	{
		//std::string content;
		//if (!getFileContent(VERSION_FILE_NAME, content))
		//	res.set_content("read version file failed.", "text/plain");
		//res.set_content(content, "text/plain");

		Json::Value firmwareInfo;
		Json::Value root, data;
		//std::ifstream ifs(PACKGE_INFO_FILE);
		std::ifstream ifs(CPathManager::getInstance().getPackInfoFile());
		data["firmwareInfo"]["createTime"] = "unknow";
		data["firmwareInfo"]["hash"] = "unknow";
		if (ifs.is_open())
		{
			JSONCPP_STRING err;
			Json::CharReaderBuilder builder;
			if (Json::parseFromStream(builder, ifs, &firmwareInfo, &err))
			{
				data["firmwareInfo"]["createTime"] = firmwareInfo.isMember("createTime") ? firmwareInfo["createTime"].asString() : "unknw";
				data["firmwareInfo"]["hash"] = firmwareInfo.isMember("name") ? firmwareInfo["name"].asString() : "unknow";
			}
			ifs.close();
		}
		data["gateway"]["processStartTime"] = UTILS::unixToFormattedTimeString(processStartTime_ * 1000);
		data["gateway"]["totalLinkCount"] = totalLinkCount_;
		data["gateway"]["totalDeviceCount"] = totalDeviceCount_;
		data["gateway"]["totalPointCount"] = innerPointCount_ + normalPointCount_;

		root["data"] = std::move(data);
		root["result"]["resultCode"] = "0";
		root["result"]["resultError"] = "success";
		res.set_content(root.toStyledString(), "application/json");
	}

	int CGatewayAIOT::getRunFirmwareInfo(std::string& version)
	{
		int ret = -1;
		std::string versionXml;
		if (getFileContent(GATEWAY_VERSION, versionXml))
		{
			//parse version
			tinyxml2::XMLDocument doc;
			if (doc.Parse(versionXml.c_str()) != tinyxml2::XML_SUCCESS)
			{
				logger_->info("parse local xml failed.");
			}
			else
			{
				tinyxml2::XMLElement* childElement = doc.FirstChildElement("update");
				if (childElement)
					childElement = childElement->FirstChildElement("package");
				if (childElement)
					childElement = childElement->FirstChildElement("version");
				if (childElement)
				{
					const char* _version = childElement->GetText();
					if (_version)
					{
						version = _version;
						ret = 0;
					}
				}
			}
		}
		return ret;
	}

	int CGatewayAIOT::getCloudFirmwareInfo(std::string &version)
	{
		std::string url = "http://" + mainPlatform_.resourceServer + "/supaiot/api/edge/" + sog_.className + "/file_list.xml";
		auto rsp = httpClient_->get(url);
		auto& resp = rsp.body;
		//parse version
		tinyxml2::XMLDocument doc;
		if (doc.Parse(resp.c_str()) != tinyxml2::XML_SUCCESS)
		{
			logger_->info("getCloudFirmwareInfo parse xml failed, url:{0}", url);
			return -1;
		}

		tinyxml2::XMLElement* childElement = doc.FirstChildElement("update");
		if (childElement)
			childElement = childElement->FirstChildElement("package");
		if (childElement)
			childElement = childElement->FirstChildElement("version");
		if (!childElement)
		{
			return -1;
		}
		const char* _version = childElement->GetText();
		if (!_version)
		{
			return -1;
		}

		version = _version;
		return 0;
	}

	void CGatewayAIOT::httpgetCloudFirmwareInfo(const httplib::Request& req, httplib::Response& res)
	{
		Json::Value root, data, result;

		std::string version;
		int ret = getCloudFirmwareInfo(version);
		if (ret < 0)
		{
			result["resultCode"] = "-1";
			result["resultError"] = "getCloudFirmwareInfo failed";
			root["result"] = std::move(result);
			res.set_content(root.toStyledString(), "application/json");
			return;
			//httpresResult(res, "-1", "failed");
			//return;
		}

		data["version"] = version;
		root["data"] = std::move(data);

		result["resultCode"] = "0";
		result["resultError"] = "success";
		root["result"] = std::move(result);
		res.set_content(root.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpgetInfoSubmission(const httplib::Request& req, httplib::Response& res)
	{
		Json::Value jv;
		jv["result"]["resultCode"] = "0";
		jv["result"]["resultError"] = "";
		jv["data"]["isReport"] = girc_.isReport;
		jv["data"]["basicInfo"] = girc_.basic;
		jv["data"]["runtimeInfo"] = girc_.runtime;
		jv["data"]["reportInterval"] = girc_.interval;

		res.set_content(jv.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpgetOtherConfigInfo(const httplib::Request& req, httplib::Response& res)
	{
		Json::Value root, data, result;

		data["disConnCache"] = enableLocalDataCaching_;
		data["cloudControlAuth"] = enableCloudControl_;
		root["data"] = std::move(data);

		result["resultCode"] = "0";
		result["resultError"] = "success";
		root["result"] = std::move(result);
		res.set_content(root.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httplinkLogList(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("linkLogList\n");
		auto param = req.params.find("link");
		if (param == req.params.end())
		{
			httpresResult(res, "-1", "request params error = item link");
			return;
		}
		std::string channelName = param->second;

		auto slc = slcs_.find(channelName);
		if (slc == slcs_.end())
		{
			httpresResult(res, "-1", "request params error = link not found");
			return;
		}

		//std::string logPath = std::string(LOG_PATH) + "/" + channelName + slc->second.alias + "/";
		//if (isMultiGatewayEnv)
		//{
		//	logPath = GENERATE_MULTI_GATEWAY_LOG_PATH(launchPath, driverXId, channelName + slc->second.alias);
		//}
		std::string logPath = CPathManager::getInstance().getChannelLogPath(channelName, slc->second.alias);

		std::unordered_map<std::string, std::string> logPathMap;
		param = req.params.find("logLevel");
		if (param != req.params.end() && !param->second.empty())
		{
			logPathMap[param->second] =  logPath+ param->second;
		}
		else
		{
			for (int i = 0; i < DRIVER::ELogLevel::n_levels; i++)
			{
				logPathMap[DRIVER::logLevel2String(DRIVER::ELogLevel(i))] = logPath + DRIVER::logLevel2String(DRIVER::ELogLevel(i));
			}
		}

#ifdef WIN32
		for (auto it = logPathMap.begin(); it != logPathMap.end(); it++)
		{
			logPathMap[it->first] = UtfToGbk(it->second.c_str());
		}
#endif
		//std::cout << logPathMap.size() << std::endl;

		std::unordered_map<std::string, std::map<uint64_t, SFileInfo>> filesInfoMap;
		Json::Value root, data, result;
		for (auto it = logPathMap.begin(); it != logPathMap.end(); it++)
		{
			std::vector<SFileInfo> filesInfo;
			getFilesInfo(it->second, filesInfo);
			int i = 0;
			for (auto fileInfoIter = filesInfo.begin(); fileInfoIter != filesInfo.end(); fileInfoIter++)
			{
				int j = i++;
				filesInfoMap[it->first][fileInfoIter->modificationTime] = *fileInfoIter;
				data[it->first][j]["name"] = fileInfoIter->name;
				data[it->first][j]["modificationTime"] = fileInfoIter->modificationTime;
				data[it->first][j]["size"] = fileInfoIter->size;
			}
		}
		

//		std::string log_path = std::string(LOG_PATH) + "/" + channelName + slc->second.alias;
//#ifdef WIN32
//		log_path = UtfToGbk(log_path.c_str());
//#endif 
//		std::vector<SFileInfo> filesInfo;
//		getFilesInfo(log_path, filesInfo);
//
//		std::map<uint64_t, SFileInfo> m_filesInfo;
//
//		
//		int i = 0;
//		for (auto fileInfoIter = filesInfo.begin(); fileInfoIter != filesInfo.end(); fileInfoIter++)
//		{
//			m_filesInfo[fileInfoIter->modificationTime] = *fileInfoIter;
//		}
//		for (auto fileInfoIter = m_filesInfo.begin(); fileInfoIter != m_filesInfo.end(); fileInfoIter++)
//		{
//			data[i]["name"] = fileInfoIter->second.name;
//			data[i]["modificationTime"] = fileInfoIter->second.modificationTime;
//			data[i++]["size"] = fileInfoIter->second.size;
//		}

		root["data"] = std::move(data);
		result["resultCode"] = "0";
		result["resultError"] = "success";
		root["result"] = std::move(result);
		res.set_content(root.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httplinkLog(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("linkLog\n");
		auto param = req.params.find("link");
		if (param == req.params.end())
		{
			httpresResult(res, "-1", "request params error = item link");
			return;
		}
		std::string channelName = param->second;

		param = req.params.find("logLevel");
		if (param == req.params.end())
		{
			httpresResult(res, "-1", "request params error = item logLevel");
			return;
		}
		std::string logLevel = param->second;

		auto slc = slcs_.find(channelName);
		if (slc == slcs_.end())
		{
			httpresResult(res, "-1", "request params error = link not found");
			return;
		}

		param = req.params.find("name");
		if (param == req.params.end())
		{
			httpresResult(res, "-1", "request params error = item name");
			return;
		}
		std::string name = param->second;

		//std::string log_path = std::string(LOG_PATH) + "/" + channelName + slc->second.alias + "/" + name;
		//std::string log_path = std::string(LOG_PATH) + "/" + channelName + slc->second.alias + "/" + logLevel + "/" + name;
		//if (isMultiGatewayEnv)
		//{
		//	log_path = GENERATE_MULTI_GATEWAY_LOG_PATH(launchPath, driverXId, channelName + slc->second.alias) + logLevel + "/" + name;
		//}
		std::string log_path = CPathManager::getInstance().getChannelLog(channelName, slc->second.alias, logLevel, name);
#ifdef WIN32
		log_path = UtfToGbk(log_path.c_str());
#endif 

		std::ifstream ifs(log_path.c_str());
		if (ifs.is_open())
		{
			std::istreambuf_iterator<char> beg(ifs), end;
			std::string payload(beg, end);
			res.set_content(payload, "text/plain");
			ifs.close();
		}
		else
		{
			res.set_content("file open failed", "text/plain");
		}
	}

	void CGatewayAIOT::httplinkInfo(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("linkInfo\n");
		auto param = req.params.find("link");
		if (param == req.params.end())
		{
			httpresResult(res, "-1", "request params error = item link");
			return;
		}

		Json::Value root, data = Json::arrayValue, result;
		if (param->second == "all")
		{
			int i = 0;
			for (auto& slc : slcs_)
			{
				auto& link = data[i];
				link["id"] = slc.first;
				link["name"] = slc.second.alias;
				link["className"] = slc.second.className;
				link["enableStatus"] = slc.second.enableStatus;
				if (slc.second.enableStatus)
				{
					link["connectStatus"] = driverEngine_->getDriverOpenStatus(slc.first);
					link["connectQuality"] = driverEngine_->getDriverConnectQuality(slc.first);
				}

				Json::Value argument, connection;
				Json::CharReaderBuilder builder;
				std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
				if (reader->parse(slc.second.argumentStr.c_str(), slc.second.argumentStr.c_str() + slc.second.argumentStr.size(), &argument, NULL))
				{
					link["argument"] = std::move(argument);
				}
				if (reader->parse(slc.second.connectionStr.c_str(), slc.second.connectionStr.c_str() + slc.second.connectionStr.size(), &connection, NULL))
				{
					link["connection"] = std::move(connection);
				}
				link["protocol"] = Json::objectValue;
				for (auto& ssc : slc.second.channelProtocol)
				{
					link["protocol"][ssc.first] = ssc.second;
				}
				++i;
			}
		}
		else
		{
			auto it = slcs_.find(param->second);
			if (it != slcs_.end())
			{
				auto& link = data[0];
				link["id"] = it->first;
				link["name"] = it->second.alias;
				link["className"] = it->second.className;
				link["enableStatus"] = it->second.enableStatus;
				if (it->second.enableStatus)
				{
					link["connectStatus"] = driverEngine_->getDriverOpenStatus(it->first);
					link["connectQuality"] = driverEngine_->getDriverConnectQuality(it->first);
				}

				Json::Value argument, connection;
				Json::CharReaderBuilder builder;
				std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
				if (reader->parse(it->second.argumentStr.c_str(), it->second.argumentStr.c_str() + it->second.argumentStr.size(), &argument, NULL))
				{
					link["argument"] = std::move(argument);
				}
				if (reader->parse(it->second.connectionStr.c_str(), it->second.connectionStr.c_str() + it->second.connectionStr.size(), &connection, NULL))
				{
					link["connection"] = std::move(connection);
				}

				for (auto& ssc : it->second.channelProtocol)
				{
					link["protocol"][ssc.first] = ssc.second;
				}
			}
		}

		root["data"] = std::move(data);
		result["resultCode"] = "0";
		result["resultError"] = "success";
		root["result"] = std::move(result);
		res.set_content(root.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httplinkEnable(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("linkEnable\n");
		auto param = req.params.find("link");
		if (param == req.params.end())
		{
			httpresResult(res, "-1", "request params error = item link");
			return;
		}
		std::string channelName = param->second;

		auto slc = slcs_.find(channelName);
		if (slc == slcs_.end())
		{
			httpresResult(res, "-1", "request params error = link not found");
			return;
		}

		param = req.params.find("enable");
		if (param == req.params.end())
		{
			httpresResult(res, "-1", "request params error = item enable");
			return;
		}

		bool enable;
		if (param->second == "true")
		{
			enable = true;
		}
		else if (param->second == "false")
		{
			enable = false;
		}
		else
		{
			httpresResult(res, "-1", "request params error = item enable");
			return;
		}

		if (slc->second.enableStatus == enable)
		{
			httpresResult(res, "-1", "enableStatus no change");
			return;
		}

		bool ret = true;
		if (enable)
		{
			ret = driverEngine_->addDriver(slc->first, slc->second);
		}
		else
		{
			driverEngine_->deleteDriver(slc->first);
		}

		Json::Value root, result;
		if (ret)
		{
			slc->second.enableStatus = enable;
			httpresResult(res, "0", "success");
			
			std::string content = (slc->second.enableStatus ? u8"启用链路:" : u8"禁用链路:") + slc->second.alias;
			insertLog(eUserOperate, content);
		}
		else
		{
			httpresResult(res, "-1", "failed");
		}
		onEngineEnable(slc->first, !slc->second.enableStatus);
	}

	void CGatewayAIOT::httprestart(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("restart\n");

		httpresResult(res, "0", "success");

		insertLog(eUserOperate, u8"重启网关");

		driverEngine_->deleteAllDriver();
		//exit(0);

		new std::thread([&]() { std::this_thread::sleep_for(std::chrono::seconds(2)); printf("exit\n"); _exit(0); });
	}

	//post
	void CGatewayAIOT::httpsetPointValue(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("setPointValue\n");

		Json::Value root, result;
		SCloudMessageSptr cloudMessageSptr(new SCloudMessage);
		cloudMessageSptr->payload = req.body;
		if (control(cloudMessageSptr).compare("succeed") == 0)
		{
			httpresResult(res, "0", "success");
		}
		else
		{
			httpresResult(res, "-1", "failed");
		}
	}

	void CGatewayAIOT::httpgetLinkEnableInfo(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("getLinkEnableInfo\n");

		int num = 0, numEable = 0;
		Json::Value root, data, result;

		for (auto iter : slcs_) {
			num++;
			if (iter.second.enableStatus == true) {
				numEable++;
			}
		}

		data["numOfEnable"] = numEable;
		data["numOfAll"] = num;

		root["data"] = std::move(data);
		result["resultCode"] = "0";
		result["resultError"] = "success";
		root["result"] = std::move(result);
		res.set_content(root.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpsetClientInfo(const httplib::Request& req, httplib::Response& res, bool newReturn)
	{
		logger_->info("setClientInfo\n");
		//std::string configPath = CONFIG_PATH;
		//if (isMultiGatewayEnv)
		//{
		//	configPath = GENERATE_MULTI_GATEWAY_FILE_PATH(launchPath, driverXId, "AIOTConfig.json");
		//}
		std::string configPath = CPathManager::getInstance().getAIOTConfigPath();
		std::ofstream ofs(configPath);
		if (ofs.is_open())
		{
			ofs.write(req.body.c_str(), req.body.size());
			ofs.close();

			int flag = 1;
			//std::ofstream(GATEWAY_FLAG, std::ios::binary).write(reinterpret_cast<char*>(&flag), sizeof flag);
			//blank_ = false;

			if (killSelf_)
			{
				new std::thread([&]() { std::this_thread::sleep_for(std::chrono::seconds(2)); _exit(0); });
			}
			else //disconnect mqtt
			{
				std::unique_lock<std::mutex> lock(mqttListMutex_);
				for (int i = 0; i < dataDistributionVec_.size(); i++)
				{
					const auto& dd = dataDistributionVec_[i];
					dd.mqttClient->disconnect();
					delete dd.mqttClient;
				}
				dataDistributionVec_.clear();

				loadGatewayConfig();
				httpClient_->configure(mainPlatform_.resourceServer);
				// mqtt
				mainPlatform_.mqttClient->disconnect();
				//mainPlatform_.mqttClient->configure(mainPlatform_.clientId, mainPlatform_.mqttUrl, mainPlatform_.mqttUsername, mainPlatform_.mqttPassword, 
				//	std::string(CRT_PATH) + "ca.crt", std::string(CRT_PATH) + "client.crt", std::string(CRT_PATH) + "client.key");
				mainPlatform_.mqttClient->configure(mainPlatform_.clientId, mainPlatform_.mqttUrl, mainPlatform_.mqttUsername, mainPlatform_.mqttPassword,
					CPathManager::getInstance().getCrtPath() + "ca.crt", CPathManager::getInstance().getCrtPath() + "client.crt", CPathManager::getInstance().getCrtPath() + "client.key");
				mainPlatform_.mqttClient->connect(1000);
				for (int i = 0; i < dataDistributionVec_.size(); i++)
				{
					auto& dd = dataDistributionVec_[i];
					dd.mqttClient = new CMqttClient(*this);
					//dd.mqttClient->configure(dd.clientId, dd.mqttUrl, dd.mqttUsername, dd.mqttPassword, 
					//	std::string(CRT_PATH) + "ca.crt", std::string(CRT_PATH) + "client.crt", std::string(CRT_PATH) + "client.key");
					dd.mqttClient->configure(dd.clientId, dd.mqttUrl, dd.mqttUsername, dd.mqttPassword, 
						CPathManager::getInstance().getCrtPath() + "ca.crt", CPathManager::getInstance().getCrtPath() + "client.crt", CPathManager::getInstance().getCrtPath() + "client.key");
					dd.mqttClient->connect(1000);
				}
			}
			if (newReturn) httpresResult(res, "0", "success");
		}
		else
		{
			if (newReturn) 
				httpresResult(res, "-1", "failed");
			else 
				res.set_content("error", "text/plain");
		}
	}

	void CGatewayAIOT::httpgetCloudNameInfo(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("httpgetCloudNameInfo\n");

		int num = 0, numEable = 0;
		Json::Value root, data, result;

		if (sog_.name.empty()) {
			data["ownershipGateway"] = "";
		}
		else {
			data["ownershipGateway"] = sog_.name;
		}

		if (sog_.projectName.empty()) {
			data["ownershipProject"] = "";
		}
		else {
			data["ownershipProject"] = sog_.projectName;
		}

		root["data"] = std::move(data);
		result["resultCode"] = "0";
		result["resultError"] = "success";
		root["result"] = std::move(result);
		res.set_content(root.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpsetModbusMappingInfo(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("httpgetModbusMappingInfo\n");
		JSONCPP_STRING err;
		Json::Value root;
		Json::CharReaderBuilder builder;
		std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
		if (!reader->parse(req.body.c_str(), req.body.c_str() + req.body.size(), &root, &err))
		{
			std::cout << "parse comming message error = " << err << std::endl;
			return;
		}
		
		std::string channelName = root["LinkId"].asString();
		if (channelName.empty())
		{
			res.set_content("request params error = item link", "text/plain");
			return;
		}

		auto slc = slcs_.find(channelName);
		if (slc == slcs_.end())
		{
			res.set_content("request params error = link not found", "text/plain");
			return;
		}

		std::string deviceName = root["DeviceId"].asString();
		if (deviceName.empty())
		{
			res.set_content("request params error = item device", "text/plain");
			return;
		}

		auto sdc = slc->second.deviceTable.find(deviceName);
		if (sdc == slc->second.deviceTable.end())
		{
			res.set_content("request params error = device not found", "text/plain");
			return;
		}

		auto& pm = modbusMappingTable_[channelName][deviceName];
		Json::Value table;
		root.removeMember("Table", &table);
		for (auto pointIter = table.begin(); pointIter != table.end(); ++pointIter)
		{
			if ((*pointIter)["Addr"].asInt() >= 0)
			{
				SModbusParam smp;
				smp.addr = (*pointIter)["Addr"].asInt();
				smp.name = (*pointIter)["Name"].asString();
				smp.dataType = (*pointIter)["DataType"].asString();

				std::string pointKey = channelName + "." + deviceName + "." + smp.name;
				if (smp.dataType == "bool")
				{
					modbusCoilAddrPointKeyMappingTable_[smp.addr] = std::move(pointKey);
				}
				else
				{
					modbusRegisterAddrPointKeyMappingTable_[smp.addr] = std::move(pointKey + "." + smp.dataType);
				}

				pm[pointIter.name()] = std::move(smp);
			}
			else
			{
				auto findIter = pm.find(pointIter.name());
				if (findIter != pm.end())
				{
					if (findIter->second.dataType == "bool")
					{
						auto iter = modbusCoilAddrPointKeyMappingTable_.find(findIter->second.addr);
						if (iter != modbusCoilAddrPointKeyMappingTable_.end())
						{

							modbusCoilAddrPointKeyMappingTable_.erase(iter);
						}
					}
					else
					{
						auto iter = modbusRegisterAddrPointKeyMappingTable_.find(findIter->second.addr);
						if (iter != modbusRegisterAddrPointKeyMappingTable_.end())
						{

							modbusRegisterAddrPointKeyMappingTable_.erase(iter);
						}
					}

					pm.erase(findIter);
				}
			}
		}
		
		saveModbusMappingTable(modbusMappingTable_, MODBUS_MAPPING_TABLE_PATH);
		res.set_content("success", "text/plain");
	}

	void CGatewayAIOT::httpgetCloudStatus(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("httpgetCloudStatus\n");

		Json::Value root, data, result;

		data["cloudConnectionStatus"] = sog_.cloudConnectionStatus;
		data["cloudConfigGetStatus"] = sog_.cloudConfigGetStatus;

		root["data"] = std::move(data);
		result["resultCode"] = "0";
		result["resultError"] = "success";
		root["result"] = std::move(result);
		res.set_content(root.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpSignUp(const httplib::Request& req, httplib::Response& res)
	{
		JSONCPP_STRING err;
		Json::Value root;
		Json::CharReaderBuilder builder;
		std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
		if (!reader->parse(req.body.c_str(), req.body.c_str() + req.body.size(), &root, &err))
		{
			std::cout << "parse comming message error = " << err << std::endl;
			return;
		}

		if (!root["Username"].empty() && root["Password"] == root["ConfirmThePassword"])
		{
			std::ofstream ofs(USERINFO_PATH);
			if (ofs.is_open())
			{
				root.removeMember("ConfirmThePassword");
				const std::string userInfo = root.toStyledString();
				ofs.write(userInfo.c_str(), userInfo.size());
				ofs.close();
				res.set_content("success", "text/plain");
			}
			else
			{
				res.set_content("error", "text/plain");
			}
		}
	}

	void CGatewayAIOT::httpSignIn(const httplib::Request& req, httplib::Response& res)
	{
		JSONCPP_STRING err;
		Json::Value webUserInfo, localUserInfo;
		Json::CharReaderBuilder builder;
		std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
		if (!reader->parse(req.body.c_str(), req.body.c_str() + req.body.size(), &webUserInfo, &err))
		{
			std::cout << "parse comming message error = " << err << std::endl;
			httpresResult(res, "-1", "wrong request body");
			return;
		}

		//std::ifstream ifs(USERINFO_PATH);
		std::ifstream ifs(CPathManager::getInstance().getUserInfoFile());
		if (ifs.is_open())
		{
			std::istreambuf_iterator<char> beg(ifs), end;
			std::string payload(beg, end);
			if (!reader->parse(payload.c_str(), payload.c_str() + payload.size(), &localUserInfo, &err))
			{
				httpresResult(res, "-1", "local userinfo is wrong");
			}
			else 
			{
				if (webUserInfo["userName"] == localUserInfo["userName"] && webUserInfo["passWord"] == localUserInfo["passWord"])
				{
					httpresResult(res, "0", "success");
				}
				else
				{
					httpresResult(res, "-1", u8"用户名或密码错误");
				}
			}
			ifs.close();
		}
		else
		{
			httpresResult(res, "-1", "local userinfo is wrong");
		}
	}

	void CGatewayAIOT::httpsetInfoSubmission(const httplib::Request& req, httplib::Response& res)
	{
		//set gatewayinfo submission

		auto& body = req.body;
		Json::Value response;

		JSONCPP_STRING err;
		Json::Value root;
		Json::CharReaderBuilder builder;
		std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
		if (!reader->parse(body.c_str(), body.c_str() + body.size(), &root, &err))
		{
			response["data"] = Json::objectValue;
			response["result"]["resultCode"] = "-1";
			response["result"]["resultError"] = "wrong request body";
			res.set_content(response.toStyledString(), "application/json");
			return;
		}
		Json::Value jv;
		if (root.removeMember("isReport", &jv))
		{
			if (girc_.isReport != jv.asBool())
			{
				girc_.isReport = jv.asBool();
				std::string content = girc_.isReport ? u8"打开信息上送开关" : u8"关闭信息上送开关";
				insertLog(eUserOperate, content);
				writeDriverXConfig("isReport", girc_.isReport);
			}
		}
		if (root.removeMember("basicInfo", &jv))
		{
			if (girc_.basic != jv.asBool())
			{
				girc_.basic = jv.asBool();
				std::string content = girc_.basic ? u8"打开基本信息上送" : u8"关闭基本信息上送";
				insertLog(eUserOperate, content);
				writeDriverXConfig("reportBasic", girc_.basic);
			}
		}
		if (root.removeMember("runtimeInfo", &jv))
		{
			if (girc_.runtime != jv.asBool())
			{
				girc_.runtime = jv.asBool();
				std::string content = girc_.runtime ? u8"打开运行信息上送" : u8"关闭运行信息上送";
				insertLog(eUserOperate, content);
				writeDriverXConfig("reportRuntime", girc_.runtime);
			}
			
		}
		if (root.removeMember("reportInterval", &jv))
		{
			if (girc_.interval != jv.asUInt64())
			{
				girc_.interval = jv.asUInt64();
				insertLog(eUserOperate, u8"修改信息上送频率:" + std::to_string(girc_.interval) + u8"秒");
				writeDriverXConfig("reportInterval", girc_.interval);
			}
		}
			
		response["result"]["resultCode"] = 0;
		response["result"]["resultError"] = "";
		
		res.set_content(response.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpsetCloudConfigInfo(const httplib::Request& req, httplib::Response& res)
	{
		insertLog(eUserOperate, u8"修改云端配置");
		//重载配置
		httpsetClientInfo(req, res, true);
		if (killSelf_)
			return;
		reload_ = true;
		mainCondVar_.notify_one();

		uploadInfoNow_ = true;
	}

	void CGatewayAIOT::httpsetPassWord(const httplib::Request& req, httplib::Response& res)
	{
		JSONCPP_STRING err;
		Json::Value root;
		Json::CharReaderBuilder builder;
		std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
		if (!reader->parse(req.body.c_str(), req.body.c_str() + req.body.size(), &root, &err))
		{
			std::cout << "parse comming message error = " << err << std::endl;
			httpresResult(res, "-1", "wrong request body");
			return;
		}

		Json::Value jv;
		std::string oldPassWord, newPassWord;
		if (!root.removeMember("oldPassWord", &jv))
		{
			httpresResult(res, "-1", "wrong request body");
			return;
		}
		oldPassWord = jv.asString();
		if (!root.removeMember("newPassWord", &jv))
		{
			httpresResult(res, "-1", "wrong request body");
			return;
		}
		newPassWord = jv.asString();

		Json::Value localUserInfo;
		std::ifstream ifs(USERINFO_PATH);
		if (ifs.is_open())
		{
			std::istreambuf_iterator<char> beg(ifs), end;
			std::string payload(beg, end);
			ifs.close();
			if (!reader->parse(payload.c_str(), payload.c_str() + payload.size(), &localUserInfo, &err))
			{
				httpresResult(res, "-1", "local userinfo is wrong");
				return;
			}
		}
		else
		{
			httpresResult(res, "-1", "local userinfo is wrong");
			return;
		}

		if (localUserInfo["passWord"].asString() != oldPassWord)
		{
			httpresResult(res, "-1", u8"原密码错误");
			return;
		}

		std::ofstream ofs(USERINFO_PATH);
		if (ofs.is_open())
		{
			std::string userInfo = "{\"userName\":\"admin\", \"passWord\": \"" + newPassWord + "\"}";
			ofs.write(userInfo.c_str(), userInfo.size());
			ofs.close();
			httpresResult(res, "0", "success");
			insertLog(eUserOperate, u8"修改密码");
		}
		else
		{
			httpresResult(res, "-1", "local userinfo is wrong");
		}
	}

	void CGatewayAIOT::httpsetOtherConfigInfo(const httplib::Request& req, httplib::Response& res)
	{
		auto& body = req.body;

		JSONCPP_STRING err;
		Json::Value root;
		Json::CharReaderBuilder builder;
		std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
		if (!reader->parse(body.c_str(), body.c_str() + body.size(), &root, &err))
		{
			httpresResult(res, "-1", "wrong request body");
			return;
		}
		Json::Value jv;
		if (root.removeMember("disConnCache", &jv))
		{
			if (enableLocalDataCaching_ != jv.asBool())
			{
				enableLocalDataCaching_ = jv.asBool();
				std::string content = enableLocalDataCaching_? u8"开启断线缓存":u8"关闭断线缓存";
				insertLog(eUserOperate, content);
				uploadInfoNow_ = true;
				writeDriverXConfig("EnableLocalDataCaching", enableLocalDataCaching_);
			}
		}
		if (root.removeMember("cloudControlAuth", &jv))
		{
			if (enableCloudControl_ != jv.asBool())
			{
				enableCloudControl_ = jv.asBool();
				std::string content = enableCloudControl_? u8"打开云端控制":u8"屏蔽云端控制";
				insertLog(eUserOperate, content);
				uploadInfoNow_ = true;
				writeDriverXConfig("EnableCloudControl", enableCloudControl_);
			}
		}
			

		httpresResult(res, "0", "success");
	}

	void CGatewayAIOT::httplogQuery(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("logQuery\n");

		auto& body = req.body;

		JSONCPP_STRING err;
		Json::Value root;
		Json::CharReaderBuilder builder;
		std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
		if (!reader->parse(body.c_str(), body.c_str() + body.size(), &root, &err))
		{
			httpresResult(res, "-1", "wrong request body");
			return;
		}

		SLogQueryMessage lqm;
		Json::Value jv;
		if (root.removeMember("pageNum", &jv))
			lqm.pageNum = jv.asInt();
		if (root.removeMember("pageSize", &jv))
			lqm.pageSize = jv.asInt();
		if (root.removeMember("createTimeBegin", &jv))
			lqm.createTimeBegin = jv.asInt64();
		if (root.removeMember("createTimeEnd", &jv))
			lqm.createTimeEnd = jv.asInt64();
		if (root.removeMember("type", &jv))
			lqm.type = jv.asInt();

		SLogMessage* lm = new SLogMessage();
		lm->type = eLogQuery;
		lm->lqm = &lqm;
		lc_->pushLog(lm);

		while (!lqm.finish)
		{
			std::this_thread::sleep_for(std::chrono::milliseconds(20));
		}

		Json::Value data;
		data["pageNum"] = lqm.pageNum;
		data["pageSize"] = lqm.pageSize;
		data["totalCount"] = lqm.totalCount;
		data["totalPage"] = lqm.totalPage;
		data["data"] = Json::arrayValue;
		int i = 0;
		for (auto it = lqm.vLog.begin(); it != lqm.vLog.end(); it++, i++)
		{
			//printf("log type:%d time:%lld content:%s\n", it->type, it->createTime, it->content.c_str());
			data["data"][i]["type"] = it->type;
			data["data"][i]["createTime"] = it->createTime;
			data["data"][i]["content"] = it->content.c_str();
		}

		Json::Value rsp, result;
		rsp["data"] = std::move(data);
		result["resultCode"] = lqm.resultCode;
		result["resultError"] = lqm.resultError;
		rsp["result"] = std::move(result);
		res.set_content(rsp.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpsetNetWorkConfig(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("setNetWorkConfig\n");

		auto& body = req.body;

		JSONCPP_STRING err;
		Json::Value root;
		Json::CharReaderBuilder builder;
		std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
		if (!reader->parse(body.c_str(), body.c_str() + body.size(), &root, &err))
		{
			httpresResult(res, "-1", "wrong request body");
			return;
		}
		if (!enableNetWorkChange_)
		{
			httpresResult(res, "-1", "cannot set");
			return;
		}

		std::string name, ipv4, netmask, gateway, dns;
		int dhcp = -1;
		Json::Value jv;
		if (root.removeMember("name", &jv))
			name = jv.asString();
		if (root.removeMember("ipv4", &jv))
			ipv4 = jv.asString();
		if (root.removeMember("netmask", &jv))
			netmask = jv.asString();
		if (root.removeMember("gateway", &jv))
			gateway = jv.asString();
		if (root.removeMember("dns", &jv))
			dns = jv.asString();
		if (root.removeMember("dhcp", &jv))
			dhcp = jv.asBool();
		printf("name:%s, ipv4:%s, netmask:%s, gateway:%s, dns:%s.\n", name.c_str(), ipv4.c_str(), netmask.c_str(), gateway.c_str(), dns.c_str());

		SNetworkConfig nwc;
		systemSetting_->getNetworkConfig(name, nwc);
		if (name.empty() || nwc.name != name)
		{
			httpresResult(res, "-1", "wrong name");
			return;
		}
		if (!nwc.enableChange || nwc.type == 4)
		{
			httpresResult(res, "-1", "cannot set");
			return;
		}

		std::string content = u8"修改网络配置 name:" + name + ",ipv4:" + ipv4 + ",netmask:" + netmask + ",gateway:" + gateway + ",dns:" + dns + ",dhcp:" + std::to_string(dhcp);
		insertLog(eUserOperate, content);

		if (ipv4.empty())
			ipv4 = nwc.ipv4;
		if (netmask.empty())
			netmask = nwc.netmask;

		std::string dnsArgs;
		if (!dns.empty())
		{
			for (int i = 0; i < dns.length(); i++)
			{
				if (dns[i] == ';') dns[i] = ' '; //作为参数时分开
			}
			dnsArgs += " " + dns;	
		}
		std::string args = " " + name + " " + nwc.ipv4 + " " + ipv4 + " " + netmask + " " + gateway + " " + dnsArgs;
		
		if (env_ == EGatewayEnv::E500)
		{
			std::string args = " " + name + " " + nwc.ipv4 + " " + ipv4 + " " + netmask + " " + gateway + " " + dnsArgs;
			systemSetting_->setNetWorkConfig(args);
			if (!dnsArgs.empty())
			{
				systemSetting_->setDnsConfig(dnsArgs);
			}
		}
		else if (env_ == EGatewayEnv::iEMSCOM4)
		{
			//std::string args = " " + name + " " + ipv4 + " " + netmask + " " + gateway + " " + dnsArgs;
			std::string args = " conn_name=" + name + ",new_ip=" + ipv4 + ",netmask=" + netmask + ",gateway=" + gateway + ",dns=" + dns;
			std::cout << "set iems network : " << args << std::endl;
			systemSetting_->setNetWorkConfig(args);
		}

		httpresResult(res, "0", "");
		uploadInfoNow_ = true;
	}

	void CGatewayAIOT::httpsetWirelessConnect(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("setWirelessConnect\n");

		auto& body = req.body;

		JSONCPP_STRING err;
		Json::Value root;
		Json::CharReaderBuilder builder;
		std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
		if (!reader->parse(body.c_str(), body.c_str() + body.size(), &root, &err))
		{
			httpresResult(res, "-1", "wrong request body");
			return;
		}
		if (!enableNetWorkChange_)
		{
			httpresResult(res, "-1", "cannot set");
			return;
		}

		std::string name, ssid, psk;
		Json::Value jv;
		if (root.removeMember("name", &jv))
			name = jv.asString();
		if (root.removeMember("ssid", &jv))
			ssid = jv.asString();
		if (root.removeMember("psk", &jv))
			psk = jv.asString();
		printf("name:%s, ssid:%s, psk:%s.\n", name.c_str(), ssid.c_str(), psk.c_str());

		if (psk.length() < 8 || psk.length() > 63)
		{
			httpresResult(res, "-1", "wrong psk length");
			return;
		}

		std::string content = u8"设置无线连接 name:" + name + ",ssid:" + ssid;
		insertLog(eUserOperate, content);

		std::string args = " " + name + " " + psk + " " + ssid;
		systemSetting_->setWirelessConnect(args);
		httpresResult(res, "0", "");

		uploadInfoNow_ = true;
	}

	void CGatewayAIOT::httpsetWirelessConfig(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("setWirelessConfig\n");

		auto& body = req.body;

		JSONCPP_STRING err;
		Json::Value root;
		Json::CharReaderBuilder builder;
		std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
		if (!reader->parse(body.c_str(), body.c_str() + body.size(), &root, &err))
		{
			httpresResult(res, "-1", "wrong request body");
			return;
		}
		if (!enableNetWorkChange_)
		{
			httpresResult(res, "-1", "cannot set");
			return;
		}

		std::string name, ipv4, netmask, gateway, dns;
		int dhcp = -1;
		Json::Value jv;
		if (root.removeMember("name", &jv))
			name = jv.asString();
		if (root.removeMember("ipv4", &jv))
			ipv4 = jv.asString();
		if (root.removeMember("netmask", &jv))
			netmask = jv.asString();
		if (root.removeMember("gateway", &jv))
			gateway = jv.asString();
		if (root.removeMember("dns", &jv))
			dns = jv.asString();
		printf("name:%s, ipv4:%s, netmask:%s, gateway:%s, dns:%s.\n", name.c_str(), ipv4.c_str(), netmask.c_str(), gateway.c_str(), dns.c_str());

		SNetworkConfig nwc;
		systemSetting_->getNetworkConfig(name, nwc);
		if (name.empty() || nwc.name != name)
		{
			httpresResult(res, "-1", "wrong name");
			return;
		}
		if (!nwc.enableChange || nwc.type == 4)
		{
			httpresResult(res, "-1", "cannot set");
			return;
		}

		std::string content = u8"修改无线网络配置 name:" + name + ",ipv4:" + ipv4 + ",netmask:" + netmask + ",gateway:" + gateway;
		insertLog(eUserOperate, content);

		if (ipv4.empty())
			ipv4 = nwc.ipv4;
		if (netmask.empty())
			netmask = nwc.netmask;
		if (gateway.empty())
			gateway = nwc.gateway;

		std::string args = " " + name + " " + ipv4 + " " + netmask + " " + gateway;
		systemSetting_->setWirelessConfig(args);

		if (!dns.empty())
		{
			for (int i = 0; i < dns.length(); i++)
			{
				if (dns[i] == ';') dns[i] = ' '; //作为参数时分开
			}
			std::string dnsArgs = " " + dns;
			systemSetting_->setDnsConfig(dnsArgs);
		}

		httpresResult(res, "0", "");

		uploadInfoNow_ = true;
	}

	void CGatewayAIOT::httplinkLogPackage(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("linkLogPackage\n");

		auto& body = req.body;

		JSONCPP_STRING err;
		Json::Value root;
		Json::CharReaderBuilder builder;
		std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
		if (!reader->parse(body.c_str(), body.c_str() + body.size(), &root, &err))
		{
			httpresResult(res, "-1", "wrong request body");
			return;
		}

		std::string link;
		Json::Value jv;
		if (root.removeMember("link", &jv) && jv.isString())
		{
			link = jv.asString();
		}
		else
		{
			httpresResult(res, "-1", "no link in request body");
			return;
		}
		auto slc = slcs_.find(link);
		if (slc == slcs_.end())
		{
			httpresResult(res, "-1", "request params error = link not found");
			return;
		}
		std::unordered_map<std::string, std::vector<std::string>> filesMap;
		if (!root.removeMember("log", &jv) || jv.type() != Json::objectValue)
		{
			httpresResult(res, "-1", "no log in request body");
			return;
		}
		for (auto item = jv.begin(); item != jv.end(); item++)
		{
			if (!item->isArray())
				continue;
			for (auto itemStr = item->begin(); itemStr != item->end(); itemStr++)
			{
				filesMap[item.name()].push_back(itemStr->asString());
			}
		}

		std::regex reg1("^[a-zA-Z0-9]+_[0-9]{4}_[0-9]{2}_[0-9]{2}$");
		std::regex reg2("^[a-zA-Z0-9]+_[0-9]{4}_[0-9]{2}_[0-9]{2}_[0-9]{2}_[0-9]{2}_[0-9]{2}$");

		std::string zip_file = link + std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count()) + ".zip";
		struct zip_t* zip = zip_open(zip_file.c_str(), ZIP_DEFAULT_COMPRESSION_LEVEL, 'w');
		for (auto mapIter = filesMap.begin(); mapIter != filesMap.end(); mapIter++)
		{
			for (auto vecIter = mapIter->second.begin(); vecIter != mapIter->second.end(); vecIter++)
			{
				//std::string filePath = std::string(LOG_PATH) + "/" + link + slc->second.alias + "/" + mapIter->first + "/" + *vecIter;
				std::string filePath = CPathManager::getInstance().getChannelLogPath(link, slc->second.alias) + mapIter->first + "/" + *vecIter;
				//std::cout << filePath << std::endl;
				std::string itemStr = mapIter->first + "/" + *vecIter;
				if (std::regex_match(*vecIter, reg1))
				{
					FILE* file = fopen(filePath.c_str(), "rb");
					if (!file) 
					{
						std::cout << "Failed to open file : " << filePath << std::endl;
						continue;
					}
					// 读取文件内容
					fseek(file, 0, SEEK_END);
					size_t fileSize = ftell(file);
					fseek(file, 0, SEEK_SET);
					char* buffer = new char[fileSize];
					fread(buffer, 1, fileSize, file);
					// 关闭文件
					fclose(file);
					std::remove("tempFile");
					std::ofstream tempFile("tempFile", std::ios::binary);
					if (tempFile.is_open()) {
						tempFile.write(buffer, fileSize);
						tempFile.close();
					}
					else
					{
						std::cout << "Failed to write to temporary file" << std::endl;
						continue;
					}
					zip_entry_open(zip, itemStr.c_str());
					zip_entry_fwrite(zip, "tempFile");
					zip_entry_close(zip);
					std::remove("tempFile");
					delete[] buffer;
				}
				else if (std::regex_match(*vecIter, reg2))
				{
					zip_entry_open(zip, itemStr.c_str());
					zip_entry_fwrite(zip, filePath.c_str());
					zip_entry_close(zip);
				}
			}
		}
		zip_close(zip);

		// 打开要下载的压缩包文件
		std::ifstream input_file(zip_file.c_str(), std::ios::binary);
		if (!input_file.is_open()) {
			httpresResult(res, "-1", "Internal Server Error");
			return;
		}

		// 读取文件内容并设置响应
		std::vector<char> file_contents((std::istreambuf_iterator<char>(input_file)), std::istreambuf_iterator<char>());
		res.set_content(std::string(file_contents.begin(), file_contents.end()), "application/zip");

		// 关闭文件并删除
		input_file.close();
		remove(zip_file.c_str());
	}

	void CGatewayAIOT::httpsetSyncTime(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("setSyncTime\n");

		auto& body = req.body;

		JSONCPP_STRING err;
		Json::Value root;
		Json::CharReaderBuilder builder;
		std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
		if (!reader->parse(body.c_str(), body.c_str() + body.size(), &root, &err))
		{
			httpresResult(res, "-1", "wrong request body");
			return;
		}

		STimeSyncInfo tsi;
		if (!systemSetting_->getSyncTime(tsi))
		{
			httpresResult(res, "-1", "get syncTime info failed");
			return;
		}
		
		Json::Value jv;
		if (root.removeMember("syncSwitch", &jv) && jv.isBool())
		{
			if (tsi.Switch != jv.asBool())
			{
				tsi.Switch = jv.asBool();

				std::string content = tsi.Switch ? u8"开启时间自动同步" : u8"关闭时间自动同步";
				insertLog(eUserOperate, content);
			}
		}
		if (root.removeMember("syncAddress", &jv) && jv.isString())
		{
			if (tsi.address != jv.asString())
			{
				tsi.address = jv.asString();

				std::string content = u8"修改时间同步地址：" + tsi.address;
				insertLog(eUserOperate, content);
			}
		}

		std::string args = " " + tsi.address + (tsi.Switch ? " true":" false");
		systemSetting_->setSyncTime(args);

		httpresResult(res, "0", "");
	}

	void CGatewayAIOT::httpdoSyncTime(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("doSyncTime\n");

		auto& body = req.body;

		JSONCPP_STRING err;
		Json::Value root;
		Json::CharReaderBuilder builder;
		std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
		if (!reader->parse(body.c_str(), body.c_str() + body.size(), &root, &err))
		{
			httpresResult(res, "-1", "wrong request body");
			return;
		}

		std::string syncAddress;

		Json::Value jv;
		if (root.removeMember("syncAddress", &jv) && jv.isString())
		{
			syncAddress = jv.asString();
		}

		if (syncAddress.empty())
		{
			httpresResult(res, "-1", "wrong request body");
			return;
		}

		httpresResult(res, "0", "");

		std::string content = u8"手动同步时间，地址：" + syncAddress;
		insertLog(eUserOperate, content);

		std::string args = " " + syncAddress;
		systemSetting_->doSyncTime(args);
	}

	void CGatewayAIOT::httpgetEnv(const httplib::Request& req, httplib::Response& res)
	{
		Json::Value result;
		result["data"] = getLocalEnv();
		result["result"]["resultCode"] = "0";
		result["result"]["resultError"] = "success";
		res.set_content(result.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpgetCollectType(const httplib::Request& req, httplib::Response& res)
	{
	}

	//put
	void CGatewayAIOT::httpupload(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("upload\n");
	}

	void CGatewayAIOT::httpuploadDLL(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("uploadDLL\n");
		auto&& driverDll = req.get_file_value("file");
		//std::string upFileName = std::string(DRIVER_PLUGIN_PATH).append("/").append(driverDll.filename);
		//if (isMultiGatewayEnv)
		//{
		//	upFileName = launchPath + "/../plugins/" + driverDll.filename;
		//}
		std::string upFileName = CPathManager::getInstance().getPluginPath() + "/" + driverDll.filename;
		std::ofstream ofs(upFileName, std::ios::binary);
		ofs << driverDll.content;
		res.set_content("done", "text/plain");
	}

	void CGatewayAIOT::httpuploadCrt(const httplib::Request& req, httplib::Response& res)
	{
		// 检查是否有上传的文件
		if (!req.has_file("file")) 
		{
			httpresResult(res, "-1", "No file uploaded");
			return;
		}

		// 获取上传的文件
		const auto& file = req.get_file_value("file");

		// 保存上传的文件到服务器本地
		//std::ofstream ofs(CRT_PATH + file.filename, std::ios::binary);
		std::ofstream ofs(CPathManager::getInstance().getCrtPath() + file.filename, std::ios::binary);
		if (ofs.is_open())
		{
			ofs << file.content;
			ofs.close();
			httpresResult(res, "0", "File uploaded successfully");
		}
		else
		{
			httpresResult(res, "-1", "File uploaded failed");
		}
	}

	void CGatewayAIOT::httpresResult(httplib::Response& res, const std::string& resultCode, const std::string& resultError)
	{
		Json::Value root, result;
		result["resultCode"] = resultCode;
		result["resultError"] = resultError;
		root["result"] = std::move(result);
		res.set_content(root.toStyledString(), "application/json");
		return;
	}
	
	void CGatewayAIOT::httpConfigGetConfigurationSource(const httplib::Request& req, httplib::Response& res)
	{
		//std::cout << __func__ << std::endl;

		Json::Value ret;
		ret["result"]["resultCode"] = "0";
		ret["result"]["resultError"] = "success.";
		ret["data"]["type"] = collectTypeToString[configSource_];
		res.set_content(ret.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpConfigSetConfigurationSource(const httplib::Request& req, httplib::Response& res)
	{
		//std::cout << __func__ << std::endl;
		//std::cout << req.body << std::endl;
		auto& body = req.body;
		Json::Value root, ret;
		if (!StringToJsonValue(body, root))
		{
			ret["result"]["resultCode"] = "-1";
			ret["result"]["resultError"] = "wrong body.";
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}
		if (!root.isMember("type"))
		{
			ret["result"]["resultCode"] = "-1";
			ret["result"]["resultError"] = "wrong body.";
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}

		if (root["type"].asString() == collectTypeToString[EConfigurationSource::ELocal])
		{
			configSource_ = EConfigurationSource::ELocal;
			writeDriverXConfig("ConfigurationSouce", collectTypeToString[EConfigurationSource::ELocal]);
		}
		else if (root["type"].asString() == collectTypeToString[EConfigurationSource::ECloud])
		{
			configSource_ = EConfigurationSource::ECloud;
			writeDriverXConfig("ConfigurationSouce", collectTypeToString[EConfigurationSource::ECloud]);
		}
		else
		{
			ret["result"]["resultCode"] = "-1";
			ret["result"]["resultError"] = "unknow type.";
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}
		ret["result"]["resultCode"] = "0";
		ret["result"]["resultError"] = "success.";
		res.set_content(ret.toStyledString(), "application/json");

		//std::this_thread::sleep_for(std::chrono::seconds(1));

		if (killSelf_)
			_exit(0);
		return;
	}

	void CGatewayAIOT::httpConfigSetDeviceTemplate(const httplib::Request& req, httplib::Response& res)
	{
		std::cout << __func__ << std::endl;
		Json::Value ret, result;

		auto& body = req.body;
		Json::Value root;
		if (!StringToJsonValue(body, root))
		{
			result["resultCode"] = "-1";
			result["resultError"] = "Unable to parse body into json : " + body;
			std::cout << "wrong body." << std::endl;

			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
		}
		//std::cout << req.body << std::endl;

		if (root.isMember("id"))
		{
			bool sqlret = configurationCaching_.updateDeviceTemplate(root["id"].asString(), root.toStyledString());
			if (!sqlret)
			{
				result["resultCode"] = "-1";
				result["resultError"] = "insert or update device template failed.";
				ret["result"] = std::move(result);
				res.set_content(ret.toStyledString(), "application/json");
			}
		}

		result["resultCode"] = "0";
		result["resultError"] = "success";
		ret["result"] = std::move(result);
		res.set_content(ret.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpConfigGetDeviceTemplate(const httplib::Request& req, httplib::Response& res)
	{
		//std::cout << __func__ << std::endl;

		auto map = configurationCaching_.selectDeviceTemplate();
		Json::Value ret, datas;
		for (auto it = map.begin(); it != map.end(); it++)
		{
			Json::Value tmp;
			if (StringToJsonValue(it->second, tmp))
				datas.append(tmp);
		}
		ret["data"]["template"] = datas;
		ret["result"]["resultCode"] = "0";
		ret["result"]["resultError"] = "success";
	
		res.set_content(ret.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpConfigDelDeviceTemplate(const httplib::Request& req, httplib::Response& res)
	{
		//std::cout << __func__ << std::endl;
		Json::Value ret, result;

		auto& body = req.body;
		Json::Value root;
		if (!StringToJsonValue(body, root))
		{
			result["resultCode"] = "-1";
			result["resultError"] = "Unable to parse body into json : " + body;
			std::cout << "wrong body." << std::endl;
			
			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
		}
		//std::cout << req.body << std::endl;

		if (root.isMember("id"))
		{
			bool sqlret = configurationCaching_.deleteDeviceTemplate(root["id"].asString());
			if (!sqlret)
			{
				result["resultCode"] = "-1";
				result["resultError"] = "delete device template failed.";
				ret["result"] = std::move(result);
				res.set_content(ret.toStyledString(), "application/json");
			}
		}


		result["resultCode"] = "0";
		result["resultError"] = "success";
		ret["result"] = std::move(result);
		res.set_content(ret.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpConfigGetConfiguration(const httplib::Request& req, httplib::Response& res)
	{
		//std::cout << __func__ << std::endl;

		Json::Value cfg, result;

/*
		struct SProtocol_func
		{
			std::string defaultValue;
			std::string	label;
			int	level;
			std::string	name;
			std::string	value;
		};
		struct SPoint_func
		{
			std::string id;
			std::string code;
			std::string name;
			std::map<std::string, SProtocol_func> protocol;
		};
		struct SDev_func
		{
			std::string id;
			std::string name;
			std::map<std::string, SProtocol_func> protocol;
		};
		struct SLink_func
		{
			std::string id;
			std::string name;
			std::map<std::string, SProtocol_func> protocol;
			std::map<std::string, SDev_func> devices;
		};
		struct STransTemplate_func
		{
			std::string linkClass;
			std::string driverFile;
			std::map<std::string, SLink_func>instances;
		};
		std::map<std::string, STransTemplate_func> tmpMap;
		for (auto& lit : slcs_)
		{
			if (tmpMap.find(lit.second.className) == tmpMap.end())
			{
				tmpMap[lit.second.className].driverFile = lit.second.driverType;
				tmpMap[lit.second.className].linkClass = lit.second.className;
			}

			std::cout << tmpMap[lit.second.className].driverFile << std::endl;

			Json::Value device;
			device["id"] = lit.first;
			device["name"] = lit.second.alias;
			device["protocol"] = Json::arrayValue;
			device["points"] = Json::arrayValue;
			for (auto& dit : lit.second.deviceTable)
			{
				for (auto& pit : dit.second.pointTable)
				{
					Json::Value point;
					point["id"] = pit.first;
					point["code"] = pit.second.name;
					point["name"] = pit.second.alias;
					point["protocol"] = Json::arrayValue;
					device["points"].append(point);
				}
			}
		}
*/


		cfg["data"] = localConfigJson_;
		result["resultCode"] = "0";
		result["resultError"] = "success";
		cfg["result"] = std::move(result);
		
		//std::cout << cfg.toStyledString() << std::endl;

		res.set_content(cfg.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpConfigGetCfgPoints(const httplib::Request& req, httplib::Response& res)
	{
		//std::cout << __func__ << std::endl;

		Json::Value ret, rdata, result;
		result["resultCode"] = "-1";
		result["resultError"] = "wrong request body.";
		ret["data"] = std::move(rdata);
		ret["result"] = std::move(result);

		auto& body = req.body;

		JSONCPP_STRING err;
		Json::Value root;
		Json::CharReaderBuilder builder;
		std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
		if (!reader->parse(body.c_str(), body.c_str() + body.size(), &root, &err))
		{
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}


		//auto body = req.body;
		//if (body.empty())
		//{
		//	res.set_content(ret.toStyledString(), "application/json");
		//	return;
		//}
		//Json::Value root;
		//if (!StringToJsonValue(body, root));
		//{
		//	res.set_content(ret.toStyledString(), "application/json");
		//	return;
		//}
		std::string channelid = root["link"].asString();
		std::string deviceid = root["device"].asString();
		if (channelid.empty())
		{
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}
		auto slc = slcs_.find(channelid);
		if (slc == slcs_.end())
		{
			ret["result"]["resultError"] = "link not found, linkid = " + channelid;
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}

		auto sdc = slc->second.deviceTable.find(deviceid);
		if (sdc == slc->second.deviceTable.end())
		{
			ret["result"]["resultError"] = "device not found";
			res.set_content(ret.toStyledString(), "application/json"); 
			return;
		}
		auto pointsMap = sdc->second.pointTable;

		DRIVER::DeviceDataSet dds;
		driverEngine_->getBufferedData(channelid, deviceid, dds);
		//std::cout << dds.size() << std::endl;
		
		Json::Value datas;
		for (auto& spc : pointsMap)
		{
			Json::Value pi;
			pi["id"] = spc.first;
			pi["code"] = spc.second.name;
			pi["name"] = spc.second.alias;
			pi["pointType"] = spc.second.pointType;
			pi["type"] = spc.second.type;
			pi["description"] = spc.second.description;

			pi["value"] = "invalid";
			pi["quality"] = Json::arrayValue;
			pi["quality"].append("Bad");

			if (dds.find(spc.first) != dds.end())
			{
				for (auto& spp : spc.second.pointProtocol)
				{
					pi["protocol"][spp.first] = spp.second;
				}
				const auto& value = dds[spc.first].value;
				switch (value.getType())
				{
				case SValue::eValueInt:  pi["value"] = value.toInt<int64_t>(); break;
				case SValue::eValueUInt:  pi["value"] = value.toInt<uint64_t>(); break;
				case SValue::eValueReal:  pi["value"] = value.toFloat<double>(); break;
				case SValue::eValueString: pi["value"] = value.toString(); break;
				case SValue::eValueBoolean:  pi["value"] = value.toBool(); break;
				case SValue::eValueJson: pi["value"] = value.toString(); break;
				default:; break;
				}
				pi["quality"] = Json::arrayValue;
				if (dds[spc.first].quality == -1)
					pi["quality"][0] = "scanoff";
				pi["timestamp"] = dds[spc.first].timestamp;
			}
			datas["points"].append(pi);
		}

		ret["data"] = std::move(datas);
		ret["result"]["resultCode"] = "0";
		ret["result"]["resultError"] = "success";
		res.set_content(ret.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpConfigSetConfigurationTemplate(const httplib::Request& req, httplib::Response& res)
	{
		//std::cout << __func__ << std::endl;
		//std::cout << req.body << std::endl;
		
		Json::Value  ret, result;
		result["resultCode"] = "0";
		result["resultError"] = "success";
		auto& body = req.body;
		Json::Value root;
		if (!StringToJsonValue(body, root))
		{
			result["resultCode"] = "-1";
			result["resultError"] = "wrong request body.";
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}


		if (root.isMember("id"))
		{
			bool sqlret = configurationCaching_.updateConfigurationTemplate(root["id"].asString(), root.toStyledString());
			if (!sqlret)
			{
				result["resultCode"] = "-1";
				result["resultError"] = "insert or update device template failed.";
				ret["result"] = std::move(result);
				res.set_content(ret.toStyledString(), "application/json");
			}
		}


		ret["result"] = std::move(result);
		res.set_content(ret.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpConfigGetConfigurationTemplate(const httplib::Request& req, httplib::Response& res)
	{
		std::cout << __func__ << std::endl;

		auto map = configurationCaching_.selectConfigurationTemplate();
		Json::Value ret, datas;
		for (auto it = map.begin(); it != map.end(); it++)
		{
			Json::Value tmp;
			if (StringToJsonValue(it->second, tmp))
				datas.append(tmp);
		}
		ret["data"]["template"] = datas;
		ret["result"]["resultCode"] = "0";
		ret["result"]["resultError"] = "success";

		res.set_content(ret.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpConfigDelConfigurationTemplate(const httplib::Request& req, httplib::Response& res)
	{
		//std::cout << __func__ << std::endl;
		Json::Value ret, result;

		auto& body = req.body;
		Json::Value root;
		if (!StringToJsonValue(body, root))
		{
			result["resultCode"] = "-1";
			result["resultError"] = "Unable to parse body into json : " + body;
			std::cout << "wrong body." << std::endl;

			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
		}
		//std::cout << req.body << std::endl;

		if (root.isMember("id"))
		{
			bool sqlret = configurationCaching_.deleteConfigurationTemplate(root["id"].asString());
			if (!sqlret)
			{
				result["resultCode"] = "-1";
				result["resultError"] = "delete configuration template failed.";
				ret["result"] = std::move(result);
				res.set_content(ret.toStyledString(), "application/json");
			}
		}


		result["resultCode"] = "0";
		result["resultError"] = "success";
		ret["result"] = std::move(result);
		res.set_content(ret.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpConfigGetLinkTemplate(const httplib::Request& req, httplib::Response& res)
	{
		//std::cout << __func__ << std::endl;
		
		Json::Value ret, result;

		//std::cout << localLinkTemplates_.size() << std::endl;
		for (const auto& it : localLinkTemplates_)
		{
			Json::Value link, channel, device, point, driverFile;
			link["name"] = it.first;
			for (auto df : it.second.driverFile)
			{
				driverFile[df.first] = df.second;
			}
			for (auto ag : it.second.arguments)
			{
				Json::Value tmpobj;
				tmpobj["name"] = ag.name;
				tmpobj["label"] = ag.label;
				tmpobj["description"] = ag.description;
				tmpobj["type"] = ag.type;
				tmpobj["defaultValue"] = ag.defaultValue;
				tmpobj["inputType"] = ag.inputType;
				tmpobj["items"] = ag.items;
				tmpobj["level"] = ag.level;
				tmpobj["visible"] = ag.visible;
				tmpobj["required"] = ag.required;
				switch (ag.level)
				{
				case SClassLink::SArgumentLevle::EChannel: 
					channel.append(tmpobj);
					break;
				case SClassLink::SArgumentLevle::EDevice: 
					device.append(tmpobj);
					break;
				case SClassLink::SArgumentLevle::EPointAddress: 
				case SClassLink::SArgumentLevle::EPointDatType:
					point.append(tmpobj);
					break;
				default:
					break;
				}
			}
			for (auto co : it.second.connection)
			{
				Json::Value tmpobj;
				tmpobj["name"] = co.name;
				tmpobj["label"] = co.label;
				tmpobj["description"] = co.description;
				tmpobj["type"] = co.type;
				tmpobj["defaultValue"] = co.defaultValue;
				tmpobj["inputType"] = co.inputType;
				tmpobj["items"] = co.items;
				tmpobj["level"] = co.level;
				tmpobj["visible"] = co.visible;
				tmpobj["required"] = co.required;
				tmpobj["useIEMSCom"] = co.useIEMSCom;
				switch (co.level)
				{
				case SClassLink::SArgumentLevle::EChannel:
					channel.append(tmpobj);
					break;
				case SClassLink::SArgumentLevle::EDevice:
					device.append(tmpobj);
					break;
				case SClassLink::SArgumentLevle::EPointAddress:
				case SClassLink::SArgumentLevle::EPointDatType:
					point.append(tmpobj);
					break;
				default:
					break;
				}
			}
			link["channel"] = std::move(channel);
			link["device"] = std::move(device);
			link["point"] = std::move(point);
			link["driverFile"] = std::move(driverFile);
			link["iemsProtocolClass"] = it.second.iemsProtocolClass;
			link["iemsProtocolInterfaceType"] = it.second.iemsProtocolInterfaceType;
			ret["data"].append(link);
		}
		result["resultCode"] = "0";
		result["resultError"] = "success";
		ret["result"] = std::move(result);
		res.set_content(ret.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpConfigSetLocalConfiguration(const httplib::Request& req, httplib::Response& res)
	{
		//std::cout << __func__ << std::endl;
		
		Json::Value  ret, result;

		auto& body = req.body;
		Json::Value  root;

		if (running_.load())
		{
			result["resultCode"] = "-1";
			result["resultError"] = "gateway is running.";
			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}

		if (!StringToJsonValue(body, root))
		{
			result["resultCode"] = "-1";
			result["resultError"] = "wrong body.";
			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}

		//std::cout << root.toStyledString() << std::endl;

		std::string configPath = CPathManager::getInstance().getLocalConfigPath();
		std::ofstream ofs(configPath);
		if (ofs.is_open())
		{
			ofs.write(body.c_str(), body.size());
			ofs.close();
			result["resultCode"] = "0";
			result["resultError"] = "success";
		}
		else
		{
			result["resultCode"] = "-1";
			result["resultError"] = "open local config file failed.";
		}
		loadLocalConfig(sog_, slcs_);

		ret["result"] = std::move(result);
		res.set_content(ret.toStyledString(), "application/json");
	}
	
	void CGatewayAIOT::httpConfigStartLocalConfigurationWork(const httplib::Request& req, httplib::Response& res)
	{
		if (configSource_ == EConfigurationSource::ECloud)
		{
			Json::Value result, ret;
			result["resultCode"] = "-1";
			result["resultError"] = "data config from cloud.";
			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}

		if (startLocalConfigWork())
		{
			localConfigDriverRunningState_ = true;
			setLocalConfigRunningState();

			Json::Value result, ret;
			result["resultCode"] = "0";
			result["resultError"] = "success";
			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}
		else
		{
			Json::Value result, ret;
			result["resultCode"] = "-1";
			result["resultError"] = "failed";
			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}
	}

	void CGatewayAIOT::httpConfigStopLocalConfigurationWork(const httplib::Request& req, httplib::Response& res)
	{
		localConfigDriverRunningState_ = false;
		setLocalConfigRunningState();

		if (configSource_ == EConfigurationSource::ECloud)
		{
			Json::Value result, ret;
			result["resultCode"] = "-1";
			result["resultError"] = "data config from cloud.";
			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}

		//if (updateDataToOpcuaServerThread_ == nullptr)
		//{
		//	Json::Value result, ret;
		//	result["code"] = "-1";
		//	result["msg"] = "worker not start.";
		//	ret["result"] = std::move(result);
		//	res.set_content(ret.toStyledString(), "application/json");
		//	return;
		//}

		running_.exchange(false);

		if (updateDataToOpcuaServerThread_->joinable())
		{
			updateDataToOpcuaServerThread_->join();
			updateDataToOpcuaServerThread_ = nullptr;
		}

		bool stop = stopAllDriver();


		Json::Value result, ret;
		result["resultCode"] = stop ? "0" : "-1";
		result["resultError"] = stop ? "success" : "false";
		ret["result"] = std::move(result);
		res.set_content(ret.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpConfigGetRunningState(const httplib::Request& req, httplib::Response& res)
	{
		//std::cout << __func__ << std::endl;

		Json::Value ret, result, data;

		data["state"] = running_.load();
		result["resultCode"] = "0";
		result["resultError"] = "success";


		ret["data"] = std::move(data);
		ret["result"] = std::move(result);

		res.set_content(ret.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpConfigGetOPCServerState(const httplib::Request& req, httplib::Response& res)
	{
		//std::cout << __func__ << std::endl;

		Json::Value ret, result, data;
		data["state"] = opcServer_.isRunning();
		//data["state"] = true;
		data["endpoint"] = opcServer_.endpoint();
		result["resultCode"] = "0";
		result["resultError"] = "success";
		ret["data"] = std::move(data);
		ret["result"] = std::move(result);
		res.set_content(ret.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpConfigSetOPCServerState(const httplib::Request& req, httplib::Response& res)
	{
		//std::cout << __func__ << std::endl << req.body << std::endl;

		if (configSource_ == EConfigurationSource::ECloud)
		{
			Json::Value result, ret;
			result["resultCode"] = "-1";
			result["resultError"] = "data config from cloud.";
			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}

		Json::Value ret, result, data;

		auto& body = req.body;
		Json::Value root;
		if (!StringToJsonValue(body, root))
		{
			result["resultCode"] = "-1";
			result["resultError"] = "wrong request body.";
			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}

		if (!root.isMember("state") || !root.isMember("endpoint"))
		{
			result["resultCode"] = "-1";
			result["resultError"] = "wrong request body.";
			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}

		bool state = root["state"].asBool();
		std::string endpoint = root["endpoint"].asString();
		if (state)
		{
			if (opcServer_.isRunning())
			{
				result["resultCode"] = "-1";
				result["resultError"] = "opcua server is running.";
				ret["result"] = std::move(result);
				res.set_content(ret.toStyledString(), "application/json");
				return;
			}

			opcuaServerEndpoint_ = endpoint;
			localConfigOPCUARunningState_ = true;
			setLocalConfigRunningState();

			startLocalConfigOPCUAWrok();
		}
		else
		{
			localConfigOPCUARunningState_ = false;
			opcuaServerEndpoint_ = endpoint;
			setLocalConfigRunningState();

			opcServer_.stop();
		}

		result["resultCode"] = "0";
		result["resultError"] = "success";
		ret["result"] = std::move(result);
		res.set_content(ret.toStyledString(), "application/json");
		return;
	}

	void CGatewayAIOT::httpConfigSetPointValue(const httplib::Request& req, httplib::Response& res)
	{
		logger_->info("httpConfigSetPointValue\n");

		Json::Value result, ret;
		auto& body = req.body;
		Json::Value root;
		if (!StringToJsonValue(body, root))
		{
			result["resultCode"] = "-1";
			result["resultError"] = "wrong json body.";
			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}
		std::string linkId = root["linkId"].asString();
		std::string linkName = root["linkName"].asString();
		std::string deviceId = root["deviceId"].asString();
		std::string deviceName = root["deviceName"].asString();
		std::string pointId = root["pointId"].asString();
		std::string pointCode = root["pointCode"].asString();
		std::string pointName = root["pointName"].asString();

		if (slcs_.find(linkId) == slcs_.end())
		{
			result["resultCode"] = "-1";
			result["resultError"] = "link not found, linkName = " + linkName;
			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}
		auto& link = slcs_[linkId];
		if (link.deviceTable.find(deviceId) == link.deviceTable.end())
		{
			result["resultCode"] = "-1";
			result["resultError"] = "device not found, deviceName = "+ deviceName;
			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}
		
		auto& device = link.deviceTable[deviceId];
		if (device.pointTable.find(pointId) == device.pointTable.end())
		{
			result["resultCode"] = "-1";
			result["resultError"] = "point not found, pointName = " + pointName;
			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}

		Json::Value controlValue;
		controlValue["linkid"] = linkId;
		controlValue["deviceid"] = deviceId;
		controlValue["code"] = pointId;
		controlValue["type"] = "control";
		controlValue["value"] = root["value"];

		SCloudMessageSptr cloudMessageSptr(new SCloudMessage);
		cloudMessageSptr->payload = controlValue.toStyledString();
		//std::cout << cloudMessageSptr->payload << std::endl;
		if (control(cloudMessageSptr).compare("succeed") == 0)
		{
			httpresResult(res, "0", "success");
		}
		else
		{
			httpresResult(res, "-1", "failed");
		}
	}

	void CGatewayAIOT::httpConfigGetIEMSConfig(const httplib::Request& req, httplib::Response& res)
	{
		Json::Value result, ret;
		result["resultCode"] = "0";
		result["resultError"] = "success.";

		ret["data"]["config"] = iemsConfig_.toJsonValue();
		ret["result"] = std::move(result);
		res.set_content(ret.toStyledString(), "application/json");
	}

	void CGatewayAIOT::httpConfigGetIEMSDeviceLogs(const httplib::Request& req, httplib::Response& res)
	{
		auto& body = req.body;
		Json::Value root, result, ret;
		if (!StringToJsonValue(body, root))
		{
			result["resultCode"] = "-1";
			result["resultError"] = "wrong json body.";
			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}
		if (!root.isMember("linkId"))
		{
			result["resultCode"] = "-1";
			result["resultError"] = "linkId is empty.";
			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}
		if (!root.isMember("deviceId"))
		{
			result["resultCode"] = "-1";
			result["resultError"] = "deviceId is empty.";
			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}
		std::string linkId = root["linkId"].asString();
		std::string deviceId = root["deviceId"].asString();

		auto channelIter = slcs_.find(linkId);
		if (channelIter == slcs_.end())
		{
			result["resultCode"] = "-1";
			result["resultError"] = "can not find link, linkId = " + linkId;
			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}
		auto deviceIter = channelIter->second.deviceTable.find(deviceId);
		if (deviceIter == channelIter->second.deviceTable.end())
		{
			result["resultCode"] = "-1";
			result["resultError"] = "can not find device, deviceId = " + deviceId;
			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}
	
		std::string logPath = CPathManager::getInstance().getChannelLogPath(channelIter->first, channelIter->second.alias);
		std::string packetPath = logPath + DRIVER::logLevel2String(DRIVER::ELogLevel::eLogPacket);
#ifdef WIN32
		packetPath = UtfToGbk(packetPath.c_str());
#endif //WIN32
		std::vector<SFileInfo> filesInfo;
		getFilesInfo(packetPath, filesInfo);
		uint64_t lastTime = 0;
		std::string fileName;
		for (auto it = filesInfo.begin(); it != filesInfo.end(); it++)
		{
			if (it->modificationTime > lastTime)
			{
				lastTime = it->modificationTime;
				fileName = it->name;
			}
		}

		std::string log_path = CPathManager::getInstance().getChannelLog(channelIter->first, channelIter->second.alias, DRIVER::logLevel2String(DRIVER::ELogLevel::eLogPacket), fileName);
#ifdef WIN32
		log_path = UtfToGbk(log_path.c_str());
#endif 
		std::ifstream ifs(log_path.c_str());
		if (ifs.is_open())
		{
			std::string chstr = std::string("[" + linkId + "]");
			std::string destr = std::string("[" + deviceId + "]");
			std::string sendstr = std::string("[send]");
			std::string recvstr = std::string("[recv]");
			std::string line;
			while (std::getline(ifs, line)) 
			{
				//if (line.find(deviceId) != std::string::npos)

				if (line.find(chstr) != std::string::npos &&
					line.find(destr) != std::string::npos)
				{
					UTILS::replaceStrAll(line, chstr, channelIter->second.alias);
					UTILS::replaceStrAll(line, destr, deviceIter->second.alias);
					UTILS::eraseStrAll(line, sendstr);
					UTILS::eraseStrAll(line, recvstr);
					ret["data"]["log"].append(line);
				}
			}
			ifs.close();
		}
		else
		{
			result["resultCode"] = "-1";
			result["resultError"] = "get logs failed.";
			ret["result"] = std::move(result);
			res.set_content(ret.toStyledString(), "application/json");
			return;
		}


		result["resultCode"] = "0";
		result["resultError"] = "success";
		ret["result"] = std::move(result);
		res.set_content(ret.toStyledString(), "application/json");
	}

	// web start

	bool CGatewayAIOT::initial(GatewayConfig& cfg)
	{
		CGateway::initial(cfg);

		enableModbusServer_ = cfg["EnableModbusServer"] == "true";
		enableLocalDataCaching_ = cfg["EnableLocalDataCaching"] == "true";
		localDataCachingCount_ = atoi(cfg["LocalDataCachingCount"].c_str());
		enableCloudControl_ = cfg["EnableCloudControl"] == "true";
		httpServerPort_ = atoi(cfg["HttpServerPort"].c_str());
		systemSettingFloderPath_ = cfg["SystemSettingFloderPath"];
		enableNetWorkChange_ = cfg["EnableNetWorkChange"] == "true";
		regularlyReportInterval_ = atoi(cfg["RegularlyReportInterval"].c_str());
		recordReportData_ = cfg["RecordReportData"] == "true";
		killSelf_ = cfg["KillSelf"] == "true";
		logFileSize_ = atoi(cfg["LogFileSize"].c_str());
		logBaseSize_ = atoi(cfg["LogBaseSize"].c_str());
		logFileCount_ = atoi(cfg["LogFileCount"].c_str());
		logLevel_ = atoi(cfg["LogLevel"].c_str());

		std::string cfgSource = cfg["ConfigurationSouce"];
		if (cfgSource == collectTypeToString[EConfigurationSource::ECloud])
			configSource_ = EConfigurationSource::ECloud;
		else if (cfgSource == collectTypeToString[EConfigurationSource::ELocal])
			configSource_ = EConfigurationSource::ELocal;

		girc_.isReport = cfg["isReport"] == "true";
		girc_.basic = cfg["reportBasic"] == "true";
		girc_.runtime = cfg["reportRuntime"] == "true";
		girc_.interval = atoi(cfg["reportInterval"].c_str());
		serialNumberFile = cfg["serialNumberFile"];

		localDataCachingCount_ = localDataCachingCount_ <= 0 ? -1 : localDataCachingCount_;

		logFileSize_ = logFileSize_ <= 0 ? 50 : logFileSize_;
		logBaseSize_ = logBaseSize_ <= 0 ? 1024 * 10 : logBaseSize_;
		logFileCount_ = logFileCount_ <= 0 ? 5 : logFileCount_;
		logLevel_ = logLevel_ <= 0 ? 0 : logLevel_;

		if (cfg.find("isMultiGatewayEnv") != cfg.end())
			isMultiGatewayEnv = cfg.at("isMultiGatewayEnv") == "true" ? true : false;
		if (cfg.find("ManagerPort") != cfg.end())
			managerPort = cfg.at("ManagerPort");
		if (cfg.find("LaunchPath") != cfg.end())
			launchPath = cfg.at("LaunchPath");
		if (cfg.find("DriverXId") != cfg.end())
			driverXId = cfg.at("DriverXId");

		//std::string gatewayLogPath = "logs/" + getGatewayId() + ".log";
		//std::string cacheDBPath = "caching";
		//std::string logDBPath = "log";
		//if (isMultiGatewayEnv)
		//{
		//	gatewayLogPath = "../logs/gateway/" + driverXId + "/" + getGatewayId() + ".log";
		//	cacheDBPath = launchPath + "/../config/gateway/" + driverXId + "/caching";
		//	logDBPath = launchPath + "/../config/gateway/" + driverXId + "/log";
		//}
		std::string gatewayLogPath = CPathManager::getInstance().getGatewayIDLogPath(getGatewayId());
		// 20K
		auto max_size = 1024 * 20 * 1;
		auto max_files = 1;
		logger_ = spdlog::rotating_logger_mt(getGatewayId(), gatewayLogPath, max_size, max_files);
		//logger_->set_level((spdlog::level::level_enum)logLevel_);
		logger_->info("CGatewayAIOT::initial;\n");
		
		//std::ifstream ifs(GATEWAY_FLAG, std::ios::binary);
		//if (ifs.is_open())
		//{
		//	int flag = 0;
		//	ifs.read(reinterpret_cast<char*>(&flag), sizeof flag);
		//	blank_ = flag == 0;
		//}
		loadGatewayConfig();
		loadManageConfig();
		
		httpClient_ = static_cast<COMM::ICommHttp*>(COMM::CFactory::getInstance().produce("CommHttp"));
		httpClient_->configure(mainPlatform_.resourceServer);
		mainPlatform_.mqttClient = new CMqttClient(*this, true);
		for (int i = 0; i < dataDistributionVec_.size(); i++)
		{
			dataDistributionVec_[i].mqttClient = new CMqttClient(*this);
		}
		if (gmrc_.isReport)
			gmrc_.managePlatform.mqttClient = new CMqttClient(*this, false, true);

		if (enableModbusServer_)
		{
			modbusTcpClient_ = static_cast<COMM::ICommModbus*>(COMM::CFactory::getInstance().produce("CommModbus"));
		}
		
		systemSetting_ = create();
		systemSetting_->init(systemSettingFloderPath_);

		//get
		httpServer_.Get("/linkLive", [this](const httplib::Request& req, httplib::Response& res) {httplinkLive(req, res);});
		httpServer_.Get("/deviceInfo", [this](const httplib::Request& req, httplib::Response& res) {httpdeviceInfo(req, res); });
		httpServer_.Get("/pointInfo", [this](const httplib::Request& req, httplib::Response& res) {httppointInfo(req, res); });
		httpServer_.Get("/dllinfo", [this](const httplib::Request& req, httplib::Response& res) {httpdllinfo(req, res); });
		httpServer_.Get("/removedll", [this](const httplib::Request& req, httplib::Response& res) {httpremovedll(req, res); });
		httpServer_.Get("/clientinfo", [this](const httplib::Request& req, httplib::Response& res) {httpgetClientinfo(req, res); });
		httpServer_.Get("/reload", [this](const httplib::Request& req, httplib::Response& res) {httpreload(req, res); });
		httpServer_.Get("/pcapinfo", [this](const httplib::Request& req, httplib::Response& res) {httppcapinfo(req, res); });
		httpServer_.Get("/getModbusMappingInfo", [this](const httplib::Request& req, httplib::Response& res) {httpgetModbusMappingInfo(req, res); });
		httpServer_.Get("/getUserInfo", [this](const httplib::Request& req, httplib::Response& res) {httpgetUserInfo(req, res); });
		httpServer_.Get("/getBasicInfo", [this](const httplib::Request& req, httplib::Response& res) {httpgetBasicInfo(req, res); });
		httpServer_.Get("/getCloudConfigInfo", [this](const httplib::Request& req, httplib::Response& res) {httpgetCloudConfigInfo(req, res); });
		httpServer_.Get("/getRuntimeInfo", [this](const httplib::Request& req, httplib::Response& res) {httpgetRuntimeInfo(req, res); });
		httpServer_.Get("/getCloudFirmwareInfo", [this](const httplib::Request& req, httplib::Response& res) {httpgetCloudFirmwareInfo(req, res); });
		httpServer_.Get("/getInfoSubmission", [this](const httplib::Request& req, httplib::Response& res) {httpgetInfoSubmission(req, res); });
		httpServer_.Get("/getOtherConfigInfo", [this](const httplib::Request& req, httplib::Response& res) {httpgetOtherConfigInfo(req, res); });
		httpServer_.Get("/linkLogList", [this](const httplib::Request& req, httplib::Response& res) {httplinkLogList(req, res); });
		httpServer_.Get("/linkLog", [this](const httplib::Request& req, httplib::Response& res) {httplinkLog(req, res); });
		httpServer_.Get("/linkInfo", [this](const httplib::Request& req, httplib::Response& res) {httplinkInfo(req, res); });
		httpServer_.Get("/linkEnable", [this](const httplib::Request& req, httplib::Response& res) {httplinkEnable(req, res); });
		httpServer_.Get("/restart", [this](const httplib::Request& req, httplib::Response& res) {httprestart(req, res); });
		httpServer_.Get("/getNetWorkConfigList", [this](const httplib::Request& req, httplib::Response& res) {httpgetNetWorkConfigList(req, res); });
		httpServer_.Get("/getWirelessList", [this](const httplib::Request& req, httplib::Response& res) {httpgetWirelessList(req, res); });
		httpServer_.Get("/setWirelessDisconnect", [this](const httplib::Request& req, httplib::Response& res) {httpsetWirelessDisconnect(req, res); });
		httpServer_.Get("/update", [this](const httplib::Request& req, httplib::Response& res) {httpUpdate(req, res); });
		httpServer_.Get("/getTimeInfo", [this](const httplib::Request& req, httplib::Response& res) {httpgetTimeInfo(req, res); });
		httpServer_.Get("/getVersionInfo", [this](const httplib::Request& req, httplib::Response& res) {httpgetVersionInfo(req, res); });
		httpServer_.Get("/getLinkEnableInfo", [this](const httplib::Request& req, httplib::Response& res) {httpgetLinkEnableInfo(req, res); });
		httpServer_.Get("/getCloudNameInfo", [this](const httplib::Request& req, httplib::Response& res) {httpgetCloudNameInfo(req, res); });
		httpServer_.Get("/getCloudStatus", [this](const httplib::Request& req, httplib::Response& res) {httpgetCloudStatus(req, res); });

		//post
		httpServer_.Post("/setPointValue", [this](const httplib::Request& req, httplib::Response& res) {httpsetPointValue(req, res); });
		httpServer_.Post("/clientInfo", [this](const httplib::Request& req, httplib::Response& res) {httpsetClientInfo(req, res); });
		httpServer_.Post("/setModbusMappingInfo", [this](const httplib::Request& req, httplib::Response& res) {httpsetModbusMappingInfo(req, res); });
		httpServer_.Post("/SignUp", [this](const httplib::Request& req, httplib::Response& res) {httpSignUp(req, res); });
		httpServer_.Post("/SignIn", [this](const httplib::Request& req, httplib::Response& res) {httpSignIn(req, res); });
		httpServer_.Post("/setInfoSubmission", [this](const httplib::Request& req, httplib::Response& res) {httpsetInfoSubmission(req, res); });
		httpServer_.Post("/setCloudConfigInfo", [this](const httplib::Request& req, httplib::Response& res) {httpsetCloudConfigInfo(req, res); });
		httpServer_.Post("/setPassWord", [this](const httplib::Request& req, httplib::Response& res) {httpsetPassWord(req, res); });
		httpServer_.Post("/setOtherConfigInfo", [this](const httplib::Request& req, httplib::Response& res) {httpsetOtherConfigInfo(req, res); });
		httpServer_.Post("/logQuery", [this](const httplib::Request& req, httplib::Response& res) {httplogQuery(req, res); });
		httpServer_.Post("/setNetWorkConfig", [this](const httplib::Request& req, httplib::Response& res) {httpsetNetWorkConfig(req, res); });
		httpServer_.Post("/setWirelessConnect", [this](const httplib::Request& req, httplib::Response& res) {httpsetWirelessConnect(req, res); });
		httpServer_.Post("/setWirelessConfig", [this](const httplib::Request& req, httplib::Response& res) {httpsetWirelessConfig(req, res); });
		httpServer_.Post("/linkLogPackage", [this](const httplib::Request& req, httplib::Response& res) {httplinkLogPackage(req, res); });
		httpServer_.Post("/setSyncTime", [this](const httplib::Request& req, httplib::Response& res) {httpsetSyncTime(req, res); });
		httpServer_.Post("/doSyncTime", [this](const httplib::Request& req, httplib::Response& res) {httpdoSyncTime(req, res); });
		httpServer_.Post("/getEnv", [this](const httplib::Request& req, httplib::Response& res) {httpgetEnv(req, res); });
		httpServer_.Post("/getCollectType", [this](const httplib::Request& req, httplib::Response& res) {httpgetCollectType(req, res); });

		//put
		httpServer_.Put("/upload", [this](const httplib::Request& req, httplib::Response& res) {httpupload(req, res); });
		httpServer_.Put("/uploadDLL", [this](const httplib::Request& req, httplib::Response& res) {httpuploadDLL(req, res); });
		httpServer_.Put("/uploadCrt", [this](const httplib::Request& req, httplib::Response& res) {httpuploadCrt(req, res); });

		// configuration
		httpServer_.Post("/setDeviceTemplate", [this](const httplib::Request& req, httplib::Response& res) {httpConfigSetDeviceTemplate(req, res); });
		httpServer_.Post("/getDeviceTemplate", [this](const httplib::Request& req, httplib::Response& res) {httpConfigGetDeviceTemplate(req, res); });
		httpServer_.Post("/deleteDeviceTemplate", [this](const httplib::Request& req, httplib::Response& res) {httpConfigDelDeviceTemplate(req, res); });
		httpServer_.Post("/getConfiguration", [this](const httplib::Request& req, httplib::Response& res) {httpConfigGetConfiguration(req, res); });
		httpServer_.Post("/getConfigPoints", [this](const httplib::Request& req, httplib::Response& res) {httpConfigGetCfgPoints(req, res); });
		httpServer_.Post("/setConfigurationTemplate", [this](const httplib::Request& req, httplib::Response& res) {httpConfigSetConfigurationTemplate(req, res); });
		httpServer_.Post("/getConfigurationTemplate", [this](const httplib::Request& req, httplib::Response& res) {httpConfigGetConfigurationTemplate(req, res); });
		httpServer_.Post("/deleteConfigurationTemplate", [this](const httplib::Request& req, httplib::Response& res) {httpConfigDelConfigurationTemplate(req, res); });
		httpServer_.Post("/getConfigLinkTemplate", [this](const httplib::Request& req, httplib::Response& res) {httpConfigGetLinkTemplate(req, res); });
		httpServer_.Post("/getConfigurationSource", [this](const httplib::Request& req, httplib::Response& res) {httpConfigGetConfigurationSource(req, res); });
		httpServer_.Post("/setConfigurationSource", [this](const httplib::Request& req, httplib::Response& res) {httpConfigSetConfigurationSource(req, res); });
		httpServer_.Post("/setLocalConfiguration", [this](const httplib::Request& req, httplib::Response& res) {httpConfigSetLocalConfiguration(req, res); });
		httpServer_.Post("/startLocalConfigurationWork", [this](const httplib::Request& req, httplib::Response& res) {httpConfigStartLocalConfigurationWork(req, res); });
		httpServer_.Post("/stopLocalConfigurationWork", [this](const httplib::Request& req, httplib::Response& res) {httpConfigStopLocalConfigurationWork(req, res); });
		httpServer_.Post("/getConfigRunningState", [this](const httplib::Request& req, httplib::Response& res) {httpConfigGetRunningState(req, res); });
		httpServer_.Post("/getConfigOPCServerState", [this](const httplib::Request& req, httplib::Response& res) {httpConfigGetOPCServerState(req, res); });
		httpServer_.Post("/setConfigOPCServerState", [this](const httplib::Request& req, httplib::Response& res) {httpConfigSetOPCServerState(req, res); });
		httpServer_.Post("/setConfigPointValue", [this](const httplib::Request& req, httplib::Response& res) {httpConfigSetPointValue(req, res); });
		httpServer_.Post("/getIEMSConfig", [this](const httplib::Request& req, httplib::Response& res) {httpConfigGetIEMSConfig(req, res); });
		httpServer_.Post("/getIEMSDeviceLogs", [this](const httplib::Request& req, httplib::Response& res) {httpConfigGetIEMSDeviceLogs(req, res); });

		//auto ret = httpServer_.set_mount_point("/", WEB_PATH);
		auto ret = httpServer_.set_mount_point("/", CPathManager::getInstance().getWebPath());
		if (!ret) {
			logger_->info("The specified base directory doesn't exist...\n");
			return false;
		}

		if (enableModbusServer_)
			loadModbusMappingTable(modbusMappingTable_, MODBUS_MAPPING_TABLE_PATH);

		//ret = ldc_.initial("caching", localDataCachingCount_);
		//if (!ret)
		//	std::cout << "err " << std::endl;
		//lc_->initial("log");

		std::string cacheDBPath = CPathManager::getInstance().getCacheDBPath();
		std::string logDBPath = CPathManager::getInstance().getLogDBPath();
		std::string configurationDBPath = CPathManager::getInstance().getConfigurationDBPath();
		ldc_.initial(cacheDBPath, localDataCachingCount_);

		std::map<std::string, int> currentTableNames;
		ldc_.createTable(mainPlatform_.mqttUrl);
		currentTableNames[ldc_.sanitizeTableName(mainPlatform_.mqttUrl)] = 0;
		{
			std::unique_lock<std::mutex> lock(mqttListMutex_);
			for (int i = 0; i < dataDistributionVec_.size(); i++)
			{
				ldc_.createTable(dataDistributionVec_[i].mqttUrl);
				currentTableNames[ldc_.sanitizeTableName(dataDistributionVec_[i].mqttUrl)] = 0;
			}
		}
		std::set<std::string> cacheTables;
		ldc_.getAllTablesNames(cacheTables);
		
		for (const auto& nameIter : cacheTables)
		{
			if (currentTableNames.find(nameIter) == currentTableNames.end())
			{
				ldc_.dropTable(nameIter);
			}
		}

		lc_->initial(logDBPath);
		configurationCaching_.initial(configurationDBPath);

		getLocalEnv();

		loadLocalDriverTemplate();
		OpcUaServerWriteCallback boundCallback = std::bind(&CGatewayAIOT::opcuaServerWriteCallBack, this,
			std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
		opcServer_.setWriteCallback(boundCallback);

		if (env_ == EGatewayEnv::iEMSCOM4)
		{
			std::cout << "set run/err to 0." << std::endl;
			CiEMSSetting::getInstance().controlRunningStateLed(EIEMSRunningStateLed::Run, 0);
			CiEMSSetting::getInstance().controlRunningStateLed(EIEMSRunningStateLed::Err, 0);
		}

		return true;
	}

	bool CGatewayAIOT::uninitial(void)
	{
		logger_->info("CGatewayAIOT::uninitial;\n");
		CGateway::uninitial();

		if (httpClient_)
		{
			COMM::CFactory::getInstance().destory(httpClient_);
			httpClient_ = nullptr;
		}

		if (mainPlatform_.mqttClient)
		{
			mainPlatform_.mqttClient->disconnect();
			delete mainPlatform_.mqttClient;
			mainPlatform_.mqttClient = nullptr;
		}

		for (int i = 0; i < dataDistributionVec_.size(); i++)
		{
			auto& dd = dataDistributionVec_[i];
			if (dd.mqttClient)
			{
				dd.mqttClient->disconnect();
				delete dd.mqttClient;
				dd.mqttClient = nullptr;
			}
		}

		if (gmrc_.isReport && gmrc_.managePlatform.mqttClient)
		{
			gmrc_.managePlatform.mqttClient->disconnect();
			delete gmrc_.managePlatform.mqttClient;
			gmrc_.managePlatform.mqttClient = nullptr;
		}

		if (enableModbusServer_)
		{
			if (modbusTcpClient_)
			{
				COMM::CFactory::getInstance().destory(modbusTcpClient_);
				modbusTcpClient_ = nullptr;
			}
		}

		if (enableLocalDataCaching_)
		{
			ldc_.uninitial();
		}
		lc_->uninitial();
		configurationCaching_.uninitial();
		
		if (systemSetting_)
		{
			destory(systemSetting_);
		}

		return true;
	}

	bool CGatewayAIOT::start()
	{
		httpServerThread_ = new std::thread([&]() {httpServer_.listen("0.0.0.0", httpServerPort_); });

		if (configSource_ == EConfigurationSource::ECloud)
		{
			CGateway::start();
			logger_->info("CGatewayAIOT::start;\n");
			insertLog(eServiceStatus, u8"AIOT网关启动");
			running_ = true;
			mainThread_ = new std::thread(&CGatewayAIOT::mainThread, this);
		}
		else if (configSource_ == EConfigurationSource::ELocal)
		{
			running_ = false;
			loadLocalConfig(sog_, slcs_);


			getLocalConfigRunningState();

			if (localConfigDriverRunningState_)
				startLocalConfigWork();

			if (localConfigOPCUARunningState_)
				startLocalConfigOPCUAWrok();
			
		}
		
		return true;
	}

	bool CGatewayAIOT::stop()
	{
		logger_->info("CGatewayAIOT::stop;\n");

		if (env_ == EGatewayEnv::iEMSCOM4)
		{
			localIEMSLedExitControl_ = true;
			if (localIEMSLedThread_)
			{
				if (localIEMSLedThread_->joinable())
					localIEMSLedThread_->join();
				delete localIEMSLedThread_;
				localIEMSLedThread_ = nullptr;
			}
			CiEMSSetting::getInstance().controlRunningStateLed(EIEMSRunningStateLed::Run, 0);
			CiEMSSetting::getInstance().controlRunningStateLed(EIEMSRunningStateLed::Err, 0);
		}

		running_ = false;

		httpServer_.stop();
		if (httpServerThread_)
		{
			if (httpServerThread_->joinable())
			{
				httpServerThread_->join();
			}
			else
			{
				httpServerThread_->detach();
			}
			delete httpServerThread_;
			httpServerThread_ = nullptr;
		}

		mainCondVar_.notify_all();
		if (mainThread_)
		{
			if (mainThread_->joinable())
				mainThread_->join();
			else
			{
				mainThread_->detach();
			}
			delete mainThread_;
			mainThread_ = nullptr;
		}

		CGateway::stop();
		insertLog(eServiceStatus, u8"AIOT网关停止");
		return true;
	}

	void CGatewayAIOT::onEngineStatus(const std::string& channelName, const DRIVER::SStatus& status)
	{
		if (configSource_ == EConfigurationSource::ELocal)
			return;

		CGateway::onEngineStatus(channelName, status);

		DRIVER::ChannelDataSet dataSet;
		DRIVER::SData data;
		data.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
		data.value = status.connectivity;
		dataSet[channelName]["_status"] = data;
		for (const auto& iter : status.extends)
		{
			data.value = iter.second;
			dataSet[channelName][iter.first] = data;
		}
		SDataSet aiotDataSet;
		aiotDataSet.channelName = channelName;
		aiotDataSet.dataSetSptr = std::make_shared<DRIVER::ChannelDataSet>(dataSet);
		std::unique_lock<std::mutex> lock(dataSetQueueMutex_);
		dataSetQueue_.emplace(std::move(aiotDataSet));
		dataSetQueueCondVar_.notify_one();
	}

	void CGatewayAIOT::onEngineOnline(const std::string& channelName, bool online)
	{
		if (configSource_ == EConfigurationSource::ELocal)
			return;

		CGateway::onEngineOnline(channelName, online);

		DRIVER::ChannelDataSet dataSet;
		DRIVER::SData data;
		data.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
		data.value = online;
		dataSet[channelName]["_online"] = data;

		SDataSet aiotDataSet;
		aiotDataSet.channelName = channelName;
		aiotDataSet.dataSetSptr = std::make_shared<DRIVER::ChannelDataSet>(dataSet);
		std::unique_lock<std::mutex> lock(dataSetQueueMutex_);
		dataSetQueue_.emplace(std::move(aiotDataSet));
		dataSetQueueCondVar_.notify_one();
	}

	void CGatewayAIOT::onEngineData(const std::string& channelName, DRIVER::ChannelDataSetSptr dataSetSptr)
	{
		CGateway::onEngineData(channelName, dataSetSptr);

		//logger_->info("CGatewayAIOT::onDataUpdate;\n");
		SDataSet aiotDataSet;
		aiotDataSet.channelName = channelName;
		aiotDataSet.dataSetSptr = dataSetSptr;
		std::unique_lock<std::mutex> lock(dataSetQueueMutex_);
		dataSetQueue_.emplace(std::move(aiotDataSet));
		dataSetQueueCondVar_.notify_one();
	}

	void CGatewayAIOT::onEngineHistoryData(const std::string& channelName, DRIVER::ChannelHistoryDataSetSptr dataSetSptr)
	{
		if (configSource_ == EConfigurationSource::ELocal)
			return;

		for (auto deviceIter = dataSetSptr->begin(); deviceIter != dataSetSptr->end(); deviceIter++)
		{
			Json::Value history_data;
			int index = 0;
			while (!deviceIter->second.empty())
			{
				Json::Value state;
				Json::Value quality;
				Json::Value updatetime;
				for (auto pointIter = deviceIter->second.begin(); pointIter != deviceIter->second.end(); )
				{
					DRIVER::SData data = pointIter->second.back();
					pointIter->second.pop_back();
					switch (data.value.getType())
					{
					case SValue::eValueInt:
						state[pointIter->first] = data.value.asInt<int64_t>(); break;
					case SValue::eValueUInt:
						state[pointIter->first] = data.value.asInt<uint64_t>(); break;
					case SValue::eValueReal:
						state[pointIter->first] = data.value.asFloat(); break;
					case SValue::eValueString:
						state[pointIter->first] = data.value.asString(); break;
					case SValue::eValueBoolean:
						state[pointIter->first] = data.value.asBool(); break;
					default: break;
					}

					Json::Value qualityArr = Json::arrayValue;
					if (data.quality == -1)
						qualityArr[0] = "scanoff";
					quality[pointIter->first] = qualityArr;
					updatetime[pointIter->first] = data.timestamp;

					if (pointIter->second.empty())
					{
						pointIter = deviceIter->second.erase(pointIter);
					}

					Json::Value arrElem;
					arrElem["timestamp"] = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
					arrElem["state"] = std::move(state);
					arrElem["quality"] = std::move(quality);
					arrElem["updatetime"] = std::move(updatetime);
					history_data[index++] = std::move(arrElem);
				}
			}

			Json::Value pkg;
			pkg["sequence"] = 1;
			pkg["history-data"] = std::move(history_data);
			std::string topic = deviceIter->first + "/history";
			if (mainPlatform_.mqttClient->getMqttConnectionStatus())
			{
				mainPlatform_.mqttClient->publish(topic, pkg.toStyledString());
			}

			{
				std::unique_lock<std::mutex> lock(mqttListMutex_);
				for (const auto& iter : dataDistributionVec_)
				{
					iter.mqttClient->publish(topic, pkg.toStyledString());
				}
			}
		}
	}

	void CGatewayAIOT::onEngineEvent(const std::string& channelName, const std::string& deviceName, SEventSptr eventSptr)
	{
		if (configSource_ == EConfigurationSource::ELocal)
			return;

		logger_->info("CGatewayAIOT::onEvent;\n");
		SAIOTEvent e;

		e.deviceId = deviceName;
		e.uuid = "";
		e.eventSptr = eventSptr;
		std::unique_lock<std::mutex> lock(eventQueueMutex_);
		eventQueue_.emplace(std::make_shared<SAIOTEvent>(e));
		eventCondVar_.notify_one();
	}

	void CGatewayAIOT::onEngineControlFeedback(const std::string& channelName, const SControlFeedback& feedback)
	{
		if (configSource_ == EConfigurationSource::ELocal)
			return;

		Json::Value root;
		root["response"] = feedback.status != SControlFeedback::eError ? 100 : 106;
		root["result"] = feedback.status != SControlFeedback::eError ? "succeed" : feedback.info;
		root["sequence"] = feedback.sequence;
		root["step"] = feedback.step;
		if (mainPlatform_.mqttClient->getBrokerUri() == feedback.sourceID)
		{
			mainPlatform_.mqttClient->publish(feedback.deviceName + "/control/response", root.toStyledString());
		}
		else
		{
			for (const auto& iter : dataDistributionVec_)
			{
				if (iter.mqttClient->getBrokerUri() == feedback.sourceID)
				{
					iter.mqttClient->publish(feedback.deviceName + "/control/response", root.toStyledString());
					break;
				}
			}
		}
		
	}

	void CGatewayAIOT::onEngineUpdateDeviceOffline(const std::string& channelName, bool isOffline)
	{
		if (configSource_ == EConfigurationSource::ELocal)
			return;

		//std::cout << channelName << "   " << isOffline << std::endl;
		auto channelPtr = slcs_.find(channelName);
		if (channelPtr == slcs_.end())
			return;

		//mainPlatform_.mqttClient->publish(topic, payload);
		for (auto dit = channelPtr->second.deviceTable.begin(); dit != channelPtr->second.deviceTable.end(); dit++)
		{
			Json::Value jv;
			uint64_t nowTime = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
			jv["timestamp"] = nowTime;
			//std::cout << dit->first << std::endl;
			for (auto pit = dit->second.pointTable.begin(); pit != dit->second.pointTable.end(); pit++)
			{
				jv["quality"][pit->first] = Json::arrayValue;
				if (isOffline)
				{
					jv["quality"][pit->first].append("deviceoffline");
				}
				jv["updatetime"][pit->first] = nowTime;
				//std::cout << pit->first << std::endl;
			}
			for (auto pit = dit->second.innerPointTable.begin(); pit != dit->second.innerPointTable.end(); pit++)
			{
				jv["quality"][pit->first] = Json::arrayValue;
				if (isOffline)
				{
					jv["quality"][pit->first].append("deviceoffline");
				}
				jv["updatetime"][pit->first] = nowTime;
			}
			mainPlatform_.mqttClient->publish(dit->first + "/data", jv.toStyledString());
			//std::cout << jv.toStyledString() << std::endl;
		}
	}

	void CGatewayAIOT::onEngineUpdateDeviceScanoff(const std::string& channelName, std::string& deviceName, bool scanoff)
	{
		if (configSource_ == EConfigurationSource::ELocal)
			return;

		Json::Value jv;
		uint64_t nowTime = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
		jv["timestamp"] = nowTime;
		jv["state"]["_scanoff"] = scanoff;
		mainPlatform_.mqttClient->publish(deviceName + "/data", jv.toStyledString());
	}

	void CGatewayAIOT::onEngineUpdateDeviceInhibit(const std::string& channelName, std::string& deviceName, bool inhibit)
	{
		if (configSource_ == EConfigurationSource::ELocal)
			return;

		Json::Value jv;
		uint64_t nowTime = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
		jv["timestamp"] = nowTime;
		jv["state"]["_inhibit"] = inhibit;
		mainPlatform_.mqttClient->publish(deviceName + "/data", jv.toStyledString());
	}

	void CGatewayAIOT::onEngineIEMSLedBrightness(const std::string& deviceName, int brightness)
	{
		if (env_ != EGatewayEnv::iEMSCOM4)
			return;

		CiEMSSetting::getInstance().controlCOMStateLed(deviceName, brightness);
	}

	void CGatewayAIOT::onEngineEnable(const std::string& channelName, bool enable)
	{
		if (configSource_ == EConfigurationSource::ELocal)
			return;

		DRIVER::ChannelDataSet dataSet;
		DRIVER::SData data;
		data.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
		data.value = enable;
		dataSet[channelName]["_disable"] = data;

		SDataSet aiotDataSet;
		aiotDataSet.channelName = channelName;
		aiotDataSet.dataSetSptr = std::make_shared<DRIVER::ChannelDataSet>(dataSet);
		std::unique_lock<std::mutex> lock(dataSetQueueMutex_);
		dataSetQueue_.emplace(std::move(aiotDataSet));
		dataSetQueueCondVar_.notify_one();
	}

	SMqttResource CGatewayAIOT::onEngineMqttEnable(DRIVER::SCtrlInfo sci, std::map<std::string, DRIVER::SChannelConfig>::iterator slc) 
	{
		if (configSource_ == EConfigurationSource::ELocal)
			return SMqttResource();

		bool cmd = !sci.value.asBool();
		SMqttResource res;
		res.res = false;
		res.type = "Control";
		if (slc->second.enableStatus == cmd) {
			res.data = "The Channel is already " + cmd ? "enabled" : "disabled";
			return res;
		}

		bool ret = true;
		if (cmd) {
			ret = driverEngine_->addDriver(slc->first, slc->second);
		}
		else {
			driverEngine_->deleteDriver(slc->first);
		}
		
		if (ret) {
			slc->second.enableStatus = cmd;
			res.res = true;
		}
		else {
			res.data = "Channel " + sci.channelName + ". control  failed";
		}

		onEngineEnable(slc->first, !slc->second.enableStatus);
		return res;
	}

	bool CGatewayAIOT::onEngineMqttControl(DRIVER::SCtrlInfo sci) 
	{
		if (configSource_ == EConfigurationSource::ELocal)
			return true;

		std::cout << "onEngineControl" << std::endl;
		auto slc = slcs_.find(sci.channelName);
		Json::Value res;
		std::string topic = sci.channelName + "/control/response";
		bool b;
		SMqttResource ControlRes;

		if (sci.channelName.empty() || slc == slcs_.end()) {
			res["response"] = 106;
			res["result"] = "Link name null";
			res["sequence"] = sci.sequence;

			mainPlatform_.mqttClient->publish(topic, res.toStyledString());
			return false;
		}

		switch (sci.type) {
		case DRIVER::SCtrlInfo::eWrite: {
			std::cout << "DRIVER::SCtrlInfo::eWrite" << std::endl;
			ControlRes = onEngineMqttEnable(sci, slc);
			if (!ControlRes.res) {
				res["response"] = 106;
				res["result"] = ControlRes.data;
			}
			else {
				res["response"] = 100;
				res["result"] = "succed";
			}
		}
			break;
		default:
			res["response"] = 106;
			res["result"] = "Do not support";
			break;
		}

		res["sequence"] = sci.sequence;
		mainPlatform_.mqttClient->publish(topic, res.toStyledString());
		return true;
	}

} //namespace GATEWAY end
