#!/bin/bash

# 参数校验
if [[ $# -ne 2 ]]; then
    echo "Usage: $0 <NTP_IP> <auto_value>"
    echo "Example: $0 ************ false"
    exit 1
fi

ntp_ip="$1"
auto_value="$2"
target_file="/user_part/etc/systemd/timesyncd.conf"  # 根据实际路径调整

# 验证auto_value合法性
if [[ "$auto_value" != "true" && "$auto_value" != "false" ]]; then
    echo "Error: auto_value must be 'true' or 'false'"
    exit 1
fi

# 1. 更新NTP地址（精确修改[Time]段落内的配置）
sed -i "/^NTP=/c NTP=${ntp_ip}" "${target_file}"

# 2. 更新#auto注释行（仅在#supconit区块内操作）
#   - 先删除区块内原有的#auto行
#   - 然后在#supconit start后插入新的注释行
sed -i '/#supconit start/,/#supconit end/{/^#[[:space:]]*auto=/d}' "$target_file"
sed -i '/#supconit start/a #auto='"$auto_value" "$target_file"

echo "Configuration updated:"
echo "NTP=${ntp_ip}"
echo "#auto=${auto_value} (in #supconit block)"