@echo off
SETLOCAL EnableDelayedExpansion

REM Check if current directory is a git repository
git rev-parse --is-inside-work-tree >nul 2>&1
IF NOT %ERRORLEVEL% EQU 0 (
    echo Not inside a Git repository. Please run this script in a Git repository.
    goto :eof
)

for /f %%b in ('git rev-parse --short HEAD') do set branchHash=%%b

for /f "tokens=2 delims==" %%x in ('wmic os get localdatetime /value') do set datetime=%%x
set currentDate=%datetime:~0,4%%datetime:~4,2%%datetime:~6,2%

REM Check if a parameter was provided
IF "%~1"=="" (
    SET version_file_path=version_%branchHash%_%currentDate%
) ELSE (
    REM Check if the parameter ends with a backslash
    IF "%~1:~-1%"=="\" (
        SET version_file_path=%~1version
    ) ELSE (
        SET version_file_path=%~1\version
    )
)


REM Get current user
SET "current_user=%USERNAME%"

REM Get current time
FOR /F "tokens=*" %%i IN ('date /t') DO SET current_date=%%i
FOR /F "tokens=*" %%i IN ('time /t') DO SET current_time=%%i

REM Get current branch name
FOR /F "tokens=*" %%i IN ('git rev-parse --abbrev-ref HEAD') DO SET "branch_name=%%i"

REM Check if current branch name is valid
IF "!branch_name!"=="HEAD" (
    echo Unable to determine the current branch. Make sure you are not in a detached HEAD state.
    goto :eof
)

REM Get last commit details and write to a temporary file
git log -1 --pretty=format:"%%H%%n%%an%%n%%ad%%n%%s" > temp_last_commit.txt

REM Read last commit details from temporary file
SET /A count=0
SET "commit_message="
FOR /F "tokens=*" %%i IN (temp_last_commit.txt) DO (
    SET /A count+=1
    IF !count! EQU 1 SET "commit_hash=%%i"
    IF !count! EQU 2 SET "commit_author=%%i"
    IF !count! EQU 3 SET "commit_date=%%i"
    IF !count! GEQ 4 (
        IF "!commit_message!"=="" (
            SET "commit_message=%%i"
        ) ELSE (
            SET "commit_message=!commit_message! %%i"
        )
    )
)

REM Clean up temporary file
DEL temp_last_commit.txt

REM Write version information to file
(
    echo User: %current_user%
    echo Date: %current_date% %current_time%
    echo Branch: %branch_name%
    echo.
    echo Last Commit Details:
    echo   Hash: %commit_hash%
    echo   Author: %commit_author%
    echo   Date: %commit_date%
    echo   Message: %commit_message%
) > %version_file_path%

echo Version file created successfully.

ENDLOCAL
