#pragma once
#ifndef _GATEWAY_MQTT_H_
#define _GATEWAY_MQTT_H_

#include <string>

#include "Gateway.h"
#include "AGatewayFactory.h"
#include "Gateway.h"
#include "ICommMqtt.h"
#include "ACommFactory.h"

namespace GATEWAY
{

#define  MQTT_PUSHCONFIG	 "pushconfig"
#define  MQTT_PULLCONFIG	 "pullconfig"
#define  MQTT_CONTROL		 "control"
#define  MQTT_ONCONTROL      "oncontrol"
#define  MQTT_DONECONTROL	 "donecontrol"
#define  MQTT_DATA			 "data"

#define	 PROTOCAL_HEAD1		 "command"
#define  PROTOCAL_HEAD2		 "sequence"

	const int N_RETRY_ATTEMPTS = 5;

	class CGatewayIOT : public CGateway, public COMM::ICommMqttCallback
	{
	private:
		COMM::ICommMqtt* mqttClient_;
		std::string clientID_;
		std::string brokerUri_;
		std::atomic_bool connect_;

	public:
		CGatewayIOT();
		virtual ~CGatewayIOT();

		//Gateway
		virtual bool initial(GatewayConfig& cfg) override;
		virtual bool uninitial(void) override;
		virtual bool start() override;
		virtual bool stop() override;

		virtual void onEngineStatus(const std::string& channelName, const DRIVER::SStatus& status);
		virtual void onEngineOnline(const std::string& channelName, bool online);
		virtual void onEngineData(const std::string& channelName, DRIVER::ChannelDataSetSptr dataSetSptr);
		virtual void onEngineHistoryData(const std::string& channelName, DRIVER::ChannelHistoryDataSetSptr dataSetSptr);
		virtual void onEngineEvent(const std::string& channelName, const std::string& deviceName, SEventSptr eventSptr);
		virtual void onEngineControlFeedback(const std::string& channelName, const SControlFeedback& feedback);
		virtual void onEngineUpdateDeviceOffline(const std::string& channelName, bool isOffline) override;
		virtual void onEngineUpdateDeviceScanoff(const std::string& channelName, std::string& deviceName, bool scanoff) override;
		virtual void onEngineUpdateDeviceInhibit(const std::string& channelName, std::string& deviceName, bool inhibit) override;
		virtual void onEngineIEMSLedBrightness(const std::string& deviceName, int brightness) override;

		//ICommMqttCallback
		virtual void connected() override;
		virtual void disconnected() override;
		virtual void connectFailed() override;
		virtual void connectionLost() override;
		virtual void messageArrived(const std::string& topic, std::string&& payload) override;
	};

	REGISTER_GATEWAY(CGatewayIOT, "GatewayIOT")
	
} //namespace GATEWAY end


#endif