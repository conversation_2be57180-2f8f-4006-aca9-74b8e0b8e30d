﻿#include "DriverChannel.h"

#include <chrono>
#include <unordered_map>
#include <iostream>
#include <cmath>

#include "Utils/Utils.hpp"
#include "ADriverFactory.h"
#include "PathManager.h"

namespace DRIVER
{

	CDriverChannel::CDriverChannel(const std::string& channelName, const std::string& driverType) :
		driver_(nullptr),
		running_(false),
		loopThread_(nullptr),
		openFlag_(false),
		updateStatusByDriver_(false),
		status_(false),
		lastStatus_(false),
		isFirstOpen_(true),
		lc_(nullptr),
		onlineSeparate_(false),
		lastRegularlyReportInterval_(time(0)),
		hasUpdataDeviceOffline(false)
	{
		channelInfo_.name = channelName;
		channelInfo_.driverType = driverType;
		lc_ = CLogCaching::get_instance();
	}

	CDriverChannel::~CDriverChannel()
	{
	}
	
	void CDriverChannel::updateInnerPoint()
	{
		if (lastStatus_ == (updateStatusByDriver_ ? status_ : openFlag_))
		{
			return;
		}
		else
		{
			lastStatus_ = (updateStatusByDriver_ ? status_ : openFlag_);
		}

		if (!updateStatusByDriver_)
		{
			status_ = openFlag_; //status_作为链路的状态
			/*SStatus status;
			status.connectivity = status_;
			cb_->onChannelStatus(channelInfo_.name, status);*/
		}

		SData data;
		data.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
		data.value = updateStatusByDriver_ ? status_ : openFlag_;
		if (!openFlag_)
			data.qualityCodeVec.push_back(EQualityCode::deviceoffline);
		ChannelDataSet dataSet;
		dataSet[channelInfo_.name]["_online"] = data;
		if (!onlineSeparate_)
		{
			for (auto& deviceIter : channelInfo_.deviceMap)
			{
				dataSet[deviceIter.first]["_online"] = data;
			}
		}
		cb_->onChannelData(channelInfo_.name, std::make_shared<ChannelDataSet>(std::move(dataSet)));


		for (auto& deviceIter : channelInfo_.deviceMap)
		{
			cb_->onChannelUpdateDeviceScanoff(channelInfo_.name, deviceIter.second->name, deviceIter.second->_scanoff);
			cb_->onChannelUpdateDeviceInhibit(channelInfo_.name, deviceIter.second->name, deviceIter.second->_inhibit);
		}
	}
	
	void CDriverChannel::loop()
	{
		bool needClose = false;
		int ondataCnt = 0;
		while (running_)
		{
			updateInnerPoint();

			if (!openFlag_)
			{
				openFlag_ = driver_->open(protocolNode_);
				needClose = true;
				recordOpen(openFlag_);

				if (!openFlag_)
				{
					driver_->close(protocolNode_);
					needClose = false;

					if (!hasUpdataDeviceOffline)
					{
						cb_->onChannelUpdateDeviceOffline(channelInfo_.name, true);
						hasUpdataDeviceOffline = true;
					}
					
					std::this_thread::sleep_for(std::chrono::seconds(1));
					continue;
				}
				else
				{
					cb_->onChannelUpdateDeviceOffline(channelInfo_.name, false);
					continue;
				}
			}

			hasUpdataDeviceOffline = false;

			auto t1 = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
			DRIVER::EStatusCode rc;
			if(openFlag_)
			{
				{
					ControlTask task;

					{
						std::unique_lock<std::mutex> lock(controlTaskQueueMutex_);
						if (!controlTaskQueue_.empty())
						{
							task = std::move(controlTaskQueue_.front());
							controlTaskQueue_.pop();
						}
					}

					if (task)
					{
						//logger_->trace("channel control start.");
						task();
						//logger_->trace("channel control end.");
					}
						
				}

				rc = driver_->poll(protocolNode_);
				if (rc == eStatusNeedReopen)
				{
					driver_->close(protocolNode_);
					needClose = false;
					openFlag_ = false;
					continue;
				}
				else 
				{
					if (!dataSet_.empty())
					{
						if (channelInfo_.regularlyReportInterval)
						{
							SData onlineData;
							onlineData.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
								std::chrono::system_clock::now().time_since_epoch()).count();
							onlineData.value = updateStatusByDriver_ ? status_ : openFlag_;
							for (auto dsIter = dataSet_.begin(); dsIter != dataSet_.end(); dsIter++)
							{
								dsIter->second["_online"] = onlineData;
							}
						}
						cb_->onChannelData(channelInfo_.name, std::make_shared<ChannelDataSet>(std::move(dataSet_)));
						//logger_->info("channel ondata cnt : {0}, device cnt : {1}", ondataCnt++, dataSet_.size());
					}
					
					if (!historyDataSet_.empty())
					{
						cb_->onChannelHistoryData(channelInfo_.name, std::make_shared<ChannelHistoryDataSet>(std::move(historyDataSet_)));
						//logger_->info("channel ondata cnt : {0}, device cnt : {1}", ondataCnt++, dataSet_.size());
					}
				}
				//update regularly
				if (channelInfo_.regularlyReportInterval &&
					time(0) - lastRegularlyReportInterval_ > channelInfo_.regularlyReportInterval)
				{
					for (auto dataIter = key2PointDataMap_.begin(); dataIter != key2PointDataMap_.end(); dataIter++)
					{
						auto pointsIter = key2PointMap_.find(dataIter->first);
						if (pointsIter == key2PointMap_.end())
							continue;

						SData newData = dataIter->second;
						if (!newData.timestamp)
							continue;

						for (auto& pointIter : pointsIter->second)
						{
							if (!running_)
								continue;
							if (!pointIter->online)
								continue;
							if (pointIter->enableAC)
							{
								if (dataIter->second.value.getType() == SValue::eValueInt)
								{
									newData.value = dataIter->second.value.asInt<int64_t>() * pointIter->coefficient + pointIter->added;
								}
								else if (dataIter->second.value.getType() == SValue::eValueUInt)
								{
									newData.value = dataIter->second.value.asInt<uint64_t>() * pointIter->coefficient + pointIter->added;
								}
								else if (dataIter->second.value.getType() == SValue::eValueReal)
								{
									newData.value = dataIter->second.value.asFloat<double>() * pointIter->coefficient + pointIter->added;
								}
							}
							dataSet_[pointIter->deviceInfo.name][pointIter->name] = newData;
							//update _online
							newData.value = updateStatusByDriver_ ? status_ : openFlag_;
							dataSet_[pointIter->deviceInfo.name]["_online"] = newData;
						}
					}
					if (!dataSet_.empty())
					{
						cb_->onChannelData(channelInfo_.name, std::make_shared<ChannelDataSet>(std::move(dataSet_)));
						logger_->info("regularly ondata device cnt : {0}", dataSet_.size());
						lastRegularlyReportInterval_ = time(0);
					}
				}
			}

			auto t2 = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
			auto duration = t2 - t1;

			while (duration < channelInfo_.pollCycle && running_ && rc != eStatusDoNotSleep)
			{
				std::this_thread::sleep_for(std::chrono::milliseconds(100));
				duration += 100;
			}
		}

		if (needClose)
		{
			driver_->close(protocolNode_);
		}
	}

	bool CDriverChannel::initial(const SChannelConfig& cfg)
	{
		if (!logger_)
		{
			logger_ = spdlog::get(cfg.name);
			if (!logger_)
			{
				auto max_size = cfg.logBaseSize * cfg.logSize;
				auto max_files = 1;
				//std::string tmpfilename = "logs/" + cfg.name + cfg.alias;
				//if (cfg.multiGatewatConfig.find("DriverXId") != cfg.multiGatewatConfig.end() &&
				//	cfg.multiGatewatConfig.find("LaunchPath") != cfg.multiGatewatConfig.end())
				//{
				//	tmpfilename = cfg.multiGatewatConfig.at("LaunchPath") + "/../logs/gateway/" + cfg.multiGatewatConfig.at("DriverXId") + "/" + cfg.name + cfg.alias;
				//}
				std::string tmpfilename = CPathManager::getInstance().getChannelLogPath(cfg.name, cfg.alias);
				std::string baseDir;
#ifdef WIN32
				baseDir = spdlog_UTF8ToGBK(tmpfilename);
#else	// UNIX
				baseDir = tmpfilename;
#endif
				//logger_ = spdlog::rotating_logger_mt(cfg.name, logFileName, max_size, max_files);

				//logger_ = spdlog::daily_block_file_sink_mt(cfg.name, baseDir, cfg.name, max_size, 5);
				logger_ = spdlog::daily_block_level_file_sink_mt(cfg.name, baseDir, cfg.name, max_size, cfg.logCount);
				logger_->set_level((spdlog::level::level_enum)cfg.logLevel);
			}
		}
		
		channelInfo_.alias = cfg.alias;
		channelInfo_.enableUpdateStatusByDriver = cfg.enableUpdateStatusByDriver;
		channelInfo_.enableUpdateDataOnChanged = cfg.enableUpdateDataOnChanged;
		channelInfo_.regularlyReportInterval = cfg.regularlyReportInterval;
		channelInfo_.regularlyReportInterval > 0 ? channelInfo_.regularlyReportInterval : 0;


		if (cfg.pollCycle)
		{
			channelInfo_.pollCycle = cfg.pollCycle;
		}
		else
		{
			channelInfo_.pollCycle = 1000;
		}

		driver_ = CFactory::produce(channelInfo_.driverType);
		if (!driver_)
		{
			logger_->error("produce driver failed. type = {0}", channelInfo_.driverType.c_str());
			return false;
		}

		driver_->setCallback(this);

		OriginProtocol originProtocol;
		originProtocol = cfg.channelProtocol;
		originProtocol["_device"] = cfg.name;
		channelInfo_.protocol = driver_->createProtocol(EProtocolType::eProtocolLink, originProtocol);
		protocolNode_.protocol = channelInfo_.protocol;

		for (auto& device : cfg.deviceTable)
		{
			std::shared_ptr<SDeviceInfo> deviceInfo(new SDeviceInfo(channelInfo_));
			deviceInfo->name = device.first;
			originProtocol.clear();
			originProtocol = device.second.deviceProtocol;
			deviceInfo->protocol = driver_->createProtocol(EProtocolType::eProtocolDevice, originProtocol);

			originProtocol.clear();
			SProtocolNode dpn;
			dpn.protocol = deviceInfo->protocol;
			for (auto& point : device.second.pointTable)
			{
				std::shared_ptr<SPointInfo> pointInfo(new SPointInfo(*deviceInfo));
				pointInfo->name = point.first;
				pointInfo->type = point.second.type;
				pointInfo->pointType = point.second.pointType;
				pointInfo->readOnly = point.second.readOnly;
				pointInfo->minValue = point.second.minValue;
				pointInfo->maxValue = point.second.maxValue;
				pointInfo->coefficient = point.second.coefficient;
				pointInfo->added = point.second.added;
				pointInfo->value = point.second.value;
				if (pointInfo->coefficient != 1.0 || pointInfo->added != 0.0)
				{
					pointInfo->enableAC = true;
				}

				originProtocol = point.second.pointProtocol;
				originProtocol["_device"] = deviceInfo->name;
				originProtocol["_deviceName"] = device.second.alias;
				originProtocol["_point"] = pointInfo->name;
				//originProtocol["_pointType"] = point.second.type;
				originProtocol["_type"] = point.second.type;
				originProtocol["_pointType"] = point.second.pointType;
				pointInfo->protocol = driver_->createProtocol(EProtocolType::eProtocolPoint, originProtocol);
				if (pointInfo->protocol)
				{
					SProtocolNode ppn;
					ppn.protocol = pointInfo->protocol;
					if (deviceInfo->protocol)
					{
						dpn.sub.emplace_back(ppn);
					}
					else
					{
						protocolNode_.sub.emplace_back(ppn);
					}
				}
				else
				{
					continue;
				}

				deviceInfo->pointMap.emplace(point.first, pointInfo);
				const std::string& key = driver_->getPointKey(channelInfo_.protocol, deviceInfo->protocol, pointInfo->protocol);
				if (!key.empty())
				{
					key2PointMap_[key].emplace_back(pointInfo);
				}
			}

			originProtocol.clear();
			for (auto& point : device.second.innerPointTable)
			{
				originProtocol["_device"] = deviceInfo->name;
				originProtocol["_point"] = point.first;
				originProtocol["_type"] = point.second.type;
				originProtocol["_pointType"] = point.second.pointType;

				driver_->createProtocol(EProtocolType::eProtocolInnerPoint, originProtocol);
			}

			if (deviceInfo->protocol)
			{
				protocolNode_.sub.emplace_back(dpn);
			}

			channelInfo_.deviceMap.emplace(deviceInfo->name, deviceInfo);
		}

		return true;
	}

	bool CDriverChannel::uninitial()
	{
		if (driver_)
		{
			for (auto& device : channelInfo_.deviceMap)
			{
				for (auto& point : device.second->pointMap)
				{
					driver_->destoryProtocol(EProtocolType::eProtocolPoint, point.second->protocol);
				}
				driver_->destoryProtocol(EProtocolType::eProtocolDevice, device.second->protocol);
			}
			driver_->destoryProtocol(EProtocolType::eProtocolLink, channelInfo_.protocol);
		}
		
		return true;
	}

	bool CDriverChannel::start()
	{
		//bool ret = false;
		//{
		//	std::unique_lock<std::mutex> lock(driverMutex_);
		//	ret = driver_->open(protocolNode_);
		//}
		//
		//if (!ret)
		//{
		//	LOGFMTE("driver [%s] start failed.", channelInfo_.name.c_str());
		//	return false;
		//}
		driver_->setFlag(EFlag::eFlagNone);
		running_ = true;
		loopThread_ = new std::thread(&CDriverChannel::loop, this);
		return true;
	}

	bool CDriverChannel::stop()
	{
		driver_->setFlag(EFlag::eFlagInterrupt);
		running_ = false;
		//{
		//	std::unique_lock<std::mutex> lock(driverMutex_);
		//	driver_->close(protocolNode_);
		//}
		
		if (loopThread_)
		{
			if (loopThread_->joinable())
				loopThread_->join();
			else
				loopThread_->detach();

			delete loopThread_;
			loopThread_ = nullptr;
		}

		cb_ = nullptr;
		return true;
	}

	std::future<SControlFeedback> CDriverChannel::control(const SControlInfo& controlInfo)
	{
		auto writeFunc = [&](SControlInfo& controlInfo) -> SControlFeedback {
			SControlFeedback cf;
			cf.sourceID = controlInfo.sourceID;
			cf.sequence = controlInfo.sequence;
			cf.deviceName = controlInfo.deviceName;
			cf.pointName = controlInfo.pointName;
			cf.status = SControlFeedback::eError;
			cf.raw = controlInfo.raw;
			if (!openFlag_)
			{
				cf.info = "offline";
				logger_->info("channel got control cmd , feedback : {0}", cf.info);
				if (!cf.sourceID.empty())
					cb_->onChannelControlFeedback(channelInfo_.name, cf);
				return cf;
			}

			auto deviceInfoIter = channelInfo_.deviceMap.find(controlInfo.deviceName);
			if (deviceInfoIter == channelInfo_.deviceMap.end())
			{
				cf.info = "DeviceNotFound";
				logger_->info("channel got control cmd , feedback : {0}", cf.info);
				if (!cf.sourceID.empty())
					cb_->onChannelControlFeedback(channelInfo_.name, cf);
				return cf;
			}

			if (!deviceInfoIter->second->online)
			{
				cf.info = "Device  offline";
				logger_->info("channel got control cmd , feedback : {0}", cf.info);
				if (!cf.sourceID.empty())
					cb_->onChannelControlFeedback(channelInfo_.name, cf);
				return cf;
			}

			if (cf.pointName == POINT_NAME_SCANOFF || cf.pointName == POINT_NAME_INHIBIT)
			{
				cf.status = SControlFeedback::eError;
				if (controlInfo.value.getType() == SValue::eValueBoolean)
				{
					if (cf.pointName == POINT_NAME_SCANOFF)
					{
						channelInfo_.deviceMap[controlInfo.deviceName]->_scanoff = controlInfo.value.toBool();
						cf.status = SControlFeedback::eFinished;
						cb_->onChannelUpdateDeviceScanoff(channelInfo_.name, deviceInfoIter->second->name, deviceInfoIter->second->_scanoff);
					}
					else if (cf.pointName == POINT_NAME_INHIBIT)
					{
						channelInfo_.deviceMap[controlInfo.deviceName]->_inhibit = controlInfo.value.toBool();
						cf.status = SControlFeedback::eFinished;
						cb_->onChannelUpdateDeviceInhibit(channelInfo_.name, deviceInfoIter->second->name, deviceInfoIter->second->_inhibit);
					}
				}
				cb_->onChannelControlFeedback(channelInfo_.name, cf);
			}
			else
			{
				if (deviceInfoIter->second->_inhibit)
				{
					cf.info = "Device  inhibit";
					if (!cf.sourceID.empty())
						cb_->onChannelControlFeedback(channelInfo_.name, cf);
					return cf;
				}


				auto pointInfoIter = deviceInfoIter->second->pointMap.find(controlInfo.pointName);
				if (pointInfoIter == deviceInfoIter->second->pointMap.end())
				{
					cf.info = "point not found";
					logger_->info("channel got control cmd , feedback : {0}", cf.info);
					if (!cf.sourceID.empty())
						cb_->onChannelControlFeedback(channelInfo_.name, cf);
					return cf;
				}

				if (pointInfoIter->second->readOnly || !pointInfoIter->second->online)
				{
					cf.info = "point is readonly";
					logger_->info("channel got control cmd , feedback : {0}", cf.info);
					if (!cf.sourceID.empty())
						cb_->onChannelControlFeedback(channelInfo_.name, cf);
					return cf;
				}

				if (pointInfoIter->second->enableAC && pointInfoIter->second->coefficient != 0)
				{
					if (controlInfo.value.getType() == SValue::eValueInt)
					{
						controlInfo.value = (controlInfo.value.asInt<int64_t>() - pointInfoIter->second->added) / pointInfoIter->second->coefficient;
					}
					else if (controlInfo.value.getType() == SValue::eValueUInt)
					{
						controlInfo.value = (controlInfo.value.asInt<uint64_t>() - pointInfoIter->second->added) / pointInfoIter->second->coefficient;
					}
					else if (controlInfo.value.getType() == SValue::eValueReal)
					{
						controlInfo.value = (controlInfo.value.asFloat<double>() - pointInfoIter->second->added) / pointInfoIter->second->coefficient;
					}
				}

				EStatusCode ctrl_ret = driver_->control(channelInfo_.protocol, deviceInfoIter->second->protocol, pointInfoIter->second->protocol, controlInfo);
				cf.status = (ctrl_ret == EStatusCode::eStatusSuccess || ctrl_ret == EStatusCode::eStatusControlAsync || ctrl_ret == EStatusCode::eStatusDoNotResponse) ? SControlFeedback::eFinished : SControlFeedback::eError;

				if (ctrl_ret != EStatusCode::eStatusControlAsync &&
					ctrl_ret != EStatusCode::eStatusDoNotResponse)
					cb_->onChannelControlFeedback(channelInfo_.name, cf);
			}

			return cf;
		};

		auto writeTask = std::make_shared< std::packaged_task<SControlFeedback()> >(
			std::bind(writeFunc, controlInfo)
			);
		auto ret = writeTask->get_future();

		{
			std::unique_lock<std::mutex> lock(controlTaskQueueMutex_);
			controlTaskQueue_.emplace([writeTask]() -> void {(*writeTask)(); });
		}
		logger_->info("channel got control cmd");
		return ret;
	}

	std::future<SControlFeedback> CDriverChannel::control(const std::vector<SControlInfo>& controlInfos)
	{
		auto writeFunc = [&](std::vector<SControlInfo>& controlInfos) -> SControlFeedback {
			SControlFeedback cf;
			EStatusCode ctrl_ret;
			for (auto controlInfo : controlInfos)
			{
				cf.sourceID = controlInfo.sourceID;
				cf.sequence = controlInfo.sequence;
				cf.deviceName = controlInfo.deviceName;
				cf.pointName = controlInfo.pointName;
				cf.status = SControlFeedback::eError;
				cf.raw = controlInfo.raw;
				if (!openFlag_)
				{
					cf.info = "offline";
					logger_->info("channel got control cmd , feedback : {0}", cf.info);
					//return cf;
					continue;
				}
				auto deviceInfoIter = channelInfo_.deviceMap.find(controlInfo.deviceName);
				if (deviceInfoIter == channelInfo_.deviceMap.end())
				{
					cf.info = "DeviceNotFound";
					logger_->info("channel got control cmd , feedback : {0}", cf.info);
					//return cf;
					continue;
				}
				if (!deviceInfoIter->second->online)
				{
					cf.info = "Device  offline";
					logger_->info("channel got control cmd , feedback : {0}", cf.info);
					//return cf;
					continue;
				}

				if (cf.pointName == POINT_NAME_SCANOFF || cf.pointName == POINT_NAME_INHIBIT)
				{
					cf.status = SControlFeedback::eError;
					if (controlInfo.value.getType() == SValue::eValueBoolean)
					{
						if (cf.pointName == POINT_NAME_SCANOFF)
						{
							channelInfo_.deviceMap[controlInfo.deviceName]->_scanoff = controlInfo.value.toBool();
							cf.status = SControlFeedback::eFinished;
							cb_->onChannelUpdateDeviceScanoff(channelInfo_.name, deviceInfoIter->second->name, deviceInfoIter->second->_scanoff);
						}
						else if (cf.pointName == POINT_NAME_INHIBIT)
						{
							channelInfo_.deviceMap[controlInfo.deviceName]->_inhibit = controlInfo.value.toBool();
							cf.status = SControlFeedback::eFinished;
							cb_->onChannelUpdateDeviceInhibit(channelInfo_.name, deviceInfoIter->second->name, deviceInfoIter->second->_inhibit);
						}
					}
					cb_->onChannelControlFeedback(channelInfo_.name, cf);
				}
				else
				{
					if (deviceInfoIter->second->_inhibit)
					{
						cf.info = "Device  inhibit";
						continue;
					}

					auto pointInfoIter = deviceInfoIter->second->pointMap.find(controlInfo.pointName);
					if (pointInfoIter == deviceInfoIter->second->pointMap.end())
					{
						cf.info = "point not found";
						logger_->info("channel got control cmd , feedback : {0}", cf.info);
						//return cf;
						continue;
					}
					if (pointInfoIter->second->readOnly || !pointInfoIter->second->online)
					{
						cf.info = "point is readonly";
						logger_->info("channel got control cmd , feedback : {0}", cf.info);
						//return cf;
						continue;
					}
					if (pointInfoIter->second->enableAC && pointInfoIter->second->coefficient != 0)
					{
						if (controlInfo.value.getType() == SValue::eValueInt)
						{
							controlInfo.value = (controlInfo.value.asInt<int64_t>() - pointInfoIter->second->added) / pointInfoIter->second->coefficient;
						}
						else if (controlInfo.value.getType() == SValue::eValueUInt)
						{
							controlInfo.value = (controlInfo.value.asInt<uint64_t>() - pointInfoIter->second->added) / pointInfoIter->second->coefficient;
						}
						else if (controlInfo.value.getType() == SValue::eValueReal)
						{
							controlInfo.value = (controlInfo.value.asFloat<double>() - pointInfoIter->second->added) / pointInfoIter->second->coefficient;
						}
					}

					ctrl_ret = driver_->control(channelInfo_.protocol, deviceInfoIter->second->protocol, pointInfoIter->second->protocol, controlInfo);
					cf.status = (ctrl_ret == EStatusCode::eStatusSuccess || ctrl_ret == EStatusCode::eStatusControlAsync || ctrl_ret == EStatusCode::eStatusDoNotResponse) ? SControlFeedback::eFinished : SControlFeedback::eError;
				}
				if (ctrl_ret != EStatusCode::eStatusControlAsync &&
					ctrl_ret != EStatusCode::eStatusDoNotResponse)
					cb_->onChannelControlFeedback(channelInfo_.name, cf);
			}
			return cf;
		};


		auto writeTask = std::make_shared< std::packaged_task<SControlFeedback()> >(
			std::bind(writeFunc, controlInfos)
		);
		auto ret = writeTask->get_future();
		{
			std::unique_lock<std::mutex> lock(controlTaskQueueMutex_);
			controlTaskQueue_.emplace([writeTask]() -> void {(*writeTask)(); });
		}
		logger_->info("channel got control cmd");
		return ret;

		return std::future<SControlFeedback>();
	}

	bool CDriverChannel::controlSet(const std::unordered_map<std::string, std::unordered_map<SControlInfo::EType, std::vector<SControlInfo>>>& controlSets)
	{
		//devicename - pointname result
		std::unordered_map<std::string, std::unordered_map<std::string, EStatusCode>> results;
		std::unordered_map<std::string, std::vector<SControlSetInfo>> controlValues;
		std::unordered_map<std::string, std::unordered_map<std::string, bool>> controlRet;

		for (auto dit = controlSets.begin(); dit != controlSets.end(); dit++)
		{
			std::string deviceName = dit->first;
			for (auto tit = dit->second.begin(); tit != dit->second.end(); tit++)
			{
				for (auto pit = tit->second.begin(); pit != tit->second.end(); pit++)
				{
					std::string pointName = pit->pointName;
					switch (tit->first)
					{
					case SControlInfo::EType::eTLSM:
					case SControlInfo::EType::eKZYZ:
					case SControlInfo::EType::eJDGP:
					case SControlInfo::EType::eWXGP:
						results[deviceName][pointName] = offlineDevice(*pit);
						break;
					case SControlInfo::EType::eOfflinePoint:
						results[deviceName][pointName] = offlinePoint(*pit);
						break;
					case SControlInfo::EType::eOnlinePoint:
						results[deviceName][pointName] = onlinePoint(*pit);
						break;
					case SControlInfo::EType::emanset:
						results[deviceName][pointName] = setArtificialValue(*pit);
						break;
					case SControlInfo::EType::eWrite:
						{
							controlRet[deviceName][pointName] = false;
							auto deviceInfoIter = channelInfo_.deviceMap.find(deviceName);
							if (deviceInfoIter == channelInfo_.deviceMap.end())
							{
								results[deviceName][pointName] = EStatusCode::eStatusFail;
								break;
							}
							if (!deviceInfoIter->second->online)
							{
								results[deviceName][pointName] = EStatusCode::eStatusFail;
								break;
							}
							auto pointInfoIter = deviceInfoIter->second->pointMap.find(pointName);
							if (pointInfoIter == deviceInfoIter->second->pointMap.end())
							{
								results[deviceName][pointName] = EStatusCode::eStatusFail;
								break;
							}
							if (pointInfoIter->second->readOnly || !pointInfoIter->second->online)
							{
								results[deviceName][pointName] = EStatusCode::eStatusFail;
								break;
							}
							SControlInfo controlValue = *pit;
							if (pointInfoIter->second->enableAC && pointInfoIter->second->coefficient != 0)
							{
								if (pit->value.getType() == SValue::eValueInt)
								{
									controlValue.value = (controlValue.value.asInt<int64_t>() - pointInfoIter->second->added) / pointInfoIter->second->coefficient;
								}
								else if (pit->value.getType() == SValue::eValueReal)
								{
									controlValue.value = (controlValue.value.asFloat<double>() - pointInfoIter->second->added) / pointInfoIter->second->coefficient;
								}
							}
							SControlSetInfo csif(controlValue, channelInfo_.protocol, deviceInfoIter->second->protocol, pointInfoIter->second->protocol);
							controlValues[deviceName].emplace_back(csif);
						}
						break;
					default:
						break;
					}
				}
			}
		}

		//std::cout << controlValues.size() << std::endl;

		auto writeFunc = [&](std::unordered_map<std::string, std::vector<SControlSetInfo>>& controlValues)->SControlFeedback {
			SControlFeedback cf;
			cf.status = driver_->controlSet(controlValues) == EStatusCode::eStatusSuccess ?
				SControlFeedback::EStatus::eSuccess :
				SControlFeedback::EStatus::eFailed;
			return cf;
			};

		auto writeTask = std::make_shared< std::packaged_task<SControlFeedback()> >(
			std::bind(writeFunc, controlValues)
		);

		auto ret = writeTask->get_future();

		{
			std::unique_lock<std::mutex> lock(controlTaskQueueMutex_);
			controlTaskQueue_.emplace([writeTask]() -> void {(*writeTask)(); });
		}

		return true;
	}

	EStatusCode CDriverChannel::getBufferedData(ChannelDataSet& dataSet)
	{
		for (const auto& deviceInfoIter : channelInfo_.deviceMap)
		{
			DeviceDataSet dds;
			getBufferedData(deviceInfoIter.first, dds);
			dataSet[deviceInfoIter.first] = std::move(dds);
			getDeviceOnlineStatus(dataSet);
			getChannelOnline(dataSet);
		}


		SData data;
		data.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
		data.value = updateStatusByDriver_ ? status_ : openFlag_;
		dataSet[channelInfo_.name]["_online"] = data;


		return eStatusSuccess;
	}

	EStatusCode CDriverChannel::getBufferedData(const std::string& deviceName, DeviceDataSet& dataSet, bool isWeb)
	{
		auto deviceInfoIter = channelInfo_.deviceMap.find(deviceName);
		if (deviceInfoIter == channelInfo_.deviceMap.end())
			return eStatusDeviceNotFound;

		for (const auto& pointInfoIter : deviceInfoIter->second->pointMap)
		{
			if (pointInfoIter.second->online)
			{
				std::string key = driver_->getPointKey(channelInfo_.protocol, deviceInfoIter->second->protocol, pointInfoIter.second->protocol);
				auto valueIter = key2PointDataMap_.find(key);
				if (valueIter == key2PointDataMap_.end())
					continue;
				if (pointInfoIter.second->pointType == "control" || pointInfoIter.second->pointType == "calculation")
					continue;

				SData newData = calcDataOffset(pointInfoIter.second, valueIter->second);
				if (isWeb)
				{
					newData = valueIter->second;
				}

				dataSet[pointInfoIter.first] = newData;
			}
			else
			{
				dataSet[pointInfoIter.first].value = pointArtificialValueMap_[deviceName + "." + pointInfoIter.first];
				dataSet[pointInfoIter.first].quality = -1;
			}
			
		}
		return eStatusSuccess;
	}

	EStatusCode CDriverChannel::getBufferedData(const std::string& deviceName, const std::string& pointName, SData& data)
	{
		auto deviceInfoIter = channelInfo_.deviceMap.find(deviceName);
		if (deviceInfoIter == channelInfo_.deviceMap.end())
			return eStatusDeviceNotFound;

		auto pointInfoIter = deviceInfoIter->second->pointMap.find(pointName);
		if (pointInfoIter == deviceInfoIter->second->pointMap.end())
			return eStatusPointNotFound;

		if (pointInfoIter->second->online)
		{
			std::string key = driver_->getPointKey(channelInfo_.protocol, deviceInfoIter->second->protocol, pointInfoIter->second->protocol);
			auto valueIter = key2PointDataMap_.find(key);
			if (valueIter == key2PointDataMap_.end())
				return eStatusError;

			SData newData = calcDataOffset(pointInfoIter->second, valueIter->second);
			data = newData;
		}
		else
		{
			data.value = pointArtificialValueMap_[deviceName + "." + pointName];
			data.quality = -1;
		}
		
		return eStatusSuccess;
	}

	EStatusCode CDriverChannel::getDeviceOnlineStatus(ChannelDataSet& dataSet)
	{
		for (const auto& deviceInfoIter : channelInfo_.deviceMap)
		{
			//std::cout << deviceInfoIter.first << std::endl;
			SData data;
			data.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
				std::chrono::system_clock::now().time_since_epoch()).count();
			data.value = updateStatusByDriver_ ? status_ : openFlag_;
			dataSet[deviceInfoIter.first]["_online"] = data;
		}
		return eStatusSuccess;

	}

	EStatusCode CDriverChannel::getChannelOnline(ChannelDataSet& dataSet)
	{
		SData data;
		data.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
			std::chrono::system_clock::now().time_since_epoch()).count();
		data.value = updateStatusByDriver_ ? status_ : openFlag_;
		dataSet[channelInfo_.name]["_online"] = data;
		return EStatusCode::eStatusSuccess;
	}

	EStatusCode CDriverChannel::setArtificialValue(const SControlInfo& controlInfo)
	{
		SControlFeedback cf;
		cf.sourceID = controlInfo.sourceID;
		cf.sequence = controlInfo.sequence;
		cf.deviceName = controlInfo.deviceName;
		cf.pointName = controlInfo.pointName;
		cf.info = "failed.";
		cf.status = SControlFeedback::eError;

		if (!openFlag_)
		{
			cf.info = "link is not open.";
			if (!cf.sourceID.empty())
				cb_->onChannelControlFeedback(channelInfo_.name, cf);
			return EStatusCode::eStatusOffline;
		}

		auto deviceInfoIter = channelInfo_.deviceMap.find(controlInfo.deviceName);
		if (deviceInfoIter == channelInfo_.deviceMap.end())
		{
			cf.info = "device not found.";
			if (!cf.sourceID.empty())
				cb_->onChannelControlFeedback(channelInfo_.name, cf);
			return EStatusCode::eStatusDeviceNotFound;
		}
		auto pointInfoIter = deviceInfoIter->second->pointMap.find(controlInfo.pointName);
		if (pointInfoIter == deviceInfoIter->second->pointMap.end())
		{
			cf.info = "point not found.";
			if (!cf.sourceID.empty())
				cb_->onChannelControlFeedback(channelInfo_.name, cf);
			return EStatusCode::eStatusPointNotFound;
		}
		if (pointInfoIter->second->online)
		{
			cf.info = "point offline.";
			if (!cf.sourceID.empty())
				cb_->onChannelControlFeedback(channelInfo_.name, cf);
			return EStatusCode::eStatusFail;
		}

		pointArtificialValueMap_[controlInfo.deviceName + "." + controlInfo.pointName] = controlInfo.value;
		if (cb_ && running_)
		{
			SData newSData;
			if (!newSData.timestamp)
			{
				newSData.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
			}
			newSData.value = controlInfo.value;
			newSData.qualityCodeVec.push_back(DRIVER::EQualityCode::scanoff);
			ChannelDataSet dataSet;
			dataSet[deviceInfoIter->second->name][pointInfoIter->second->name] = newSData;
			cb_->onChannelData(channelInfo_.name, std::make_shared<ChannelDataSet>(dataSet));

			cf.status = SControlFeedback::eSuccess;
			cf.info = "success.";
		}
		if (!cf.sourceID.empty())
			cb_->onChannelControlFeedback(channelInfo_.name, cf);
		return EStatusCode::eStatusSuccess;
	}

	EStatusCode CDriverChannel::setDeviceStatus(const std::string& deviceName)
	{
		if (!openFlag_)
		{
			return eStatusOffline;
		}

		auto deviceInfoIter = channelInfo_.deviceMap.find(deviceName);
		if (deviceInfoIter == channelInfo_.deviceMap.end())
			return eStatusDeviceNotFound;

		return eStatusSuccess;
	}

	EStatusCode CDriverChannel::setPointReadOnly(const std::string& deviceName, const std::string& pointName)
	{
		if (!openFlag_)
		{
			return eStatusOffline;
		}

		auto deviceInfoIter = channelInfo_.deviceMap.find(deviceName);
		if (deviceInfoIter == channelInfo_.deviceMap.end())
			return eStatusDeviceNotFound;

		auto pointInfoIter = deviceInfoIter->second->pointMap.find(pointName);
		if (pointInfoIter == deviceInfoIter->second->pointMap.end())
			return eStatusPointNotFound;

		pointInfoIter->second->readOnly = true;
		return eStatusSuccess;
	}

	EStatusCode CDriverChannel::setPointWritable(const std::string& deviceName, const std::string& pointName)
	{
		if (!openFlag_)
		{
			return eStatusOffline;
		}

		auto deviceInfoIter = channelInfo_.deviceMap.find(deviceName);
		if (deviceInfoIter == channelInfo_.deviceMap.end())
			return eStatusDeviceNotFound;

		auto pointInfoIter = deviceInfoIter->second->pointMap.find(pointName);
		if (pointInfoIter == deviceInfoIter->second->pointMap.end())
			return eStatusPointNotFound;

		pointInfoIter->second->readOnly = false;
		return eStatusSuccess;
	}

	EStatusCode CDriverChannel::offlinePoint(const SControlInfo& controlInfo)
	{
		SControlFeedback cf;
		cf.sourceID = controlInfo.sourceID;
		cf.sequence = controlInfo.sequence;
		cf.deviceName = controlInfo.deviceName;
		cf.pointName = controlInfo.pointName;
		cf.status = SControlFeedback::eError;

		if (!openFlag_)
		{
			cf.info = "link is not open.";
			if (!cf.sourceID.empty())
				cb_->onChannelControlFeedback(channelInfo_.name, cf);
			return EStatusCode::eStatusOffline;
		}

		auto deviceInfoIter = channelInfo_.deviceMap.find(controlInfo.deviceName);
		if (deviceInfoIter == channelInfo_.deviceMap.end())
		{
			cf.info = "device not found.";
			if (!cf.sourceID.empty())
				cb_->onChannelControlFeedback(channelInfo_.name, cf);
			return EStatusCode::eStatusDeviceNotFound;
		}

		auto pointInfoIter = deviceInfoIter->second->pointMap.find(controlInfo.pointName);
		if (pointInfoIter == deviceInfoIter->second->pointMap.end())
		{
			cf.info = "point not found.";
			if (!cf.sourceID.empty())
				cb_->onChannelControlFeedback(channelInfo_.name, cf);
			return EStatusCode::eStatusPointNotFound;
		}

		pointInfoIter->second->online = false;
		std::string key = driver_->getPointKey(channelInfo_.protocol, deviceInfoIter->second->protocol, pointInfoIter->second->protocol);
		auto valueIter = key2PointDataMap_.find(key);
		if (valueIter != key2PointDataMap_.end())
		{
			pointArtificialValueMap_[controlInfo.deviceName + "." + controlInfo.pointName] = valueIter->second.value;
		}

		SData data;
		data.qualityCodeVec.push_back(EQualityCode::scanoff);
		data.quality = -1;
		ChannelDataSet dataSet;
		dataSet[deviceInfoIter->first][pointInfoIter->first] = data;
		cb_->onChannelData(channelInfo_.name, std::make_shared<ChannelDataSet>(dataSet));

		cf.status = SControlFeedback::eSuccess;
		cf.info = "success.";
		if (!cf.sourceID.empty())
			cb_->onChannelControlFeedback(channelInfo_.name, cf);
		return eStatusSuccess;
	}

	EStatusCode CDriverChannel::onlinePoint(const SControlInfo& controlInfo)
	{
		SControlFeedback cf;
		cf.sourceID = controlInfo.sourceID;
		cf.sequence = controlInfo.sequence;
		cf.deviceName = controlInfo.deviceName;
		cf.pointName = controlInfo.pointName;
		cf.status = SControlFeedback::eError;

		if (!openFlag_)
		{
			cf.info = "link is not open.";
			if (!cf.sourceID.empty())
				cb_->onChannelControlFeedback(channelInfo_.name, cf);
			return EStatusCode::eStatusOffline;
		}

		auto deviceInfoIter = channelInfo_.deviceMap.find(controlInfo.deviceName);
		if (deviceInfoIter == channelInfo_.deviceMap.end())
		{
			cf.info = "device not found.";
			if (!cf.sourceID.empty())
				cb_->onChannelControlFeedback(channelInfo_.name, cf);
			return EStatusCode::eStatusDeviceNotFound;
		}
		auto pointInfoIter = deviceInfoIter->second->pointMap.find(controlInfo.pointName);
		if (pointInfoIter == deviceInfoIter->second->pointMap.end())
		{
			cf.info = "point not found.";
			if (!cf.sourceID.empty())
				cb_->onChannelControlFeedback(channelInfo_.name, cf);
			return EStatusCode::eStatusPointNotFound;
		}

		pointInfoIter->second->online = true;
		auto iter = pointArtificialValueMap_.find(controlInfo.deviceName + "." + controlInfo.pointName);
		if (iter != pointArtificialValueMap_.end())
		{
			pointArtificialValueMap_.erase(iter);
		}
		SData data;
		data.qualityCodeVec.push_back(EQualityCode::none);
		getBufferedData(controlInfo.deviceName, controlInfo.pointName, data);
		ChannelDataSet dataSet;
		dataSet[deviceInfoIter->first][pointInfoIter->first] = data;
		cb_->onChannelData(channelInfo_.name, std::make_shared<ChannelDataSet>(dataSet));

		cf.status = SControlFeedback::eSuccess;
		cf.info = "success.";
		if (!cf.sourceID.empty())
			cb_->onChannelControlFeedback(channelInfo_.name, cf);

		return EStatusCode::eStatusSuccess;
	}

	EStatusCode CDriverChannel::offlineDevice(const SControlInfo& controlInfo)
	{
		SControlFeedback cf;
		cf.sourceID = controlInfo.sourceID;
		cf.sequence = controlInfo.sequence;
		cf.deviceName = controlInfo.deviceName;
		cf.pointName = controlInfo.pointName;
		cf.status = SControlFeedback::eError;

		if (!openFlag_)
		{
			cf.info = "link is not open.";
			if (!cf.sourceID.empty())
				cb_->onChannelControlFeedback(channelInfo_.name, cf);
			return EStatusCode::eStatusOffline;
		}

		auto deviceInfoIter = channelInfo_.deviceMap.find(controlInfo.deviceName);
		if (deviceInfoIter == channelInfo_.deviceMap.end())
		{
			cf.info = "device not found.";
			if (!cf.sourceID.empty())
				cb_->onChannelControlFeedback(channelInfo_.name, cf);
			return EStatusCode::eStatusDeviceNotFound;
		}

		deviceInfoIter->second->online = false;

		cf.status = SControlFeedback::eSuccess;
		cf.info = "success.";
		if (!cf.sourceID.empty())
			cb_->onChannelControlFeedback(channelInfo_.name, cf);
		return EStatusCode::eStatusSuccess;
	}

	EStatusCode CDriverChannel::onlineDevice(const SControlInfo& controlInfo)
	{
		SControlFeedback cf;
		cf.sourceID = controlInfo.sourceID;
		cf.sequence = controlInfo.sequence;
		cf.deviceName = controlInfo.deviceName;
		cf.pointName = controlInfo.pointName;
		cf.status = SControlFeedback::eError;

		if (!openFlag_)
		{
			cf.info = "link is not open.";
			if (!cf.sourceID.empty())
				cb_->onChannelControlFeedback(channelInfo_.name, cf);
			return EStatusCode::eStatusOffline;
		}

		auto deviceInfoIter = channelInfo_.deviceMap.find(controlInfo.deviceName);
		if (deviceInfoIter == channelInfo_.deviceMap.end())
		{
			cf.info = "device not found.";
			if (!cf.sourceID.empty())
				cb_->onChannelControlFeedback(channelInfo_.name, cf);
			return EStatusCode::eStatusDeviceNotFound;
		}
		deviceInfoIter->second->online = true;

		cf.status = SControlFeedback::eSuccess;
		cf.info = "success.";
		if (!cf.sourceID.empty())
			cb_->onChannelControlFeedback(channelInfo_.name, cf);

		return EStatusCode::eStatusSuccess;
	}

	void CDriverChannel::setCallback(IChannelCallback* cb)
	{
		cb_ = cb;
	}

	bool CDriverChannel::hasDevice(const std::string& deviceName)
	{
		auto deviceCfgIter = channelInfo_.deviceMap.find(deviceName);
		if (deviceCfgIter != channelInfo_.deviceMap.end())
			return true;
		return false;
	}

	bool CDriverChannel::getStatus()
	{
		return status_;
	}

	bool CDriverChannel::insertLog(ELogType type, const std::string& content)
	{
		SLog log;
		log.type = type;
		log.createTime = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
		log.content = content;
		SLogMessage* lm = new SLogMessage();
		lm->type = eLogInsert;
		lm->lim.vLog.emplace_back(std::move(log));
		lc_->pushLog(lm);
		return true;
	}

	void CDriverChannel::recordOpen(bool openFlag)
	{
		std::string content = u8"链路:" + channelInfo_.alias + u8" 打开" + (openFlag? u8"成功":u8"失败");
		insertLog(eLinkStatus, content);
		if (isFirstOpen_) //只记录重连
		{
			isFirstOpen_ = false;
			return;
		}
		
		//删除超过一小时的记录
		auto now = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
		for (auto it = openRecordMap_.begin(); it != openRecordMap_.end(); )
		{
			if (now - it->first > 3600 * 1000)
				openRecordMap_.erase(it++);
			else
				it++;
		}

		logger_->info("recordOpen {0}", openFlag);
		openRecordMap_.emplace(now, openFlag);
	}

	int CDriverChannel::getConnectQuality()
	{
		//删除超过一小时的记录
		auto now = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
		for (auto it = openRecordMap_.begin(); it != openRecordMap_.end(); )
		{
			if (now - it->first > 3600 * 1000)
				openRecordMap_.erase(it++);
			else
				it++;
		}
		int connectTimes = openRecordMap_.size();

		//根据重连次数计算连接质量0-100分，每重连3次-20分，0次100分，1-3次80分，4-6次60分
		int connectQuality = 100 - ceil(1.0 * connectTimes / 3) * 20;
		if (connectQuality < 0) connectQuality = 0;
		return connectQuality;
	}

	EStatusCode CDriverChannel::sendAllData()
	{
		//std::cout << __func__ << std::endl;

		for (const auto& deviceInfoIter : channelInfo_.deviceMap)
		{
			ChannelDataSet channelDataSet;
			DeviceDataSet dds;
			getBufferedData(deviceInfoIter.first, dds);
			SData data;
			data.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
			data.value = updateStatusByDriver_ ? status_ : openFlag_;
			dds["_online"] = data;
			channelDataSet[deviceInfoIter.first] = std::move(dds);
			cb_->onChannelData(deviceInfoIter.first, std::make_shared<ChannelDataSet>(std::move(channelDataSet)));
			cb_->onChannelUpdateDeviceScanoff(channelInfo_.name, deviceInfoIter.second->name, deviceInfoIter.second->_scanoff);
			cb_->onChannelUpdateDeviceInhibit(channelInfo_.name, deviceInfoIter.second->name, deviceInfoIter.second->_inhibit);
		}
		return EStatusCode::eStatusSuccess;
	}

	void CDriverChannel::onLog(ELogLevel logLevel, const std::string& log)
	{
		switch (logLevel)
		{
		case ELogLevel::eLogTrace:
			logger_->trace(log.c_str());
			break;
		case ELogLevel::eLogDebug:
			logger_->debug(log.c_str());
			break;
		case ELogLevel::eLogInfo:
			logger_->info(log.c_str());
			break;
		case ELogLevel::eLogWarn:
			logger_->warn(log.c_str());
			break;
		case ELogLevel::eLogError:
			logger_->error(log.c_str());
			break;
		 case ELogLevel::eLogPacket:
		 	logger_->packet(log.c_str());
		 	break;
		default:
			break;
		}
	}

	void CDriverChannel::onLog(ELogLevel logLevel, const std::vector<uint8_t>& data)
	{
		onLog(logLevel, UTILS::bytesToHexString(data.data(), data.size()));
	}

	void CDriverChannel::onLog(const std::string& func, const std::string& type, const std::string& addr, const std::string& log)
	{
		onLog(ELogLevel::eLogPacket, "[" + func + "] [" + type + "] [" + addr + "] [" + log + "]");
	}

	void CDriverChannel::onStatus(const SStatus& status)
	{
		updateStatusByDriver_ = true;
		status_ = status.connectivity;
		cb_->onChannelStatus(channelInfo_.name, status);
		/*if (!status)
		{
			for (auto& deviceIter : channelInfo_.deviceMap)
			{
				deviceIter.second->online = false;
			}
		}*/
	}

	void CDriverChannel::onOnline(bool online)
	{
		updateStatusByDriver_ = true;
		status_ = online;
		cb_->onChannelOnline(channelInfo_.name, online);
	}

	void CDriverChannel::onData(const std::string& key, const SData& data)
	{
		auto pointsIter = key2PointMap_.find(key);
		if (pointsIter == key2PointMap_.end()) return;
		
		//对于没有时间的data，第一次取当前时间，后面如果值和质量码不变，则保持原来的时间，否则取当前时间
		SData newData = data;
		auto dataIter = key2PointDataMap_.find(key);
		if (dataIter != key2PointDataMap_.end())
		{
			if (!newData.timestamp)
			{
				if (dataIter->second.value == newData.value && dataIter->second.quality == newData.quality)
				{
					newData.timestamp = dataIter->second.timestamp;
				}
				else
				{
					newData.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
				}
			}
			if (channelInfo_.enableUpdateDataOnChanged)
			{
				// check 
				if (dataIter->second == newData)
					return;
			}
			// update
			dataIter->second = newData;
		}
		else
		{
			if (!newData.timestamp)
			{
				newData.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
			}
			// update
			key2PointDataMap_[key] = newData;
		}

		logger_->info("driver ondata key : {0}", key);

		for (auto& pointIter : pointsIter->second)
		{
			if (!running_)
			{
				return;
			}

			if (!pointIter->online)
			{
				continue;
			}

			if (pointIter->deviceInfo._scanoff)
			{
				continue;
			}
			
			if (pointIter->pointType == "control" || pointIter->pointType == "calculation")
			{
				//std::cout << pointIter->pointType << std::endl;
				continue;
			}

			if (!pointIter->type.empty())
			{
				switch (pointIter->type[0])
				{
				case 'A':
				{
					if (data.value.getType() == SValue::eValueBoolean)
					{
						break;
					}
					else if (data.value.getType() == SValue::eValueInt)
					{
						newData.value = data.value.asInt<int64_t>();
					}
					else if (data.value.getType() == SValue::eValueUInt)
					{
						newData.value = data.value.asInt<uint64_t>();
					}
					else if (data.value.getType() == SValue::eValueString)
					{
						std::string str = data.value.asString();
						double f64 = data.value.toFloat<double>();
						newData.qualityCodeVec.push_back(EQualityCode::typeerror);
						newData.value = f64;
					}
					else
					{
						newData.value = data.value.toFloat<double>();
					}

					if (newData.value.getType() == SValue::EValueType::eValueReal &&
						!newData.value.isRealValid())
					{
						newData.qualityCodeVec.push_back(EQualityCode::bad);
					}
					else
					{
						if (pointIter->value.find("maximum") != pointIter->value.end())
						{
							if (newData.value.toFloat() > pointIter->maxValue)
							{
								newData.qualityCodeVec.push_back(EQualityCode::outofrange);
							}
						}
						if (pointIter->value.find("minimum") != pointIter->value.end())
						{
							if (newData.value.toFloat() < pointIter->minValue)
							{
								newData.qualityCodeVec.push_back(EQualityCode::outofrange);
							}
						}
						//if (pointIter->minValue != pointIter->maxValue)
						//{
						//	if (newData.value.toFloat() < pointIter->minValue || newData.value.toFloat() > pointIter->maxValue)
						//	{newData.qualityCodeVec.push_back(EQualityCode::outofrange);
						//	}
						//}
					}
				}
					break;
				case 'E':
					if (data.value.getType() == SValue::eValueInt)
					{
						newData.value = data.value.asInt<int32_t>();
					}
					else if (data.value.getType() == SValue::eValueUInt)
					{
						newData.value = data.value.asInt<uint32_t>();
					}
					else if (data.value.getType() == SValue::eValueString)
					{
						std::string str = data.value.asString();
						int32_t i32 = data.value.toFloat<int32_t>();
						newData.qualityCodeVec.push_back(EQualityCode::typeerror);

						//if ((str == "0" || str == "0.0") && i32 == 0)
						//{
						//	newData.qualityCodeVec.push_back(EQualityCode::typeerror);
						//}
						newData.value = i32;
					}
					else
					{
						newData.value = data.value.toFloat<int32_t>();
					} 
					break;
				case 'D':
					newData.value = data.value.toBool(); 
					break;
				case 'S': 
					newData.value = data.value.toString();
					break;
				default:
					break;
				}
			}

			if (pointIter->enableAC)
			{
				if (data.value.getType() == SValue::eValueInt)
				{
					newData.value = data.value.asInt<int64_t>() * pointIter->coefficient + pointIter->added;
				}
				else if (data.value.getType() == SValue::eValueUInt)
				{
					newData.value = data.value.asInt<uint64_t>() * pointIter->coefficient + pointIter->added;
				}
				else if (data.value.getType() == SValue::eValueReal)
				{
					newData.value = data.value.asFloat<double>() * pointIter->coefficient + pointIter->added;
				}
			}

			dataSet_[pointIter->deviceInfo.name][pointIter->name] = newData;
		}
	}

	void CDriverChannel::onInnerData(const std::string& deviceName, const std::string& pointName, SData& data)
	{
		dataSet_[deviceName][pointName] = data;
	}

	void CDriverChannel::onHistoryData(const std::string& key, const SData& data)
	{
		auto pointsIter = key2PointMap_.find(key);
		if (pointsIter == key2PointMap_.end()) return;

		SData newData = data;
		for (auto& pointIter : pointsIter->second)
		{
			if (!running_)
			{
				return;
			}

			if (!pointIter->online)
			{
				continue;
			}

			if (pointIter->pointType == "control" || pointIter->pointType == "calculation")
			{
				std::cout << pointIter->pointType << std::endl;
				continue;
			}

			if (pointIter->enableAC)
			{
				if (data.value.getType() == SValue::eValueInt)
				{
					newData.value = data.value.asInt<int64_t>() * pointIter->coefficient + pointIter->added;
				}
				else if (data.value.getType() == SValue::eValueUInt)
				{
					newData.value = data.value.asInt<uint64_t>() * pointIter->coefficient + pointIter->added;
				}
				else if (data.value.getType() == SValue::eValueReal)
				{
					newData.value = data.value.asFloat<double>() * pointIter->coefficient + pointIter->added;
				}
			}

			historyDataSet_[pointIter->deviceInfo.name][pointIter->name].emplace_back(newData);
		}
	}

	void CDriverChannel::onEvent(const std::string& key, SEvent&& event)
	{
		auto pointsIter = key2PointMap_.find(key);
		if (pointsIter == key2PointMap_.end()) return;

		auto eventSptr = std::make_shared<SEvent>(event);
		for (const auto& pointIter : pointsIter->second)
		{
			if (!running_)
			{
				return;
			}

			if (cb_ && running_)
			{
				cb_->onChannelEvent(channelInfo_.name, pointIter->deviceInfo.name, eventSptr);
			}
		}
	}

	void CDriverChannel::onEventByDevice(const std::string& deviceID, SEvent&& event)
	{
		auto eventSptr = std::make_shared<SEvent>(event);

		if (cb_ && running_)
		{
			cb_->onChannelEvent(channelInfo_.name, deviceID, eventSptr);
		}
	}

	void CDriverChannel::onControlFeedback(const SControlFeedback& feedback)
	{
		if (cb_ && running_)
		{
			cb_->onChannelControlFeedback(channelInfo_.name, feedback);
		}
	}

	void CDriverChannel::setOnlineSeparate(bool separate)
	{
		onlineSeparate_ = separate;
	}

	void CDriverChannel::onPacketContentCount(const SPacketContentInfo& info)
	{
		switch (info.type)
		{
		case SPacketContentInfo::EType::Recv:
		{
			dataTrafficCount_.recv += info.size;
		}
		break;
		case SPacketContentInfo::EType::Send:
		{
			dataTrafficCount_.send += info.size;
		}
		break;
		default:
			break;
		}
		//std::cout << dataTrafficCount_.toString() << std::endl;
	}

	void CDriverChannel::onChannelControlFeedback(const std::string& channelName, const SControlFeedback& feedback)
	{
		cb_->onChannelControlFeedback(channelName, feedback);
	}

	void CDriverChannel::onChannelIEMSLedBrightness(const std::string& deviceName, int brightness)
	{
		if (cb_)
			cb_->onChannelIEMSLedBrightness(deviceName, brightness);
	}


	SData CDriverChannel::calcDataOffset(const std::shared_ptr<SPointInfo>& pointInfo, const SData& originData)
	{
		SData newData = originData;
		if (!pointInfo->type.empty())
		{
			switch (pointInfo->type[0])
			{
			case 'A':
				if (originData.value.getType() == SValue::eValueBoolean)
				{
					break;
				}
				else if (originData.value.getType() == SValue::eValueInt)
				{
					newData.value = originData.value.toInt<int64_t>();
				}
				else if (originData.value.getType() == SValue::eValueUInt)
				{
					newData.value = originData.value.toInt<uint64_t>();
				}
				else if (originData.value.getType() == SValue::eValueString)
				{
					std::string str = originData.value.asString();
					double f64 = originData.value.toFloat<double>();
					newData.qualityCodeVec.push_back(EQualityCode::typeerror);
					newData.value = f64;
				}
				else
				{
					newData.value = originData.value.toFloat<double>();
				}

				if (!newData.value.isRealValid())
				{
					newData.qualityCodeVec.push_back(EQualityCode::bad);
				}
				else
				{
					if (pointInfo->value.find("maximum") != pointInfo->value.end())
					{
						if (newData.value.toFloat() > pointInfo->maxValue)
						{
							newData.qualityCodeVec.push_back(EQualityCode::outofrange);
						}
					}
					if (pointInfo->value.find("minimum") != pointInfo->value.end())
					{
						if (newData.value.toFloat() < pointInfo->minValue)
						{
							newData.qualityCodeVec.push_back(EQualityCode::outofrange);
						}
					}
				}
				break;
			case 'E':
				if (originData.value.getType() == SValue::eValueInt)
				{
					newData.value = originData.value.asInt<int32_t>();
				}
				else if (originData.value.getType() == SValue::eValueUInt)
				{
					newData.value = originData.value.asInt<uint32_t>();
				}
				else if (originData.value.getType() == SValue::eValueString)
				{
					std::string str = originData.value.asString();
					int32_t i32 = originData.value.toFloat<int32_t>();
					newData.qualityCodeVec.push_back(EQualityCode::typeerror);
					newData.value = i32;
				}
				else
				{
					newData.value = originData.value.toFloat<int32_t>();
				}
				break;
			case 'D':
				newData.value = originData.value.toBool();
				break;
			case 'S':
				newData.value = originData.value.toString();
				break;
			default:
				break;
			}
		}

		if (pointInfo->enableAC)
		{
			if (originData.value.getType() == SValue::eValueInt)
			{
				newData.value = originData.value.asInt<int64_t>() * pointInfo->coefficient + pointInfo->added;
			}
			else if (originData.value.getType() == SValue::eValueUInt)
			{
				newData.value = originData.value.asInt<uint64_t>() * pointInfo->coefficient + pointInfo->added;
			}
			else if (originData.value.getType() == SValue::eValueReal)
			{
				newData.value = originData.value.asFloat<double>() * pointInfo->coefficient + pointInfo->added;
			}
		}

		return newData;
	}

} //namespace DRIVER end
