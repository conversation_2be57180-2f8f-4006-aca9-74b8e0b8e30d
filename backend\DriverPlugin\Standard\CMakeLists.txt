#if(WIN32)
#add_subdirectory(DriverOpcDa)
#endif()

#if (${PLATFORM} STREQUAL "Linux64")
#add_subdirectory(DriverIEC104)
#add_subdirectory(DriverSyncPlatformData_build_20240603)
#add_subdirectory(Driver808_2013)
#add_subdirectory(DriverKNX)
#add_subdirectory(DriverSNMP)
#add_subdirectory(DriverCoAP_Build202512)
#add_subdirectory(DriverSyncPlatformData_build202527)
#endif()

#add_subdirectory(DriverMQTTClient)
#add_subdirectory(DriverStandard_MQTTClient_build20240103)
#add_subdirectory(DriverBACnet)
add_subdirectory(DriverCJT188)
add_subdirectory(DriverDLT645)
#add_subdirectory(DriverRockwellPlc)
#add_subdirectory(DriverSECP)
#add_subdirectory(DriverSiemensPlc)
#add_subdirectory(DriverSimulation)
#add_subdirectory(DriverModbus)
add_subdirectory(DriverModbusV1)
#add_subdirectory(DriverModbusV2)
#add_subdirectory(DriverOpcUa)
#add_subdirectory(DriverOmRon_Fins_UDP)
#add_subdirectory(DriverXunRaoMQTT_build_20240508)

add_subdirectory(DriverEMSBoxModbus_build202528)
add_subdirectory(DriverEMSBoxDLT645_build202528)
add_subdirectory(DriverEMSBoxCJT188_build202528)