#include "PathManager.h"

#include <iostream>
#include <string>
#include <algorithm>

CPathManager::CPathManager():
	isMultiGateway_(false)
{

}


void CPathManager::initialize(const std::string& launchPath)
{
	if (launchPath.empty())
		return;

	launchPath_ = launchPath;

	if (launchPath_.back() != '/')
		launchPath_ = launchPath_ + "/";

	mainLogPath_		= launchPath_ + mainLogPath_;
	driverXConfigPath_	= launchPath_ + driverXConfigPath_;
	enginelogFilePath_	= launchPath_ + enginelogFilePath_;
	gatewayLogFilePath_ = launchPath_ + gatewayLogFilePath_;
	aiotConfigFile_		= launchPath_ + aiotConfigFile_;
	cloudConfigPath_	= launchPath_ + cloudConfigPath_;
	cloudVersionPath_	= launchPath_ + cloudVersionPath_;
	pluginPath_			= launchPath_ + pluginPath_;
	userInfoFile_		= launchPath_ + userInfoFile_;
	cacheDBPath_		= launchPath_ + cacheDBPath_;
	logDBPath_			= launchPath_ + logDBPath_;
	configurationDBPath_ = launchPath_ + configurationDBPath_;
	webPath_			= launchPath_ + webPath_;
	gatewayVersion_		= launchPath_ + gatewayVersion_;
	crtPath_			= launchPath_ + crtPath_;
	managerCrtPath_		= launchPath_ + managerCrtPath_;
	packVersionFile_	= launchPath + packVersionFile_;
	packInfoFile_		= launchPath_ + packInfoFile_;
	localDriverTemplatePath_ = launchPath_ = localDriverTemplatePath_;
	localConfigPath_    = launchPath_ + localConfigPath_;
	localConfigStatePath_ = launchPath_ + localConfigStatePath_;


	mainLogPath_ = normalizePath(mainLogPath_);
	driverXConfigPath_ = normalizePath(driverXConfigPath_);
	enginelogFilePath_ = normalizePath(enginelogFilePath_);
	gatewayLogFilePath_ = normalizePath(gatewayLogFilePath_);
	aiotConfigFile_ = normalizePath(aiotConfigFile_);
	cloudConfigPath_ = normalizePath(cloudConfigPath_);
	cloudVersionPath_ = normalizePath(cloudVersionPath_);
	pluginPath_ = normalizePath(pluginPath_);
	userInfoFile_ = normalizePath(userInfoFile_);
	cacheDBPath_ = normalizePath(cacheDBPath_);
	logDBPath_ = normalizePath(logDBPath_);
	configurationDBPath_ = normalizePath(configurationDBPath_);
	webPath_ = normalizePath(webPath_);
	gatewayVersion_ = normalizePath(gatewayVersion_);
	crtPath_ = normalizePath(crtPath_);
	managerCrtPath_ = normalizePath(managerCrtPath_);
	packVersionFile_ = normalizePath(packVersionFile_);
	packInfoFile_ = normalizePath(packInfoFile_);
	localDriverTemplatePath_ = normalizePath(localDriverTemplatePath_);
	localConfigPath_ = normalizePath(localConfigPath_);
	localConfigStatePath_ = normalizePath(localConfigStatePath_);
}

void CPathManager::initialize(const std::string& driverXId, const std::string& listenPort,
	const std::string& managerPort, const std::string& launchPath)
{
	isMultiGateway_ = true;
	driverXId_ = driverXId;
	listenPort_ = listenPort;
	managerPort_ = managerPort;
	launchPath_ = launchPath;

	if (launchPath_.back() == '/') 
	{
		launchPath_.substr(0, launchPath_.size() - 1);
	}
	//std::cout << __func__ << "   " << launchPath_ << std::endl;
	mainLogPath_		= launchPath_ + "/../logs/gateway/" + driverXId + "/main.log";
	enginelogFilePath_	= launchPath_ + "/../logs/gateway/" + driverXId_ + "/DriverEngine.log";
	gatewayLogFilePath_ = launchPath_ + "/../logs/gateway/" + driverXId_ + "/gateway.log";
	driverXConfigPath_	= launchPath_ + "/../config/gateway/" + driverXId + "/DriverXConfig.json";
	aiotConfigFile_		= launchPath_ + "/../config/gateway/" + driverXId_ + "/AIOTConfig.json";
	userInfoFile_		= launchPath_ + "/../config/gateway/" + driverXId_ + "/UserInfo.json";
	cloudConfigPath_	= launchPath_ + "/../config/gateway/" + driverXId_ + "/cloudConfig/";
	cloudVersionPath_	= launchPath_ + "/../config/gateway/" + driverXId_ + "/cloudConfig/version";
	pluginPath_			= launchPath_ + "/../plugins";
	cacheDBPath_		= launchPath_ + "/../config/gateway/" + driverXId + "/caching";
	logDBPath_			= launchPath_ + "/../config/gateway/" + driverXId + "/log";
	configurationDBPath_= launchPath_ + "/../config/gateway/" + driverXId + "/configuration";
	//webPath_			= launchPath_ + "/../" + webPath_;
	//gatewayVersion_		= launchPath_ + "/../bin/" + gatewayVersion_;
	//crtPath_			= launchPath_ + "/../bin/" + crtPath_;
	localDriverTemplatePath_ = launchPath_ + "/../bin/DriverTemplate";
	localConfigPath_    = launchPath_ + "/../config/gateway/" + driverXId + "/LocalConfig.json";
	localConfigStatePath_ = launchPath_ + "/../config/gateway/" + driverXId + "/LocalConfigState.json";


	mainLogPath_ = normalizePath(mainLogPath_);
	enginelogFilePath_ = normalizePath(enginelogFilePath_);
	gatewayLogFilePath_ = normalizePath(gatewayLogFilePath_);
	driverXConfigPath_ = normalizePath(driverXConfigPath_);
	aiotConfigFile_ = normalizePath(aiotConfigFile_);
	userInfoFile_ = normalizePath(userInfoFile_);
	cloudConfigPath_ = normalizePath(cloudConfigPath_);
	cloudVersionPath_ = normalizePath(cloudVersionPath_);
	pluginPath_ = normalizePath(pluginPath_);
	cacheDBPath_ = normalizePath(cacheDBPath_);
	logDBPath_ = normalizePath(logDBPath_);
	configurationDBPath_ = normalizePath(configurationDBPath_);
	localDriverTemplatePath_ = normalizePath(localDriverTemplatePath_);
	localConfigPath_ = normalizePath(localConfigPath_);
	localConfigStatePath_ = normalizePath(localConfigStatePath_);

}

std::string CPathManager::getMainLogPath()
{
	return mainLogPath_;
}

std::string CPathManager::getDriverXConfigPath()
{
	return driverXConfigPath_;
}

std::string CPathManager::getEngineLogPath()
{
	return enginelogFilePath_;
}

std::string CPathManager::getGatewayLogPath()
{
	return gatewayLogFilePath_;
}

std::string CPathManager::getAIOTConfigPath()
{
	return aiotConfigFile_;
}

std::string CPathManager::getCloudConfigPath()
{
	return cloudConfigPath_;
}

std::string CPathManager::getCloudVersionPath()
{
	return cloudVersionPath_;
}

std::string CPathManager::getDownloadConfigTarName(std::string gid)
{
	//std::cout << __func__ << std::endl;

	std::string ret;
	if (isMultiGateway_)
	{
		ret = launchPath_ + "/../config/gateway/" + driverXId_ + "/cloudConfig/" + gid + ".tar";
	}
	else
	{
		if (launchPath_.empty())
			ret = "cloudConfig/" + gid + ".tar";
		else
			ret = launchPath_ + "/cloudConfig/" + gid + ".tar";
	}

	ret = normalizePath(ret);

	//std::cout << __func__ << "   " << ret << std::endl;
	return ret;
}

std::string CPathManager::getPluginPath()
{
	return pluginPath_;
}

std::string CPathManager::getUserInfoFile()
{
	return userInfoFile_;
}

std::string CPathManager::getChannelLogPath(std::string channelName, std::string alias)
{
	std::string ret;
	if (isMultiGateway_)
	{
		ret = launchPath_ + "/../logs/gateway/" + driverXId_ + "/" + channelName + alias + "/";
	}
	else
	{
		if (launchPath_.empty())
			ret = "logs/" + channelName + alias + "/";
		else
			ret = launchPath_ + "/logs/" + channelName + alias + "/";
	}
	ret = normalizePath(ret);
	
	//std::cout << __func__ << "   " << ret << std::endl;
	return ret;
}

std::string CPathManager::getChannelLog(std::string channelName, std::string alias, std::string logLevel, std::string name)
{
	//std::cout << __func__ << std::endl;

	std::string ret;
	if (isMultiGateway_)
	{
		ret =  launchPath_ + "/../logs/gateway/" + driverXId_ + "/" + channelName + alias + "/" + logLevel + "/" + name;
	}
	else
	{
		if (launchPath_.empty())
			ret = "logs/" + channelName + alias + "/" + logLevel + "/" + name;
		else
			ret = launchPath_ + "/logs/" + channelName + alias + "/" + logLevel + "/" + name;
	}

	ret = normalizePath(ret);

	//std::cout << __func__ << "   " << ret << std::endl;
	return ret;
}

std::string CPathManager::getGatewayIDLogPath(std::string gid)
{
	std::string ret;
	if (isMultiGateway_)
	{
		ret = launchPath_ + "/../logs/gateway/" + driverXId_ + "/" + gid + ".log";
	}
	else
	{
		if (launchPath_.empty())
			ret = "logs/" + gid + ".log";
		else
			ret = launchPath_ + "/logs/" + gid + ".log";
	}

	ret = normalizePath(ret);

	//std::cout << __func__ << "   " << ret << std::endl;
	return ret;
}

std::string CPathManager::getCacheDBPath()
{
	return cacheDBPath_;
}

std::string CPathManager::getLogDBPath()
{
	return logDBPath_;
}

std::string CPathManager::getConfigurationDBPath()
{
	return configurationDBPath_;
}

std::string CPathManager::getWebPath()
{
	return webPath_;
}

std::string CPathManager::getAIOTFlagPath()
{
	return aiotFlagPath_;
}

std::string CPathManager::getGatewayVersion()
{
	return gatewayVersion_;
}

std::string CPathManager::getCrtPath()
{
	return crtPath_;
}

std::string CPathManager::getManagerCrtPath()
{
	return managerCrtPath_;
}

std::string CPathManager::getPackVersionFile()
{
	return packVersionFile_;
}

std::string CPathManager::getPackInfoFile()
{
	return packInfoFile_;
}

std::string CPathManager::getLocalDriverTemplatePath()
{
	return localDriverTemplatePath_;
}

std::string CPathManager::getLocalConfigPath()
{
	return localConfigPath_;
}

std::string CPathManager::getLocalConfigStatePath()
{
	return localConfigStatePath_;
}

std::string CPathManager::normalizePath(std::string path)
{
	std::string normalizePath = path;
#if defined(_MSC_VER) || defined(__MINGW32__)
	std::replace(normalizePath.begin(), normalizePath.end(), '/', '\\');
#endif // UNIX
	//std::cout << normalizePath << std::endl;
	return normalizePath;
}
