import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";

import path, { resolve } from "path";

import { createHtmlPlugin } from "vite-plugin-html";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
import vueJsx from "@vitejs/plugin-vue-jsx";
import vueSetupExtend from "vite-plugin-vue-setup-extend";

// https://vitejs.dev/config/
export default defineConfig({
	resolve: {
		alias: {
			"@": path.resolve(__dirname, "src")
		}
	},
	css: {
		preprocessorOptions: {
			scss: {
				additionalData: `@import "@/styles/var.scss";`
			}
		}
	},
	server: {
		// 服务器主机名，如果允许外部访问，可设置为 "0.0.0.0"
		// host: "0.0.0.0",
		open: false,
		cors: true,
		// 代理跨域（mock 不需要配置跨域，直接能访问，这里只是个示例）
		proxy: {
			"/supaiot": {
				target: "http://************:8899/", // easymock
				changeOrigin: true,
				rewrite: path => path.replace(/^\/supaiot/, "")
			}
		}
	},
	plugins: [
		vue(),
		createHtmlPlugin({
			minify: true,
			pages: [
				{
					filename: "index.html",
					template: "index.html",
					injectOptions: {
						data: {
							title: "网关配置"
						}
					}
				},
				{
					filename: "watchdog.html",
					template: "watchdog.html",
					injectOptions: {
						data: {
							title: "网关管理"
						}
					}
				}
			]
		}),
		// * 使用 svg 图标
		createSvgIconsPlugin({
			iconDirs: [resolve(process.cwd(), "src/assets/icons")],
			symbolId: "icon-[dir]-[name]"
		}),
		// * vite 可以使用 jsx/tsx 语法
		vueJsx(),
		// * name 可以写在 script 标签上
		vueSetupExtend()
	],
	// * 打包去除 console.log && debugger
	esbuild: {
		pure: ["console.log", "debugger"]
	},
	build: {
		outDir: "../scanner_web",
		minify: "esbuild",
		// esbuild 打包更快，但是不能去除 console.log，terser打包慢，但能去除 console.log
		// minify: "terser",
		// terserOptions: {
		// 	compress: {
		// 		drop_console: viteEnv.VITE_DROP_CONSOLE,
		// 		drop_debugger: true
		// 	}
		// },
		chunkSizeWarningLimit: 1500,
		rollupOptions: {
			output: {
				// Static resource classification and packaging
				chunkFileNames: "assets/js/[name]-[hash].js",
				entryFileNames: "assets/js/[name]-[hash].js",
				assetFileNames: "assets/[ext]/[name]-[hash].[ext]"
			}
		}
	}
});
